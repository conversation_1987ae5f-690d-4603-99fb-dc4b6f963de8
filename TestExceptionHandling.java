package net.datatp.module.asset;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import net.datatp.module.common.ClientInfo;
import net.datatp.module.communication.CommunicationMessageService;
import net.datatp.module.communication.entity.Message;
import net.datatp.module.communication.entity.TargetRecipient;
import net.datatp.module.communication.entity.MessageDeliverType;
import net.datatp.module.data.db.entity.ICompany;
import lombok.extern.slf4j.Slf4j;

/**
 * Class để test exception handling trong queue
 */
@Slf4j
@Component
public class TestExceptionHandling {
  
  @Autowired
  private CommunicationMessageService messageService;
  
  /**
   * Test các loại exception khác nhau trong queue
   */
  public void testQueueExceptionHandling(ClientInfo client, ICompany company) {
    
    // Test 1: RuntimeError
    testErrorType(client, company, "Test Error - RuntimeError", "<EMAIL>");
    
    // Test 2: Timeout
    testErrorType(client, company, "Test Timeout - Processing timeout", "<EMAIL>");
    
    // Test 3: NullPointerException  
    testErrorType(client, company, "Test Null - Null pointer", "<EMAIL>");
    
    // Test 4: IO Exception
    testErrorType(client, company, "Test IO - IO exception", "<EMAIL>");
    
    // Test 5: Normal message (should work)
    testErrorType(client, company, "Normal Message - Should work", "<EMAIL>");
  }
  
  private void testErrorType(ClientInfo client, ICompany company, String subject, String email) {
    try {
      Message message = new Message("<EMAIL>");
      message.setSubject(subject);
      message.setContent("<div>Testing exception handling in queue processing</div>");
      
      TargetRecipient recipient = new TargetRecipient(MessageDeliverType.Email, email, "Test User");
      message.withRecipient(recipient);
      
      log.info("🚀 Sending message with subject: {}", subject);
      
      // Gửi message vào queue - try-catch này SẼ KHÔNG bắt được exception từ queue
      Message sentMessage = messageService.sendMessage(client, company, message);
      
      log.info("✅ Message queued successfully with ID: {}", sentMessage.getId());
      log.info("⚠️  Note: Exception trong queue (nếu có) sẽ KHÔNG được bắt ở đây!");
      
      // Để demo, chúng ta có thể check status sau một khoảng thời gian
      checkMessageStatusLater(client, sentMessage.getId(), subject);
      
    } catch (Exception e) {
      // Exception này chỉ bắt được lỗi khi enqueue, KHÔNG phải lỗi trong queue processing
      log.error("❌ Exception khi enqueue message (NOT from queue processing): {}", e.getMessage());
    }
  }
  
  private void checkMessageStatusLater(ClientInfo client, Long messageId, String subject) {
    // Simulate checking status after some time
    new Thread(() -> {
      try {
        Thread.sleep(3000); // Wait 3 seconds
        
        // Trong thực tế, bạn sẽ query database để check status
        log.info("🔍 Checking status for message ID: {} with subject: {}", messageId, subject);
        log.info("💡 Tip: Check database hoặc implement callback để biết kết quả thực tế");
        
      } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
      }
    }).start();
  }
  
  /**
   * Demo sử dụng sendImmediatelyMessage để có thể bắt exception
   */
  public void testImmediateExceptionHandling(ClientInfo client, ICompany company) {
    try {
      Message message = new Message("<EMAIL>");
      message.setSubject("Test Error - This will throw exception immediately");
      message.setContent("<div>Testing immediate exception handling</div>");
      
      TargetRecipient recipient = new TargetRecipient(MessageDeliverType.Email, "<EMAIL>", "Test User");
      message.withRecipient(recipient);
      
      log.info("🚀 Sending message immediately (synchronous)");
      
      // Sử dụng sendImmediatelyMessage - exception SẼ được bắt ở đây
      messageService.sendImmediatelyMessage(client, company, message);
      
      log.info("✅ Message sent immediately without error");
      
    } catch (Exception e) {
      // Exception này SẼ bắt được lỗi từ processing
      log.error("❌ Exception caught from immediate processing: {}", e.getMessage(), e);
      log.info("✅ Successfully caught exception from synchronous processing!");
    }
  }
}

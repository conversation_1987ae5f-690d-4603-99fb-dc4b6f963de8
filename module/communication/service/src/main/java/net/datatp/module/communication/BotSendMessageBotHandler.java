package net.datatp.module.communication;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import net.datatp.module.communication.entity.*;
import net.datatp.module.communication.entity.Message.Status;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;
import net.datatp.module.bot.BotEvent;
import net.datatp.module.bot.BotEventHandler;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.communication.entity.TargetRecipient.DeliverStatus;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.graphapi.GraphApiService;
import net.datatp.module.monitor.SourceType;
import net.datatp.util.ds.Collections;
import net.datatp.util.error.RuntimeError;


@Component
@Slf4j
public class BotSendMessageBotHandler extends BotEventHandler {

  @Autowired
  private CommunicationMessageLogic messageLogic;

  @Autowired
  private DomainMailSender domainMailSender;

  @Autowired
  private GraphApiService graphApiService;

  @Value("${app.communication.forward-email:true}")
  private boolean forwardEmail;

  public BotSendMessageBotHandler() {
    super(BotSendMessageEvent.EVENT_NAME);
  }

  @Override
  public void handle(ClientInfo client, ICompany company, SourceType sourceType, BotEvent<?> event) {
    Message message = (Message) event.getData();
    List<TargetRecipient> targets = message.getRecipients();
    List<TargetRecipient> targetInternals = new ArrayList<>();
    List<TargetRecipient> targetChannels = new ArrayList<>();
    List<TargetRecipient> targetEmails = new ArrayList<>();

    for(TargetRecipient target : targets) {
      if(MessageDeliverType.isPrivate(target.getDeliverType())) {
        targetInternals.add(target);
      } else if (MessageDeliverType.isChannel(target.getDeliverType())) {
        targetChannels.add(target);
      } else if(MessageDeliverType.isEmail(target.getDeliverType())) {
        targetEmails.add(target);
      }
    }
    CommunicationAccount fromAccount = messageLogic.getCommunicationAccount(client, message.getSenderLoginId());
    message = handleTargetRecipients(client, fromAccount, message, targetInternals);
    message = handleTargetRecipients(client, fromAccount, message, targetChannels);

    // Tạo lỗi để test exception handling
//    throw new RuntimeError("Simulated error for testing exception handling in queue");


    message = sendEmail(client, fromAccount, message, targetEmails);

    message = messageLogic.saveMessage(client, message);
  }

  private Message handleTargetRecipients(ClientInfo client, CommunicationAccount fromAccount, Message message, List<TargetRecipient> targets) {
    int deliveredCount = 0;
    int deliveredErrorCount = 0;
    int deliveredAbortCount = 0;
    int targetRecipientCount = 0;
    for(TargetRecipient target: targets) {
      targetRecipientCount++;
      sendTo(client, fromAccount, message, target);
      DeliverStatus deliverStatus = target.getDeliverStatus();
      if(deliverStatus == DeliverStatus.Delivered) {
        deliveredCount++ ;
      } else if(deliverStatus == DeliverStatus.DeliveredWithError) {
        if(target.getDeliverCount() >= 3) {
          target.setDeliverStatus(DeliverStatus.Aborted);
          deliveredAbortCount++ ;
        } else {
          deliveredErrorCount++;
        }
      }
    }
    Message.Status status = Message.Status.PartiallyDelivered;
    if(deliveredCount == targetRecipientCount) {
      status = Message.Status.Delivered;
    } else if(deliveredCount + deliveredAbortCount == targetRecipientCount) {
      status = Message.Status.DeliveredWithError;
    }
    message.setStatus(status);

    log.info(
        "Process send message {}. delivered count = {}, delivered error count = {}, delivered abort count = {}",
        message.getSubject(), deliveredCount, deliveredErrorCount, deliveredAbortCount);
    return message;
  }

  private void sendTo(ClientInfo client, CommunicationAccount fromAccount, Message message, TargetRecipient target) {
    if(target.isDelivered()) return;
    try {
      target.incDeliverCount(1);
      if(MessageDeliverType.isChannel(target.getDeliverType())) {
        CommunicationChannel channel = messageLogic.getCommunicationChannel(client, target.getRecipientId());
        toChannel(client, fromAccount, message, target, channel);
      } else if(MessageDeliverType.isPrivate(target.getDeliverType())) {
        CommunicationAccount account = messageLogic.getCommunicationAccount(client, target.getRecipientId());
        toInternal(client, fromAccount, message, target, account);
      }
      //Email
    } catch(Throwable error) {
      log.error("Cannot send the message {} to , deliver type {}", target.getRecipientId(), target.getDeliverType());
      log.error("Root Error", error);
    }
  }

  private void toInternal(
    ClientInfo client, CommunicationAccount fromAccount, Message message, TargetRecipient target, CommunicationAccount account) {
    RecipientMessage recipient =
      new RecipientMessage(account.getLoginId(), message).withDeliverType(target.getDeliverType());
    if(target.isForwardEmail()) {
      sendEmail(client, fromAccount, message, target, account.getEmail());
    }
    target.setDeliverStatus(DeliverStatus.Delivered);
    target.setDelivered(true);
    messageLogic.saveRecipientMessage(client, recipient);
  }

  private void toChannel(
    ClientInfo client, CommunicationAccount fromAccount, Message message, TargetRecipient target, CommunicationChannel channel) {
    List<CommunicationAccount> communicationAccounts =
        messageLogic.findCommunicationAccountInChannel(client, channel.getName());
    Collections.apply(communicationAccounts, (account) -> {
      RecipientMessage recipient =
        new RecipientMessage(account.getLoginId(), message)
          .withChannelName(channel.getName());
      messageLogic.saveRecipientMessage(client, recipient);
      if(account.isAutoForward()) {
        sendEmail(client, fromAccount, message, target, account.getEmail());
      }
    });
    target.setDeliverStatus(DeliverStatus.Delivered);
    target.setDelivered(true);
  }

  private boolean sendEmail(
    ClientInfo client, CommunicationAccount fromAccount, Message message, TargetRecipient target, String email) {
    if(!forwardEmail) return false;
    try {
      message.clearError();
      if(fromAccount.getSmtpProvider() == CommunicationAccount.SmtpProvider.GraphApi) {
        messageLogic.buildMessageFileAttachments(message);
        graphApiService.sendEmail(client, null, fromAccount, message, Arrays.asList(email));
        return true;
      } else {
        return domainMailSender.sendEmail(client, fromAccount, message, target, email);
      }
    } catch (Exception e) {
      target.setDeliverStatus(DeliverStatus.Aborted);
      message.withStatus(Status.DeliveredWithError);
      message.withError("Email: "+ email+" Error:" + e.getMessage());
      return false;
    }
  }

  private Message sendEmail(
      ClientInfo client, CommunicationAccount fromAccount, Message message, List<TargetRecipient> targets) {
    if(targets.size() == 0) return message;
      List<String> emails = new ArrayList<>();
      for(TargetRecipient target : targets) {
        emails.add(target.getRecipientId());
        target.incDeliverCount(1);
      }
      try {
        messageLogic.buildMessageFileAttachments(message);
        graphApiService.sendEmail(client, null, fromAccount, message, emails);
        targets.forEach(target -> {
          target.setDeliverStatus(DeliverStatus.Delivered);
          target.setDelivered(true);
        });
        message.clearError();
      } catch (Exception e) {
        targets.forEach(target -> {
          target.setDeliverStatus(DeliverStatus.Aborted);
          target.setDelivered(false);
        });
        String error = e.getMessage();
        message.withStatus(Status.DeliveredWithError);
        message.withError(error);
        log.error("Cannot send the message {} to , deliver type {}", emails, MessageDeliverType.Email);
        log.error("Root Error", e);
      }
      return message;
    }


}
@charset "UTF-8";

/* Need to add those fonts in ITextRenderer Verdana, Times New, Roman Arial */
* {
  font-family: Verdana;
}

html,
body {
  margin: 0px;
}

table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin: 0px;
}

th, td {
  border: 0.5px solid black;
  padding: 4px;
}

@media print {
  html,
  body {
    margin: 0;
    box-shadow: 0;
  }
}

@page {
  size: A4 portrait;
  margin: 0;
}

p {
  margin: 5px 0 5px 0;
  font-size: 11px;
}

 /* review and Clean css */
.p-11px p {font-size: 11px};
.p-10px p {font-size: 10px};
.p-9px p {font-size: 9px};
.p-8px p {font-size: 8px};

.page {
  margin: 10mm 10mm 0mm 10mm;
  font-size: 11px;
}

body.page {
  margin: 10mm 10mm 0mm 10mm;
  font-size: 11px;
}

.page-new {
  page-break-before: always;
}

.p-0 { padding: 0px; }
.p-1 { padding: 5px; }
.p-2 { padding: 10px; }
.p-3 { padding: 15px; }
.px-1 { padding: 0px 5px; }
.px-2 { padding: 0px 10px; }
.px-3 { padding: 0px 15px; }
.py-1 { padding: 5px 0px; }
.py-2 { padding: 10px 0px; }
.py-3 { padding: 15px 0px; }
.pl-1 { padding-left: 5px; }
.pl-2 { padding-left: 10px; }
.pl-3 { padding-left: 15px; }
.pr-1 { padding-right: 5px; }
.pr-2 { padding-right: 10px; }
.pr-3 { padding-right: 15px; }

.m-0 { margin: 0px; }
.m-1 { margin: 5px; }
.m-2 { margin: 10px; }
.m-3 { margin: 15px; }
.mx-1 { margin: 0px 5px; }
.mx-2 { margin: 0px 10px; }
.mx-3 { margin: 0px 15px; }
.my-1 { margin: 5px 0px; }
.my-2 { margin: 10px 0px; }
.my-3 { margin: 15px 0px; }
.ml-1 { margin-left: 5px; }
.ml-2 { margin-left: 10px; }
.ml-3 { margin-left: 15px; }
.mr-1 { margin-right: 5px; }
.mr-2 { margin-right: 10px; }
.mr-3 { margin-right: 15px; }
.mb-1 { margin-bottom: 5px; }
.mb-2 { margin-bottom: 10px; }
.mb-3 { margin-bottom: 15px; }
.mt-1 { margin-top: 5px; }

.w-50px { width: 50px; }
.w-75px { width: 75px; }
.w-100px { width: 100px ; }
.w-125px { width: 125px ; }
.w-150px { width: 150px ; }
.w-200px { width: 200px ; }
.w-250px { width: 250px; }
.w-300px { width: 300px; }
.w-350px { width: 350px; }
.w-400px { width: 400px; }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-bold { font-weight: bold; }
.text-italic { font-style: italic; }
.text-warning { color: goldenrod; }
.text-danger  { color: crimson; }

.bg-gray-1 { background-color: #dfdfdf; }
.bg-gray-2 { background-color: #bdbdbd; }
.bg-gray-3 { background-color: #8f8f8f; }

.iblock-valign-middle { vertical-align: middle; }
.iblock-valign-top { vertical-align: top; }
.iblock-valign-bottom { vertical-align: bottom; }

.iblock { display: inline-block; }

.table-no-border,
.table-no-border th,
.table-no-border td  {
  border: none;
}

.border { border: 1px solid gray; }
.border-top { border-top: 1px solid gray; }
.border-bottom { border-bottom: 1px solid gray; }
.border-left { border-left: 1px solid gray; }
.border-right { border-right: 1px solid gray; }

.border-none { border: none; }
.border-top-none { border-top: none; }
.border-bottom-none { border-bottom: none; }
.border-left-none { border-left: none; }
.border-right-none { border-right: none; }
.border-x-none {
  border-top: none;
  border-bottom: none;
}
.border-y-none {
  border-left: none;
  border-right: none;
}

.font-size-normal { font-size:  1em; }
.font-size-smaller { font-size: 0.9em; }
.font-size-smallest { font-size: 0.8em; }
.font-size-larger { font-size: 1.1em; }
.font-size-largest { font-size: 1.2em; }

hr {
  border:none;
  border-top:1px dotted black;
  height:1px;
  width:100%;
  margin: 0;
}

<div style="padding-bottom: 5px;">
  <table>
    {{#each itemsMap}}
      <tr class="mt-2">
        <th colspan="2" class="text-center">No.</th>
        <th colspan="9" class="text-center">Description</th>
        <th colspan="6" class="text-center">Q'ty | (Unit)</th>
        <th colspan="5" class="text-center">Price</th>
        <th colspan="3" class="text-center">VAT %</th>
        <th colspan="2" class="text-center">Curr.</th>
        <th colspan="7" class="text-center">Debit</th>
        <th colspan="7" class="text-center">Credit</th>
      </tr>
      {{>items chargeType='FreightCharge' hbl=@key name='Freight Charge'}}
      {{>items chargeType='OtherCharge' name='Other Charge'}}
      {{>items chargeType='PayOnBehalf' name='Pay On Behalf'}}
      <div class="my-1"></div>
    {{/each}}
    <div class="my-1"></div>
    <tr>
      <td colspan="27" class="text-right">TOTAL</td>
        <td colspan="7" class="text-right">
          {{#each debitAmounts}}
            <p>
              {{#text "eq" currency "VND"}}
                {{numberFormat totalAmount "integer"}}
              {{else}}
                {{numberFormat totalAmount minimumFractionDigits=2}}
              {{/text}}
              ({{currency}})
            </p>
          {{/each}}
        </td>
        <td colspan="7" class="text-right">
          {{#each creditAmounts}}
            <p>
              {{#text "eq" currency "VND"}}
                {{numberFormat totalAmount "integer"}}
                {{else}}
                {{numberFormat totalAmount minimumFractionDigits=2}}
              {{/text}}
              ({{currency}})
            </p>
          {{/each}}
        </td>
    </tr>
  </table>

  {{#if isIssued}}
    <table class="mt-1">
      <tr>
        <td colspan="27" class="text-left border-right-none">BALANCE DUE: {{partnerFullName}}</td>
        <td colspan="7" class="text-right">
          {{#each debitAmounts}}
            <p>
              {{#text "eq" currency "VND"}}
                {{numberFormat totalAmount "integer"}}
              {{else}}
                {{numberFormat totalAmount minimumFractionDigits=2}}
              {{/text}}
              ({{currency}})
            </p>
          {{/each}}
        </td>
        <td colspan="7" class="text-right">
          {{#each creditAmounts}}
            <p>
              {{#text "eq" currency "VND"}}
                {{numberFormat totalAmount "integer"}}
                {{else}}
                {{numberFormat totalAmount minimumFractionDigits=2}}
              {{/text}}
              ({{currency}})
            </p>
          {{/each}}
        </td>
      </tr>
    </table>
  {{/if}}
</div>

{{#*inline "items" chargeType name}}
  {{#list 'has' this 'type' chargeType }}
    <tr>
      <td
        colspan="41">+ {{name}}
        {{#text 'eq' itemsMap.size '1'}}
        {{else}}
          {{#if hbl}}
            ({{hbl}})
          {{/if}}
        {{/text}}
      </td>
    </tr>
  {{/list}}
    {{#each this}}
      {{#text 'eq' type chargeType}}
        <tr>
          <td colspan="2" class="text-center">{{math 'inc' @index}}</td>
          <td colspan="9" class="pl-2"> {{label}} </td>
          <td colspan="6" class="text-right">{{numberFormat quantity minimumFractionDigits=3}} {{unit}}</td>
          <td colspan="5" class="text-right">
            {{#text "eq" currency "VND"}}
              {{numberFormat unitPrice "integer"}}
            {{else}}
              {{numberFormat unitPrice minimumFractionDigits=2}}
            {{/text}}
          </td>
          <td colspan="3" class="text-right">
            {{#text 'eq' taxRate '0.0'}}
            {{else}}
              {{cut (text "ft:percent" taxRate) "%"}}
            {{/text}}
          </td>
          <td colspan="2" class="text-right">{{currency}}</td>
            <td colspan="7" class="text-right">
              {{#text "eq" debit "0.0"}}
                {{else}}
                {{#text "eq" currency "VND"}}
                  {{numberFormat debit "integer"}}
                {{else}}
                  {{numberFormat debit minimumFractionDigits=2}}
                {{/text}}
              {{/text}}
            </td>
            <td colspan="7" class="text-right">
              {{#text "eq" credit "0.0"}}
                {{else}}
                {{#text "eq" currency "VND"}}
                  {{numberFormat credit "integer"}}
                {{else}}
                  {{numberFormat credit minimumFractionDigits=2}}
                {{/text}}
              {{/text}}
            </td>
        </tr>
      {{/text}}
    {{/each}}
{{/inline}}
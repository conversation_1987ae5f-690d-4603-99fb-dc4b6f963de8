<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
  <meta http-equiv='Content-Type' content='text/xhtml; charset=UTF-8' />
  <title>{{label}}</title>

  <style>
    {{>db:print:css/A4/A4.css}}
  </style>
</head>

<body class="page" style="margin-top: 10px; line-height: 1.6">
  {{!-- banner --}}
  <div style="margin-bottom: 10px">
    <div class="iblock" style="margin-bottom: 10px">
      <img class="iblock iblock-valign-middle" src="{{resStorage}}/images/logos/logo-main.png" width="160px" />
      <div class="iblock iblock-valign-middle">
        <div class="font-size-largest text-bold">CÔNG TY TNHH LOGISTICS HIGHPASS VIỆT NAM</div>
        <div>Số 175-176 B<PERSON><PERSON> Kiề<PERSON> 2, <PERSON><PERSON><PERSON><PERSON>ả<PERSON> 2, <PERSON><PERSON><PERSON><PERSON>, <PERSON>h<PERSON><PERSON>h<PERSON>, Việ<PERSON> Nam</div>
        <div>
          <div class="iblock w-300px">Tel:</div>
          <div class="iblock">Email: <EMAIL></div>
        </div>
        <div>
          <div class="iblock w-300px">MST (Tax Code): **********</div>
        </div>
        <div style="margin-top: 10px;">Thông tin ngân hàng (Bank details): **************(VND)/ **************(USD)</div>
        <div>Tại ngân hàng TMCP đầu tư và phát triển Việt Nam - Chi nhánh Đông Hải Phòng</div>
      </div>
    </div>
    <hr style="border-top: solid" />
  </div>

  <div class="text-bold" style="margin-left: auto; margin-right: 0; width: 150px;">
    <div>Ref No. : {{refNo}}</div>
    <div>Date : {{dateFormat date format="dd/MM/yyyy"}}</div>
  </div>

  <div class="text-bold" style="text-align: center; line-height: 1.3; margin: 10px;">
    <div style="font-size: 21px;">
      STATEMENT OF ACCOUNT
    </div>
    <div>
      (From {{dateFormat fromDate format="MMM dd, yyyy"}} to {{dateFormat toDate format="MMM dd, yyyy"}})
    </div>
  </div>

  <div style="font-size: 10px; line-height: 1em; border: 1px solid; width: 60%; margin: 10px 0px; padding: 5px 0px;">
    <table>
      <tr>
        <td colspan="1" class="border-none pl-2 text-bold">To:</td>
        <td colspan="14" class="border-none pr-2 text-bold">{{partnerFullName}}</td>
      </tr>
      <tr>
        <td colspan="1" class="border-none"></td>
        <td colspan="14" class="border-none pr-2">{{partnerAddress}}</td>
      </tr>
      <tr>
        <td colspan="1" class="border-none pl-2 text-bold">Tel:</td>
        <td colspan="7" class="border-none">{{partnerTel}}</td>
        <td colspan="7" class="border-none pl-2 text-bold">{{personInCharge}}</td>
      </tr>
    </table>
  </div>

  <div style='font-size: 7px; margin: 20px 0px;'>
    <table>
      <tr class="mt-2" style="font-size: 10px;">
        <th colspan="1" rowspan="2" class="text-center">No.</th>
        <th colspan="4" rowspan="2" class="text-center">Invoice No.</th>
        <th colspan="2" rowspan="2" class="text-center">Issued Date</th>
        <th colspan="2" rowspan="2" class="text-center">ETD</th>
        <th colspan="2" rowspan="2" class="text-center">ETA</th>
        <th colspan="4" rowspan="2" class="text-center">Job No.</th>
        <th colspan="4" rowspan="2" class="text-center">MAWB/HAWB</th>
        <th colspan="4" class="text-center">Amount</th>
      </tr>
      <tr class="mt-2">
        <th colspan="2" class="text-center">Debit</th>
        <th colspan="2" class="text-center">Credit</th>
      </tr>

      {{#each records}}
        {{~#text 'eq' currency "USD"}}
          <tr class="mt-2" style="page-break-inside: avoid; white-space: nowrap">
            <td colspan="1" class="text-center">{{math 'inc' @index}}</td>
            <td colspan="4" class="text-left">{{invoice_no}}</td>
            <td colspan="2" class="text-center">{{issued_date}}</td>
            <td colspan="2" class="text-center">{{other_info_etd}}</td>
            <td colspan="2" class="text-center">{{other_info_eta}}</td>
            <td colspan="4" class="text-left">{{case_reference}}</td>
            <td colspan="4" class="text-left" style="width: 100px; word-wrap:break-word">
              <div style="word-wrap: break-word; word-break: break-all; white-space: normal;">M: {{mawb_no}}</div>
              <div style="word-wrap: break-word; word-break: break-all; white-space: normal;">H: {{hawb_no}}</div>
            </td>
            <td colspan="2" class="text-right">{{#if debit}}{{numberFormat debit maximumFractionDigits=2}}{{/if}}</td>
            <td colspan="2" class="text-right">{{#if credit}}{{numberFormat credit maximumFractionDigits=2}}{{/if}}</td>
          </tr>
        {{~/text}}
      {{/each}}

      {{#if totalDebitUSD}}
        <tr class="mt-2" style="page-break-inside: avoid; white-space: nowrap">
          <td colspan="19" class="text-center text-bold">Total Amount (USD)</td>
          <td colspan="2" class="text-right text-bold">{{#if totalDebitUSD}}{{numberFormat totalDebitUSD maximumFractionDigits=2}}{{/if}}</td>
          <td colspan="2" class="text-right text-bold">{{#if totalCreditUSD}}{{numberFormat totalCreditUSD maximumFractionDigits=2}}{{/if}}</td>
        </tr>
      {{~else}}
        {{#if totalCreditUSD}}
          <tr class="mt-2" style="page-break-inside: avoid; white-space: nowrap">
            <td colspan="19" class="text-center text-bold">Total Amount (USD)</td>
            <td colspan="2" class="text-right text-bold">{{#if totalDebitUSD}}{{numberFormat totalDebitUSD maximumFractionDigits=2}}{{/if}}</td>
            <td colspan="2" class="text-right text-bold">{{#if totalCreditUSD}}{{numberFormat totalCreditUSD maximumFractionDigits=2}}{{/if}}</td>
          </tr>
        {{/if}}
      {{/if}}


      {{#each records}}
        {{~#text 'eq' currency "VND"}}
          <tr class="mt-2" style="page-break-inside: avoid; white-space: nowrap">
            <td colspan="1" class="text-center">{{math 'inc' @index}}</td>
            <td colspan="4" class="text-left">{{invoice_no}}</td>
            <td colspan="2" class="text-center">{{issued_date}}</td>
            <td colspan="2" class="text-center">{{other_info_etd}}</td>
            <td colspan="2" class="text-center">{{other_info_eta}}</td>
            <td colspan="4" class="text-left">{{case_reference}}</td>
            <td colspan="4" class="text-left">
              <div style="word-wrap: break-word; word-break: break-all; white-space: normal;">M: {{mawb_no}}</div>
              <div style="word-wrap: break-word; word-break: break-all; white-space: normal;">H: {{hawb_no}}</div>
            </td>
            <td colspan="2" class="text-right">{{#if debit}}{{numberFormat debit "integer"}}{{/if}}</td>
            <td colspan="2" class="text-right">{{#if credit}}{{numberFormat credit "integer"}}{{/if}}</td>
          </tr>
        {{~/text}}
      {{/each}}

      {{#if totalDebitVND}}
        <tr class="mt-2" style="page-break-inside: avoid; white-space: nowrap">
          <td colspan="19" class="text-center text-bold">Total Amount (VND)</td>
          <td colspan="2" class="text-right text-bold">{{#if totalDebitVND}}{{numberFormat totalDebitVND "integer"}}{{/if}}</td>
          <td colspan="2" class="text-right text-bold">{{#if totalCreditVND}}{{numberFormat totalCreditVND "integer"}}{{/if}}</td>
        </tr>
      {{~else}}
        {{#if totalCreditVND}}
          <tr class="mt-2" style="page-break-inside: avoid; white-space: nowrap">
            <td colspan="19" class="text-center text-bold">Total Amount (VND)</td>
            <td colspan="2" class="text-right text-bold">{{#if totalDebitVND}}{{numberFormat totalDebitVND "integer"}}{{/if}}</td>
            <td colspan="2" class="text-right text-bold">{{#if totalCreditVND}}{{numberFormat totalCreditVND "integer"}}{{/if}}</td>
          </tr>
        {{/if}}
      {{/if}}
    </table>
  </div>

  <div class="py-1" style="display: table; page-break-inside: avoid; padding-bottom: 40px;">
    <div class="mt-1" style="float: left; border: 1px solid; width: 65%; margin-right: 20px;">
      <table>
        <tr>
          <td colspan="21" class="border-none">BANKING DETAILS:</td>
        </tr>
        <tr style="vertical-align: top">
          <td colspan="5" class="border-none text-italic">ACCOUNT NAME</td>
          <td colspan="1" class="border-none" style="text-align: right; padding-right: 0"> :</td>
          <td colspan="15" class="border-none text-bold">
            {{companyBankAccountName}}
          </td>
        </tr>
        <tr style="vertical-align: top">
          <td colspan="5" class="border-none text-italic">BANK NAME</td>
          <td colspan="1" class="border-none" style="text-align: right; padding-right: 0"> :</td>
          <td colspan="15" class="border-none">
            {{companyBankName}}
          </td>
        </tr>
        <tr style="vertical-align: top">
          <td colspan="5" class="border-none text-italic">BANK ADDRESS</td>
          <td colspan="1" class="border-none" style="text-align: right; padding-right: 0"> :</td>
          <td colspan="15" class="border-none">
            {{companyBankAddress}}
          </td>
        </tr>
        <tr style="vertical-align: top">
          <td colspan="5" class="border-none text-italic">SWIFT CODE</td>
          <td colspan="1" class="border-none" style="text-align: right; padding-right: 0"> :</td>
          <td colspan="15" class="border-none">
            {{companyBankSwiftCode}}
          </td>
        </tr>
        <tr style="vertical-align: top">
          <td colspan="5" class="border-none text-italic">ACCOUNT NO</td>
          <td colspan="1" class="border-none" style="text-align: right; padding-right: 0"> :</td>
          <td colspan="15" class="border-none">
            {{companyBankAccountNo}}
          </td>
        </tr>
      </table>
    </div>
    <div class="mt-1">
      <div style="word-wrap: break-word;">
        <span>
          <p style="text-decoration: underline; font-size: x-small;">
            <span class="text-bold">For and on behalf of</span> {{companyName}}
          </p>
        </span>
      </div>
      <div style="margin-top: 60px;">
        <div style="text-decoration: underline; font-size: x-small;">Date</div>
        <div style="text-decoration: underline; font-size: x-small;">Authorized signature</div>
      </div>
    </div>
  </div>

</body>

</html>
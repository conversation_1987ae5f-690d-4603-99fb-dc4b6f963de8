package chatbot.intent.sql;

def query = """
    SELECT 
      id, 
      created_by,
      created_time, 
      modified_by, 
      modified_time, 
      storage_state, 
      version, 
      intent_id, 
      language, 
      example
    FROM chatbot_intent_example
    WHERE 
      storage_state = COALESCE(:storage_state, storage_state) AND 
      example LIKE :filter_val AND
      --AND language = :language
      intent_id = :intent_id
    ORDER BY modified_time DESC
    LIMIT :max_return
"""

return query;
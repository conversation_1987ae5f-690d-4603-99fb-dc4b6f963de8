package chatbot.intent.sql;

def query = """
    SELECT 
      id, 
      created_by, 
      created_time, 
      modified_by, 
      modified_time, 
      storage_state, 
      version, 
      label, 
      type,
      name,
      domain, 
      category
    FROM chatbot_intent
    WHERE 
      storage_state = COALESCE(:storage_state, storage_state) AND 
      (name LIKE :filter_val OR label LIKE :filter_val)
    ORDER BY domain, category, name ASC, modified_time DESC
    LIMIT :max_return
"""

return query;
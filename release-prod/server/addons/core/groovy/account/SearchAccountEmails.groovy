package account;

def buildQuery() {
  return """
    SELECT 
      aa.full_name                  AS full_name, 
      aa.login_id                   AS login_id,
      aa.email                      AS email
    FROM account_account aa
    WHERE aa.account_type = :account_type
      AND (COALESCE(:login_id, '') = i'' OR aa.login_id = :login_id)
      AND aa.storage_state = 'ACTIVE'
      AND aa.email IS NOT NULL
      AND aa.email <> ''
  """;
}

buildQuery();
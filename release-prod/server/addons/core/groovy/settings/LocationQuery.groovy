package settings;

/**
 * Search Locations
 * @return Location with Alias, type (sea, air, truck, ...) or locations by all types
 */

static def buildQuery() {
  return """
    SELECT 
      loc.id                        AS id, 
      loc.code                      AS code, 
      loc.short_label               AS short_label, 
      loc.label                     AS label, 
      loc.address                   AS address, 
      
      loc.subdistrict_id            AS subdistrict_id, 
      loc.subdistrict_label         AS subdistrict_label, 
      loc.district_id               AS district_id, 
      loc.district_label            AS district_label, 
      loc.state_id                  AS state_id, 
      loc.state_label               AS state_label, 
      loc.country_id                AS country_id, 
      loc.country_label             AS country_label, 
      
      loc.postal_code               AS postal_code, 
      loc.location_type             AS location_type, 
      loc.created_by                AS created_by,
      loc.created_time              AS created_time,
      loc.modified_by               AS modified_by,
      loc.modified_time             AS modified_time,                            
      loc.version                   AS version,                            
      loc.storage_state             AS storage_state
    FROM settings_location loc
    WHERE loc.storage_state = COALESCE(:storage_state, loc.storage_state)
      AND (:all_type IS TRUE OR loc.location_type IN (:location_types))
      AND (loc.country_id IS NULL OR loc.country_id = COALESCE(:country_id, loc.country_id))
      AND (
        (length(:org_filter_val) IN (3, 5) 
        AND (
          lower(loc.iata_code) = lower(:org_filter_val)
          OR lower(loc.un_locode) = lower(:org_filter_val)
          OR lower(loc.code) = lower(:org_filter_val)
        ))
        OR LOWER(REGEXP_REPLACE(loc.short_label, '\\s+', '', 'g')) LIKE '%' || LOWER(:filter_val_prefix)
        OR lower(loc.code) LIKE lower(:filter_val_prefix)
      )
      AND (
        COALESCE(:tags, '') = '' OR 
        -- use EXISTS pattern instead of JOIN (see the note above)
        EXISTS (
              SELECT 1 
              FROM settings_location_tag_rel rel 
              JOIN settings_location_tag loc_tag 
                      ON loc_tag.id = rel.location_tag_id 
                      AND loc_tag.type IN (:tags)
              WHERE rel.location_id = loc.id)) 
    ORDER BY 
      CASE 
        WHEN length(:org_filter_val) IN (3, 5) THEN 
          CASE 
            WHEN lower(loc.iata_code) = lower(:org_filter_val) THEN 1
            WHEN lower(loc.un_locode) = lower(:org_filter_val) THEN 2
            WHEN lower(loc.code) = lower(:org_filter_val) THEN 3
            ELSE 4
          END
        ELSE 5
      END,
      -- Ưu tiên location_type là Country
      CASE WHEN loc.location_type = 'Country' THEN 1 ELSE 2 END,
      -- Ưu tiên country_label là 'VIETNAM'
      CASE WHEN lower(loc.country_label) = 'vietnam' THEN 1 ELSE 2 END,
      loc.short_label ASC  
    LIMIT :max_return
  """;
}

buildQuery();
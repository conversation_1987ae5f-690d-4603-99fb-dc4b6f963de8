package partner

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject

public class PartnerSql extends Executor {
  static public class SearchPartners extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams  = ctx.getParam("sqlParams");
      int maxReturn        = sqlParams.getInteger('maxReturn', 1000);

      String storageState  = sqlParams.getString("storageState", "ACTIVE");
      storageState         = """'${storageState}'""";
      String groupName     = sqlParams.getString("groupName", null);
      groupName            = groupName != null ? """'${groupName.toLowerCase()}'""" : null;

      String query = """
      SELECT
      gr."label"                AS group_label,
      aa.id                     AS account_id,
      aa.login_id               AS login_id,
      aa.email                  AS email,
      aa.mobile                 AS mobile,
      aa.account_type           AS account_type,
      p.legacy_company_login_id AS legacy_company_login_id,
      p.id                      AS id,
      p."name"                  AS name,
      p."label"                 AS label,
      p.shareable               AS shareable
    FROM partner p 
      JOIN account_account aa ON aa.id = p.account_id 
      LEFT JOIN partner_partner_category_rel group_ref 
        ON group_ref.partner_id = p.id 
      LEFT JOIN partner_group gr 
        ON gr.id = group_ref.partner_group_id 
    WHERE
      p.company_id = :companyId AND
      ${FILTER_BY('p.storage_state', storageState)}
      ${AND_SEARCH_BY_PARAMS(['aa.login_id', 'p.legacy_company_login_id', 'p.name', ' p.label'], 'search', sqlParams)}
      ${AND_FILTER_BY('gr.name', groupName)}
    ORDER BY gr."label", aa.account_type, p."name"
    LIMIT ${maxReturn}
    """
      return query;
    }
  }
  
  public PartnerSql() {
    register(new SearchPartners());
  }
}
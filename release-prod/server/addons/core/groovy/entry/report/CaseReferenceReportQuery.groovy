package entry.report;

/**
 * Build an SQL query to calculate revenue, expenses, and profit based on a case reference from
 * accounting_journal_entry_line table.
 *
 * @param {@code domain} The conditions, filters of the query.
 * @return A string containing the SQL query.
 */

def buildQuery() {
  return """
      SELECT 
          li.case_reference as case_reference,
          -- Calculate the total revenue based on the 'domestic_total_amount' column for entries that are 'out_invoice
          SUM(
              CASE
                WHEN li.type = 'out_invoice' THEN li.domestic_total_amount ELSE 0
              END
          ) AS revenue,
          -- Calculate the total costing based on the 'domestic_total_amount' column for entries that are 'in_invoice
          SUM(
              CASE
                WHEN li.type = 'in_invoice' THEN li.domestic_total_amount ELSE 0
              END
          ) AS costing,
          -- Calculate the total profit based on the 'domestic_total_amount' column.
          -- gross_profit = revenue - costing
          SUM(
              CASE
                  WHEN li.type = 'out_invoice' THEN li.domestic_total_amount
                  WHEN li.type = 'in_invoice' THEN -li.domestic_total_amount
                  ELSE 0
              END
          ) AS gross_profit
      FROM accounting_journal_entry_line as li
          INNER JOIN accounting_journal_entry en on en.id = li.entry_id 
      WHERE li.storage_state = 'ACTIVE' and en.state = 'posted'
              AND en.company_id = :company_id
              ${domain}
      GROUP BY li.case_reference
      -- Only retrieve rows of data when either revenue or costing is not zero.
      HAVING SUM(
                CASE
                    WHEN li.type = 'out_invoice' THEN li.domestic_total_amount ELSE 0
                END
            ) != 0 OR 
            SUM(
                CASE
                    WHEN li.type = 'in_invoice' THEN li.domestic_total_amount ELSE 0
                END
            ) != 0
      ORDER BY li.case_reference asc
      """
}

buildQuery()
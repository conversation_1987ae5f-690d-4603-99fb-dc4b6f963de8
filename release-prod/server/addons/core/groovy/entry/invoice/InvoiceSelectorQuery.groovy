package entry.invoice;

/**
 * Build an SQL query to a list of invoices containing items
 * that haven't been created as entries to push through the accounting software from the accounting_invoice table.
 *
 * @param {@code domain} The conditions, filters of the query.
 * @return A string containing the SQL query.
 */

def buildQuery() {
  return """
      SELECT 
          ai.id as id, 
          ai.code as code, 
          ai.cash_flow as cash_flow,
          ai.issued_date as issued_date,
          ai.note as note,
          ai.case_reference_type,
          ai.case_reference,
          ai.internal_case_reference,
          null as hawb_no,
          ai.partner_id,
          ai.partner_label,
          ai.payer_partner_id,
          ai.payer_partner_name,
          ai.payee_partner_id,
          ai.payee_partner_name,
          ai.assignee_employee_id,
          ai.assignee_label,
          ai.total,
          ai.total_tax,
          ai.final_charge,
          ai.currency,
          item.item_count as possible_item_register,
          ai.created_by,
          ai.created_time,
          ai.modified_by,
          ai.modified_time
      FROM accounting_invoice ai 
          -- Filter only invoice that haven't been created as entries
          INNER JOIN (
              SELECT 
                  aii.invoice_id,
                  count(*) as item_count
              FROM accounting_invoice_item aii 
              WHERE aii.journal_entry_no is null
              GROUP BY aii.invoice_id
          ) as item ON item.invoice_id = ai.id 
          WHERE company_id = :company_id
          ${domain} 
      ORDER BY case_reference, cash_flow, partner_label, currency, final_charge ASC
      """
}

buildQuery()
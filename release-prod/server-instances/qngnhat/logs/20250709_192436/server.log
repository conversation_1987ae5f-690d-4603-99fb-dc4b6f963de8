2025-07-09T19:24:37.307+07:00  INFO 39434 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 39434 (/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-prod/server)
2025-07-09T19:24:37.308+07:00  INFO 39434 --- [main] net.datatp.server.ServerApp              : The following 8 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-console", "database", "db-schema-validate", "data"
2025-07-09T19:24:38.073+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.178+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 101 ms. Found 22 JPA repository interfaces.
2025-07-09T19:24:38.187+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.188+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-09T19:24:38.188+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.195+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-07-09T19:24:38.196+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.198+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-09T19:24:38.199+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.203+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-09T19:24:38.213+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.218+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-07-09T19:24:38.227+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.232+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-07-09T19:24:38.235+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.238+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-09T19:24:38.238+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.238+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-09T19:24:38.242+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.248+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-09T19:24:38.253+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.255+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-09T19:24:38.256+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.260+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-09T19:24:38.261+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.268+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-07-09T19:24:38.268+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.271+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-07-09T19:24:38.272+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.272+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-09T19:24:38.272+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.273+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-09T19:24:38.273+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.278+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-07-09T19:24:38.278+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.280+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-09T19:24:38.280+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.280+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-09T19:24:38.280+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.290+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-07-09T19:24:38.300+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.306+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-07-09T19:24:38.307+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.309+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-09T19:24:38.309+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.313+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-07-09T19:24:38.313+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.319+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-09T19:24:38.319+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.323+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-07-09T19:24:38.323+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.326+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-09T19:24:38.326+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.335+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-07-09T19:24:38.335+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.350+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 25 JPA repository interfaces.
2025-07-09T19:24:38.363+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.374+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 19 JPA repository interfaces.
2025-07-09T19:24:38.374+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.378+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-09T19:24:38.378+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.380+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-09T19:24:38.384+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.385+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-09T19:24:38.385+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.392+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-07-09T19:24:38.396+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.433+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 67 JPA repository interfaces.
2025-07-09T19:24:38.433+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.434+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-09T19:24:38.434+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:24:38.437+07:00  INFO 39434 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-09T19:24:38.629+07:00  INFO 39434 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-09T19:24:38.633+07:00  INFO 39434 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-09T19:24:38.903+07:00  WARN 39434 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-09T19:24:39.095+07:00  INFO 39434 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-07-09T19:24:39.097+07:00  INFO 39434 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-07-09T19:24:39.109+07:00  INFO 39434 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-07-09T19:24:39.110+07:00  INFO 39434 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1636 ms
2025-07-09T19:24:39.160+07:00  WARN 39434 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-09T19:24:39.160+07:00  INFO 39434 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-07-09T19:24:39.257+07:00  INFO 39434 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@64f3f849
2025-07-09T19:24:39.259+07:00  INFO 39434 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-07-09T19:24:39.264+07:00  WARN 39434 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-09T19:24:39.264+07:00  INFO 39434 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-07-09T19:24:39.270+07:00  INFO 39434 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@334d96af
2025-07-09T19:24:39.270+07:00  INFO 39434 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-07-09T19:24:39.270+07:00  WARN 39434 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-09T19:24:39.270+07:00  INFO 39434 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-09T19:24:39.719+07:00  INFO 39434 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@d200d3e
2025-07-09T19:24:39.719+07:00  INFO 39434 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-09T19:24:39.719+07:00  WARN 39434 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-09T19:24:39.720+07:00  INFO 39434 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-07-09T19:24:39.755+07:00  INFO 39434 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@5b7b5484
2025-07-09T19:24:39.755+07:00  INFO 39434 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-07-09T19:24:39.755+07:00  INFO 39434 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************'
2025-07-09T19:24:39.803+07:00  INFO 39434 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-07-09T19:24:39.805+07:00  INFO 39434 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@ba65354{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.6000333202881885764/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@5af850f1{STARTED}}
2025-07-09T19:24:39.806+07:00  INFO 39434 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@ba65354{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.6000333202881885764/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@5af850f1{STARTED}}
2025-07-09T19:24:39.807+07:00  INFO 39434 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@9124278{STARTING}[12.0.15,sto=0] @2990ms
2025-07-09T19:24:39.879+07:00  INFO 39434 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09T19:24:39.913+07:00  INFO 39434 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-09T19:24:39.929+07:00  INFO 39434 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-09T19:24:40.061+07:00  INFO 39434 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-09T19:24:40.118+07:00  WARN 39434 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-09T19:24:40.722+07:00  INFO 39434 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-09T19:24:40.729+07:00  INFO 39434 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6a355f4e] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-09T19:24:40.761+07:00  INFO 39434 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09T19:24:40.949+07:00  INFO 39434 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages: 
 [ "net.datatp.module.project", "cloud.datatp.fforwarder.mgmt", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "cloud.datatp.fforwarder.price", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "cloud.datatp.fforwarder.sales", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-07-09T19:24:40.952+07:00  INFO 39434 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "validate",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-07-09T19:24:40.960+07:00  INFO 39434 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09T19:24:40.961+07:00  INFO 39434 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-09T19:24:40.992+07:00  INFO 39434 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-09T19:24:41.000+07:00  WARN 39434 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-09T19:24:43.753+07:00  INFO 39434 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-09T19:24:43.754+07:00  INFO 39434 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@11a1a942] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-09T19:24:43.882+07:00  INFO 39434 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09T19:24:43.919+07:00  INFO 39434 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-07-09T19:24:43.923+07:00  INFO 39434 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-07-09T19:24:43.923+07:00  INFO 39434 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-09T19:24:43.931+07:00  WARN 39434 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-09T19:24:44.074+07:00  INFO 39434 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-09T19:24:44.533+07:00  INFO 39434 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-09T19:24:44.536+07:00  INFO 39434 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-09T19:24:44.573+07:00  INFO 39434 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-09T19:24:44.616+07:00  INFO 39434 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-07-09T19:24:44.629+07:00  INFO 39434 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-09T19:24:44.656+07:00  INFO 39434 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-09T19:24:44.687+07:00  INFO 39434 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 15900234ms : this is harmless.
2025-07-09T19:24:44.697+07:00  INFO 39434 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-07-09T19:24:44.700+07:00  INFO 39434 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-09T19:24:44.711+07:00  INFO 39434 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 97640123ms : this is harmless.
2025-07-09T19:24:44.713+07:00  INFO 39434 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-07-09T19:24:44.726+07:00  INFO 39434 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-07-09T19:24:44.728+07:00  INFO 39434 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-07-09T19:24:47.561+07:00  INFO 39434 --- [main] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 09/07/2025@19:15:00+0700 to 09/07/2025@19:30:00+0700
2025-07-09T19:24:47.561+07:00  INFO 39434 --- [main] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 09/07/2025@19:15:00+0700 to 09/07/2025@19:30:00+0700
2025-07-09T19:24:48.197+07:00  INFO 39434 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-07-09T19:24:48.197+07:00  INFO 39434 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-09T19:24:48.198+07:00  WARN 39434 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-09T19:24:48.633+07:00  INFO 39434 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-07-09T19:24:48.633+07:00  INFO 39434 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/templates
2025-07-09T19:24:48.633+07:00  INFO 39434 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/datatp-crm/templates
2025-07-09T19:24:48.633+07:00  INFO 39434 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/logistics/templates
2025-07-09T19:24:48.633+07:00  INFO 39434 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/document-ie/templates
2025-07-09T19:24:50.148+07:00  WARN 39434 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: f8e49dc7-656b-489d-95b2-f6375ff64b24

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-09T19:24:50.152+07:00  INFO 39434 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-09T19:24:50.472+07:00  INFO 39434 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-09T19:24:50.472+07:00  INFO 39434 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-07-09T19:24:50.472+07:00  INFO 39434 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-07-09T19:24:50.472+07:00  INFO 39434 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-09T19:24:50.472+07:00  INFO 39434 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-07-09T19:24:50.472+07:00  INFO 39434 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-07-09T19:24:50.472+07:00  INFO 39434 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-09T19:24:50.472+07:00  INFO 39434 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-07-09T19:24:50.472+07:00  INFO 39434 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-07-09T19:24:50.472+07:00  INFO 39434 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-09T19:24:50.472+07:00  INFO 39434 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-07-09T19:24:50.472+07:00  INFO 39434 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-07-09T19:24:50.476+07:00  INFO 39434 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-09T19:24:50.476+07:00  INFO 39434 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-09T19:24:50.476+07:00  INFO 39434 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-09T19:24:50.490+07:00  INFO 39434 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09T19:24:50.490+07:00  INFO 39434 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-09T19:24:50.494+07:00  INFO 39434 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
2025-07-09T19:24:50.503+07:00  INFO 39434 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@1c6057a7{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-09T19:24:50.503+07:00  INFO 39434 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-07-09T19:24:50.504+07:00  INFO 39434 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-07-09T19:24:50.534+07:00  INFO 39434 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-07-09T19:24:50.535+07:00  INFO 39434 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-07-09T19:24:50.540+07:00  INFO 39434 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.551 seconds (process running for 13.722)
2025-07-09T19:24:58.002+07:00  INFO 39434 --- [qtp1558550579-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01v846er12ol261829jzpqjeilo1
2025-07-09T19:24:58.002+07:00  INFO 39434 --- [qtp1558550579-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01m74g379p3swa1v3m32tizd78b0
2025-07-09T19:24:58.273+07:00  INFO 39434 --- [qtp1558550579-37] n.d.module.session.ClientSessionManager  : Add a client session id = node01v846er12ol261829jzpqjeilo1, token = df113b69017c849e9585cf6f67c89dda
2025-07-09T19:24:58.279+07:00  INFO 39434 --- [qtp1558550579-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01m74g379p3swa1v3m32tizd78b0, token = df113b69017c849e9585cf6f67c89dda
2025-07-09T19:24:58.739+07:00  INFO 39434 --- [qtp1558550579-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-09T19:24:58.739+07:00  INFO 39434 --- [qtp1558550579-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-09T19:25:00.235+07:00 DEBUG 39434 --- [qtp1558550579-59] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:25:00.235+07:00 DEBUG 39434 --- [qtp1558550579-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:25:04.510+07:00  INFO 39434 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-09T19:25:04.513+07:00  INFO 39434 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-09T19:25:37.736+07:00  INFO 39434 --- [qtp1558550579-36] n.d.m.c.BotSendMessageBotHandler         : Process send message You have new message from CRM system !. delivered count = 0, delivered error count = 0, delivered abort count = 0
2025-07-09T19:25:38.290+07:00  INFO 39434 --- [qtp1558550579-36] n.d.m.c.BotSendMessageBotHandler         : Process send message You have new message from CRM system !. delivered count = 0, delivered error count = 0, delivered abort count = 0
2025-07-09T19:25:50.350+07:00  INFO 39434 --- [qtp1558550579-36] n.d.m.monitor.call.EndpointCallService   : Call fail, duplicate key value violates unique constraint. Endpoint BotSendMessageBotHandler/handle
2025-07-09T19:25:53.626+07:00  INFO 39434 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-07-09T19:25:53.652+07:00 DEBUG 39434 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:25:53.652+07:00 DEBUG 39434 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01m74g379p3swa1v3m32tizd78b0, remote user nhat.le
2025-07-09T19:25:53.653+07:00 DEBUG 39434 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:25:53.653+07:00  INFO 39434 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-09T19:26:00.208+07:00 DEBUG 39434 --- [qtp1558550579-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:26:06.677+07:00  INFO 39434 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-09T19:26:26.712+07:00 DEBUG 39434 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:26:26.713+07:00 DEBUG 39434 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01m74g379p3swa1v3m32tizd78b0, remote user nhat.le
2025-07-09T19:26:26.714+07:00 DEBUG 39434 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:26:35.947+07:00  INFO 39434 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@1c6057a7{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-09T19:26:35.949+07:00  INFO 39434 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-09T19:26:35.949+07:00  INFO 39434 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-09T19:26:35.949+07:00  INFO 39434 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-09T19:26:35.949+07:00  INFO 39434 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-09T19:26:35.950+07:00  INFO 39434 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-07-09T19:26:35.950+07:00  INFO 39434 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-07-09T19:26:35.950+07:00  INFO 39434 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-09T19:26:35.950+07:00  INFO 39434 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-07-09T19:26:35.950+07:00  INFO 39434 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-07-09T19:26:35.950+07:00  INFO 39434 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-09T19:26:35.950+07:00  INFO 39434 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-07-09T19:26:35.950+07:00  INFO 39434 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-07-09T19:26:35.950+07:00  INFO 39434 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-09T19:26:35.950+07:00  INFO 39434 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-07-09T19:26:35.950+07:00  INFO 39434 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-07-09T19:26:35.967+07:00  INFO 39434 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-09T19:26:36.070+07:00  INFO 39434 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-07-09T19:26:36.076+07:00  INFO 39434 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-07-09T19:26:36.100+07:00  INFO 39434 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-09T19:26:36.102+07:00  INFO 39434 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-09T19:26:36.103+07:00  INFO 39434 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-07-09T19:26:36.104+07:00  INFO 39434 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-07-09T19:26:36.104+07:00  INFO 39434 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-09T19:26:36.287+07:00  INFO 39434 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-09T19:26:36.289+07:00  INFO 39434 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-07-09T19:26:36.290+07:00  INFO 39434 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-07-09T19:26:36.290+07:00  INFO 39434 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-07-09T19:26:36.290+07:00  INFO 39434 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-07-09T19:26:36.294+07:00  INFO 39434 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@9124278{STOPPING}[12.0.15,sto=0]
2025-07-09T19:26:36.298+07:00  INFO 39434 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-09T19:26:36.300+07:00  INFO 39434 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@ba65354{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.6000333202881885764/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@5af850f1{STOPPED}}

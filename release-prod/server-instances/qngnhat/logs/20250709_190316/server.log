2025-07-09T19:03:17.545+07:00  INFO 36393 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 36393 (/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-prod/server)
2025-07-09T19:03:17.547+07:00  INFO 36393 --- [main] net.datatp.server.ServerApp              : The following 8 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-console", "database", "db-schema-validate", "data"
2025-07-09T19:03:18.467+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.623+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 152 ms. Found 22 JPA repository interfaces.
2025-07-09T19:03:18.642+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.644+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-09T19:03:18.644+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.655+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 9 JPA repository interfaces.
2025-07-09T19:03:18.657+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.662+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 3 JPA repository interfaces.
2025-07-09T19:03:18.662+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.667+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-07-09T19:03:18.679+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.686+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 1 JPA repository interface.
2025-07-09T19:03:18.698+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.704+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 5 JPA repository interfaces.
2025-07-09T19:03:18.712+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.716+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 3 JPA repository interfaces.
2025-07-09T19:03:18.717+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.718+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-09T19:03:18.727+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.736+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 9 JPA repository interfaces.
2025-07-09T19:03:18.750+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.755+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 3 JPA repository interfaces.
2025-07-09T19:03:18.755+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.759+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-09T19:03:18.761+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.770+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 12 JPA repository interfaces.
2025-07-09T19:03:18.771+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.775+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-07-09T19:03:18.775+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.776+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-09T19:03:18.776+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.777+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-09T19:03:18.778+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.784+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 7 JPA repository interfaces.
2025-07-09T19:03:18.784+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.786+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-09T19:03:18.786+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.786+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-09T19:03:18.786+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.800+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 19 JPA repository interfaces.
2025-07-09T19:03:18.810+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.817+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-07-09T19:03:18.818+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.822+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-07-09T19:03:18.823+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.828+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-07-09T19:03:18.828+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.834+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-07-09T19:03:18.835+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.840+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 6 JPA repository interfaces.
2025-07-09T19:03:18.841+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.845+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-07-09T19:03:18.845+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.857+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 14 JPA repository interfaces.
2025-07-09T19:03:18.857+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.873+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 16 ms. Found 25 JPA repository interfaces.
2025-07-09T19:03:18.890+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.909+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 18 ms. Found 19 JPA repository interfaces.
2025-07-09T19:03:18.909+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.917+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 6 JPA repository interfaces.
2025-07-09T19:03:18.918+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.925+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 2 JPA repository interfaces.
2025-07-09T19:03:18.930+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.934+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 0 JPA repository interfaces.
2025-07-09T19:03:18.934+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:18.943+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 12 JPA repository interfaces.
2025-07-09T19:03:18.948+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:19.013+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 64 ms. Found 67 JPA repository interfaces.
2025-07-09T19:03:19.013+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:19.015+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-09T19:03:19.016+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:03:19.019+07:00  INFO 36393 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-09T19:03:19.285+07:00  INFO 36393 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-09T19:03:19.289+07:00  INFO 36393 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-09T19:03:19.639+07:00  WARN 36393 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-09T19:03:19.870+07:00  INFO 36393 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-07-09T19:03:19.872+07:00  INFO 36393 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-07-09T19:03:19.886+07:00  INFO 36393 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-07-09T19:03:19.887+07:00  INFO 36393 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2210 ms
2025-07-09T19:03:19.933+07:00  WARN 36393 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-09T19:03:19.934+07:00  INFO 36393 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-07-09T19:03:20.049+07:00  INFO 36393 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@64f3f849
2025-07-09T19:03:20.050+07:00  INFO 36393 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-07-09T19:03:20.055+07:00  WARN 36393 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-09T19:03:20.055+07:00  INFO 36393 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-07-09T19:03:20.059+07:00  INFO 36393 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@334d96af
2025-07-09T19:03:20.059+07:00  INFO 36393 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-07-09T19:03:20.059+07:00  WARN 36393 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-09T19:03:20.059+07:00  INFO 36393 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-09T19:03:20.628+07:00  INFO 36393 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@d200d3e
2025-07-09T19:03:20.628+07:00  INFO 36393 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-09T19:03:20.629+07:00  WARN 36393 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-09T19:03:20.629+07:00  INFO 36393 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-07-09T19:03:20.638+07:00  INFO 36393 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@5b7b5484
2025-07-09T19:03:20.638+07:00  INFO 36393 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-07-09T19:03:20.638+07:00  INFO 36393 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************'
2025-07-09T19:03:20.710+07:00  INFO 36393 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-07-09T19:03:20.717+07:00  INFO 36393 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@ba65354{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.6795467717047126910/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@5af850f1{STARTED}}
2025-07-09T19:03:20.718+07:00  INFO 36393 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@ba65354{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.6795467717047126910/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@5af850f1{STARTED}}
2025-07-09T19:03:20.720+07:00  INFO 36393 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@9124278{STARTING}[12.0.15,sto=0] @3915ms
2025-07-09T19:03:20.808+07:00  INFO 36393 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09T19:03:20.861+07:00  INFO 36393 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-09T19:03:20.894+07:00  INFO 36393 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-09T19:03:21.141+07:00  INFO 36393 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-09T19:03:21.180+07:00  WARN 36393 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-09T19:03:22.207+07:00  INFO 36393 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-09T19:03:22.218+07:00  INFO 36393 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2ab689b7] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-09T19:03:22.369+07:00  INFO 36393 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09T19:03:22.697+07:00  INFO 36393 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages: 
 [ "net.datatp.module.project", "cloud.datatp.fforwarder.mgmt", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "cloud.datatp.fforwarder.price", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "cloud.datatp.fforwarder.sales", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-07-09T19:03:22.700+07:00  INFO 36393 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "validate",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-07-09T19:03:22.727+07:00  INFO 36393 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09T19:03:22.729+07:00  INFO 36393 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-09T19:03:22.764+07:00  INFO 36393 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-09T19:03:22.771+07:00  WARN 36393 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-09T19:03:25.691+07:00  INFO 36393 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-09T19:03:25.692+07:00  INFO 36393 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@7f67bcaa] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-09T19:03:25.828+07:00  INFO 36393 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09T19:03:25.866+07:00  INFO 36393 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-07-09T19:03:25.871+07:00  INFO 36393 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-07-09T19:03:25.872+07:00  INFO 36393 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-09T19:03:25.880+07:00  WARN 36393 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-09T19:03:26.029+07:00  INFO 36393 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-09T19:03:26.500+07:00  INFO 36393 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-09T19:03:26.503+07:00  INFO 36393 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-09T19:03:26.541+07:00  INFO 36393 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-09T19:03:26.585+07:00  INFO 36393 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-07-09T19:03:26.668+07:00  INFO 36393 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-09T19:03:26.697+07:00  INFO 36393 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-09T19:03:26.719+07:00  INFO 36393 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 10144163ms : this is harmless.
2025-07-09T19:03:26.727+07:00  INFO 36393 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-07-09T19:03:26.730+07:00  INFO 36393 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-09T19:03:26.749+07:00  INFO 36393 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 91884052ms : this is harmless.
2025-07-09T19:03:26.751+07:00  INFO 36393 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-07-09T19:03:26.764+07:00  INFO 36393 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-07-09T19:03:26.765+07:00  INFO 36393 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-07-09T19:03:30.022+07:00  INFO 36393 --- [main] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 09/07/2025@19:00:00+0700 to 09/07/2025@19:15:00+0700
2025-07-09T19:03:30.022+07:00  INFO 36393 --- [main] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 09/07/2025@19:00:00+0700 to 09/07/2025@19:15:00+0700
2025-07-09T19:03:30.873+07:00  INFO 36393 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-07-09T19:03:30.873+07:00  INFO 36393 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-09T19:03:30.874+07:00  WARN 36393 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-09T19:03:31.213+07:00  INFO 36393 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-07-09T19:03:31.213+07:00  INFO 36393 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/templates
2025-07-09T19:03:31.437+07:00  INFO 36393 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/datatp-crm/templates
2025-07-09T19:03:31.437+07:00  INFO 36393 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/logistics/templates
2025-07-09T19:03:31.437+07:00  INFO 36393 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/document-ie/templates
2025-07-09T19:03:33.156+07:00  WARN 36393 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 00a80732-2832-4df4-93a9-d20b22f169bb

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-09T19:03:33.160+07:00  INFO 36393 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-09T19:03:33.505+07:00  INFO 36393 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-09T19:03:33.506+07:00  INFO 36393 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-07-09T19:03:33.506+07:00  INFO 36393 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-07-09T19:03:33.506+07:00  INFO 36393 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-09T19:03:33.506+07:00  INFO 36393 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-07-09T19:03:33.506+07:00  INFO 36393 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-07-09T19:03:33.506+07:00  INFO 36393 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-09T19:03:33.506+07:00  INFO 36393 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-07-09T19:03:33.506+07:00  INFO 36393 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-07-09T19:03:33.506+07:00  INFO 36393 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-09T19:03:33.506+07:00  INFO 36393 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-07-09T19:03:33.506+07:00  INFO 36393 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-07-09T19:03:33.509+07:00  INFO 36393 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-09T19:03:33.509+07:00  INFO 36393 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-09T19:03:33.509+07:00  INFO 36393 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-09T19:03:33.526+07:00  INFO 36393 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09T19:03:33.526+07:00  INFO 36393 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-09T19:03:33.528+07:00  INFO 36393 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-07-09T19:03:33.536+07:00  INFO 36393 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@73297e0b{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-09T19:03:33.537+07:00  INFO 36393 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-07-09T19:03:33.538+07:00  INFO 36393 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-07-09T19:03:33.566+07:00  INFO 36393 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-07-09T19:03:33.566+07:00  INFO 36393 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-07-09T19:03:33.572+07:00  INFO 36393 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 16.383 seconds (process running for 16.767)
2025-07-09T19:04:01.629+07:00  INFO 36393 --- [qtp1558550579-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0yl4gdmag51a41x1zp751xxg8z0
2025-07-09T19:04:01.981+07:00  INFO 36393 --- [qtp1558550579-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0yl4gdmag51a41x1zp751xxg8z0, token = df113b69017c849e9585cf6f67c89dda
2025-07-09T19:04:02.324+07:00  INFO 36393 --- [qtp1558550579-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-09T19:04:03.905+07:00 DEBUG 36393 --- [qtp1558550579-39] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:04:03.905+07:00 DEBUG 36393 --- [qtp1558550579-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:04:06.586+07:00  INFO 36393 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-09T19:04:12.073+07:00  INFO 36393 --- [qtp1558550579-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-07-09T19:04:28.669+07:00 DEBUG 36393 --- [botTaskExecutor-2] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "",
    "companyCode" : "beehph",
    "companyId" : 8,
    "remoteUser" : "nhat.le",
    "accountId" : 2089,
    "token" : "df113b69017c849e9585cf6f67c89dda",
    "tokenId" : 60624,
    "remoteIp" : "",
    "sessionId" : "node0yl4gdmag51a41x1zp751xxg8z0",
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : ":nhat.le"
  },
  "company" : {
    "id" : 8,
    "editState" : "ORIGIN",
    "loadState" : "FromDB",
    "version" : 1,
    "createdBy" : "admin",
    "createdTime" : "17/08/2022@05:49:54+0000",
    "modifiedBy" : "admin",
    "modifiedTime" : "17/08/2022@05:49:54+0000",
    "storageState" : "ACTIVE",
    "code" : "beehph",
    "label" : "Bee HPH",
    "fullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
    "parentId" : 4,
    "parentLabel" : "Bee Corp",
    "parentIdPath" : "4/8",
    "adminAccountId" : 1988,
    "adminAccountLabel" : "Bee HPH",
    "adminAccountLoginId" : "beehph",
    "foundingDate" : "09/12/2010@17:00:00+0000",
    "registrationCode" : "*********"
  },
  "name" : "communication:send-message",
  "data" : {
    "id" : 69273,
    "uikey" : null,
    "editState" : "ORIGIN",
    "loadState" : "FromDB",
    "version" : 0,
    "createdBy" : "nhat.le",
    "createdTime" : "09/07/2025@12:04:27+0000",
    "modifiedBy" : "nhat.le",
    "modifiedTime" : "09/07/2025@12:04:27+0000",
    "storageState" : "ACTIVE",
    "code" : "MD5:Fd092a43F2477dA516D04c918eBdF7Dc",
    "parentCode" : "MD5:Fd092a43F2477dA516D04c918eBdF7Dc",
    "senderLoginId" : "dcenter",
    "subject" : "You have new message from CRM system !",
    "content" : "<div>You have new Meeting/Traing alert as follows:</div><div>- Meeting/Training Date: 09/07/2025</div><div>- From: 08:00</div><div>- To: 10:00</div><div>- Zoom ID/PW: </div><div>- Contents: test noti</div><div>- Room Name: Phòng họp tầng 6</div><div>- Hosted by: Le Quang Nhat (<EMAIL>)</div><div>- Notes: </div><div>- Created by: Le Quang Nhat - 09/07/2025 19:04</div><div><i>(Please do not reply to this email, because it was sent automatically by the system).</i></div>",
    "mimeType" : null,
    "sentToZalo" : null,
    "recipients" : [ {
      "id" : 143889,
      "uikey" : null,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "version" : 0,
      "createdBy" : "nhat.le",
      "createdTime" : "09/07/2025@12:04:27+0000",
      "modifiedBy" : "nhat.le",
      "modifiedTime" : "09/07/2025@12:04:27+0000",
      "storageState" : "ACTIVE",
      "deliverType" : "Email",
      "recipientDisplayName" : "Le Quang Nhat",
      "recipientId" : "<EMAIL>",
      "deliverStatus" : "Waiting",
      "deliverCount" : 0,
      "delivered" : false,
      "note" : null,
      "forwardEmail" : true
    }, {
      "id" : 143890,
      "uikey" : null,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "version" : 0,
      "createdBy" : "nhat.le",
      "createdTime" : "09/07/2025@12:04:27+0000",
      "modifiedBy" : "nhat.le",
      "modifiedTime" : "09/07/2025@12:04:27+0000",
      "storageState" : "ACTIVE",
      "deliverType" : "Email",
      "recipientDisplayName" : "Le Quang Nhat",
      "recipientId" : "<EMAIL>",
      "deliverStatus" : "Waiting",
      "deliverCount" : 0,
      "delivered" : false,
      "note" : null,
      "forwardEmail" : true
    } ],
    "links" : [ ],
    "flags" : [ ],
    "status" : "Draft",
    "attachments" : [ ],
    "graphAttachments" : [ ],
    "error" : null,
    "errorFileAttachment" : null,
    "esignature" : null
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : null
}
2025-07-09T19:04:28.695+07:00  INFO 36393 --- [botTaskExecutor-2] n.d.m.c.BotSendMessageBotHandler         : Process send message You have new message from CRM system !. delivered count = 0, delivered error count = 0, delivered abort count = 0
2025-07-09T19:04:28.695+07:00  INFO 36393 --- [botTaskExecutor-2] n.d.m.c.BotSendMessageBotHandler         : Process send message You have new message from CRM system !. delivered count = 0, delivered error count = 0, delivered abort count = 0
2025-07-09T19:04:33.701+07:00 ERROR 36393 --- [botTaskExecutor-2] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint BotSendMessageBotHandler/handle
2025-07-09T19:04:33.701+07:00 ERROR 36393 --- [botTaskExecutor-2] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: null
	at net.datatp.module.communication.BotSendMessageBotHandler.handle(BotSendMessageBotHandler.java:68)
	at net.datatp.module.bot.BotService$1.doCall(BotService.java:61)
	at net.datatp.module.bot.BotService$1.doCall(BotService.java:58)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:138)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.bot.BotService.process(BotService.java:69)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.bot.BotService$$SpringCGLIB$$0.process(<generated>)
	at net.datatp.module.bot.BotEventMessageHandler.process(BotEventMessageHandler.java:32)
	at net.datatp.module.bot.BotEventMessageHandler.lambda$handleMessage$0(BotEventMessageHandler.java:24)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-07-09T19:04:36.698+07:00  INFO 36393 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 0
2025-07-09T19:04:36.726+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:04:36.726+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0yl4gdmag51a41x1zp751xxg8z0, remote user nhat.le
2025-07-09T19:04:36.727+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:04:36.727+07:00  INFO 36393 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-09T19:04:37.093+07:00  INFO 36393 --- [qtp1558550579-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0yl4gdmag51a41x1zp751xxg8z0, token = df113b69017c849e9585cf6f67c89dda
2025-07-09T19:04:37.094+07:00  INFO 36393 --- [qtp1558550579-59] n.d.module.session.ClientSessionManager  : Add a client session id = node0yl4gdmag51a41x1zp751xxg8z0, token = df113b69017c849e9585cf6f67c89dda
2025-07-09T19:04:37.109+07:00  INFO 36393 --- [qtp1558550579-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-09T19:04:37.112+07:00  INFO 36393 --- [qtp1558550579-59] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-09T19:04:39.414+07:00 DEBUG 36393 --- [qtp1558550579-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:04:39.414+07:00 DEBUG 36393 --- [qtp1558550579-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:05:04.058+07:00 DEBUG 36393 --- [qtp1558550579-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:05:04.788+07:00  INFO 36393 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-09T19:05:04.789+07:00  INFO 36393 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-09T19:05:09.795+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:05:09.796+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0yl4gdmag51a41x1zp751xxg8z0, remote user nhat.le
2025-07-09T19:05:09.796+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:05:39.854+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:05:39.856+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0yl4gdmag51a41x1zp751xxg8z0, remote user nhat.le
2025-07-09T19:05:39.856+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:05:40.048+07:00 DEBUG 36393 --- [qtp1558550579-39] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:06:04.045+07:00 DEBUG 36393 --- [qtp1558550579-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:06:06.899+07:00  INFO 36393 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-09T19:06:07.905+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:06:07.906+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0yl4gdmag51a41x1zp751xxg8z0, remote user nhat.le
2025-07-09T19:06:07.906+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:06:35.967+07:00  INFO 36393 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-09T19:06:35.974+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:06:35.975+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0yl4gdmag51a41x1zp751xxg8z0, remote user nhat.le
2025-07-09T19:06:35.975+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:06:35.977+07:00  INFO 36393 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-09T19:06:40.066+07:00 DEBUG 36393 --- [qtp1558550579-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:07:04.028+07:00  INFO 36393 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-09T19:07:04.086+07:00 DEBUG 36393 --- [qtp1558550579-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:07:10.042+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:07:10.043+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0yl4gdmag51a41x1zp751xxg8z0, remote user nhat.le
2025-07-09T19:07:10.043+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:07:39.096+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:07:39.097+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0yl4gdmag51a41x1zp751xxg8z0, remote user nhat.le
2025-07-09T19:07:39.097+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:07:40.049+07:00 DEBUG 36393 --- [qtp1558550579-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:08:04.049+07:00 DEBUG 36393 --- [qtp1558550579-39] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:08:06.135+07:00  INFO 36393 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-09T19:08:07.139+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:08:07.140+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0yl4gdmag51a41x1zp751xxg8z0, remote user nhat.le
2025-07-09T19:08:07.140+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:08:40.044+07:00 DEBUG 36393 --- [qtp1558550579-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:08:40.211+07:00  INFO 36393 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-07-09T19:08:40.220+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:08:40.220+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0yl4gdmag51a41x1zp751xxg8z0, remote user nhat.le
2025-07-09T19:08:40.220+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:08:40.220+07:00  INFO 36393 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-09T19:09:03.258+07:00  INFO 36393 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-09T19:09:04.044+07:00 DEBUG 36393 --- [qtp1558550579-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:09:10.275+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:09:10.275+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0yl4gdmag51a41x1zp751xxg8z0, remote user nhat.le
2025-07-09T19:09:10.276+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:09:38.332+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:09:38.334+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0yl4gdmag51a41x1zp751xxg8z0, remote user nhat.le
2025-07-09T19:09:38.334+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:09:40.052+07:00 DEBUG 36393 --- [qtp1558550579-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:10:06.371+07:00  INFO 36393 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-09T19:10:06.373+07:00  INFO 36393 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-09T19:10:06.374+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:10:06.374+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0yl4gdmag51a41x1zp751xxg8z0, remote user nhat.le
2025-07-09T19:10:06.374+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:10:12.041+07:00 DEBUG 36393 --- [qtp1558550579-39] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:10:40.467+07:00  INFO 36393 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-09T19:10:40.484+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:10:40.484+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0yl4gdmag51a41x1zp751xxg8z0, remote user nhat.le
2025-07-09T19:10:40.485+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:10:40.485+07:00  INFO 36393 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-09T19:11:02.519+07:00  INFO 36393 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-09T19:11:09.536+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:11:09.537+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0yl4gdmag51a41x1zp751xxg8z0, remote user nhat.le
2025-07-09T19:11:09.538+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:11:12.079+07:00 DEBUG 36393 --- [qtp1558550579-39] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:11:12.079+07:00 DEBUG 36393 --- [qtp1558550579-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:11:37.600+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:11:37.602+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0yl4gdmag51a41x1zp751xxg8z0, remote user nhat.le
2025-07-09T19:11:37.603+07:00 DEBUG 36393 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:11:48.807+07:00  INFO 36393 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@73297e0b{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-09T19:11:48.809+07:00  INFO 36393 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-09T19:11:48.809+07:00  INFO 36393 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-09T19:11:48.809+07:00  INFO 36393 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-09T19:11:48.809+07:00  INFO 36393 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-09T19:11:48.809+07:00  INFO 36393 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-07-09T19:11:48.809+07:00  INFO 36393 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-07-09T19:11:48.809+07:00  INFO 36393 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-09T19:11:48.809+07:00  INFO 36393 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-07-09T19:11:48.809+07:00  INFO 36393 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-07-09T19:11:48.810+07:00  INFO 36393 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-09T19:11:48.810+07:00  INFO 36393 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-07-09T19:11:48.810+07:00  INFO 36393 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-07-09T19:11:48.810+07:00  INFO 36393 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-09T19:11:48.810+07:00  INFO 36393 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-07-09T19:11:48.810+07:00  INFO 36393 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-07-09T19:11:48.826+07:00  INFO 36393 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-09T19:11:48.918+07:00  INFO 36393 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-07-09T19:11:48.923+07:00  INFO 36393 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-07-09T19:11:49.013+07:00  INFO 36393 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-09T19:11:49.015+07:00  INFO 36393 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-09T19:11:49.016+07:00  INFO 36393 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-07-09T19:11:49.017+07:00  INFO 36393 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-07-09T19:11:49.017+07:00  INFO 36393 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-09T19:11:49.157+07:00  INFO 36393 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-09T19:11:49.157+07:00  INFO 36393 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-07-09T19:11:49.158+07:00  INFO 36393 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-07-09T19:11:49.158+07:00  INFO 36393 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-07-09T19:11:49.159+07:00  INFO 36393 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-07-09T19:11:49.163+07:00  INFO 36393 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@9124278{STOPPING}[12.0.15,sto=0]
2025-07-09T19:11:49.174+07:00  INFO 36393 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-09T19:11:49.176+07:00  INFO 36393 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@ba65354{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.6795467717047126910/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@5af850f1{STOPPED}}

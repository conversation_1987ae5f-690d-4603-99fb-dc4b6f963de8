2025-07-09T19:27:14.813+07:00  INFO 39970 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 39970 (/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-prod/server)
2025-07-09T19:27:14.814+07:00  INFO 39970 --- [main] net.datatp.server.ServerApp              : The following 8 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-console", "database", "db-schema-validate", "data"
2025-07-09T19:27:15.533+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.635+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 98 ms. Found 22 JPA repository interfaces.
2025-07-09T19:27:15.643+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.645+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-09T19:27:15.645+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.652+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 9 JPA repository interfaces.
2025-07-09T19:27:15.653+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.656+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-09T19:27:15.656+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.660+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-09T19:27:15.671+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.675+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-07-09T19:27:15.685+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.689+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-07-09T19:27:15.693+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.695+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-09T19:27:15.695+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.695+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-09T19:27:15.700+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.706+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-09T19:27:15.710+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.712+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-09T19:27:15.712+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.716+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-09T19:27:15.717+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.724+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-07-09T19:27:15.724+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.727+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-09T19:27:15.727+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.727+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-09T19:27:15.727+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.728+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-07-09T19:27:15.728+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.733+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-07-09T19:27:15.733+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.735+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-09T19:27:15.735+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.735+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-09T19:27:15.735+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.745+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-07-09T19:27:15.755+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.761+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-07-09T19:27:15.761+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.764+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-09T19:27:15.764+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.768+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-07-09T19:27:15.768+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.773+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 9 JPA repository interfaces.
2025-07-09T19:27:15.773+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.777+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-09T19:27:15.777+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.780+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-09T19:27:15.780+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.789+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-07-09T19:27:15.789+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.804+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 25 JPA repository interfaces.
2025-07-09T19:27:15.816+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.827+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-07-09T19:27:15.827+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.831+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-09T19:27:15.831+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.833+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-09T19:27:15.837+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.838+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-09T19:27:15.838+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.844+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-07-09T19:27:15.849+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.885+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 67 JPA repository interfaces.
2025-07-09T19:27:15.885+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.886+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-09T19:27:15.886+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09T19:27:15.889+07:00  INFO 39970 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-09T19:27:16.090+07:00  INFO 39970 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-09T19:27:16.094+07:00  INFO 39970 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-09T19:27:16.372+07:00  WARN 39970 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-09T19:27:16.563+07:00  INFO 39970 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-07-09T19:27:16.565+07:00  INFO 39970 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-07-09T19:27:16.576+07:00  INFO 39970 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-07-09T19:27:16.576+07:00  INFO 39970 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1665 ms
2025-07-09T19:27:16.628+07:00  WARN 39970 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-09T19:27:16.628+07:00  INFO 39970 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-07-09T19:27:16.717+07:00  INFO 39970 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@2505480a
2025-07-09T19:27:16.717+07:00  INFO 39970 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-07-09T19:27:16.722+07:00  WARN 39970 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-09T19:27:16.722+07:00  INFO 39970 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-07-09T19:27:16.726+07:00  INFO 39970 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@21339b6f
2025-07-09T19:27:16.727+07:00  INFO 39970 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-07-09T19:27:16.727+07:00  WARN 39970 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-09T19:27:16.727+07:00  INFO 39970 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-09T19:27:17.222+07:00  INFO 39970 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@4f2ab774
2025-07-09T19:27:17.222+07:00  INFO 39970 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-09T19:27:17.222+07:00  WARN 39970 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-09T19:27:17.222+07:00  INFO 39970 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-07-09T19:27:17.230+07:00  INFO 39970 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@d200d3e
2025-07-09T19:27:17.230+07:00  INFO 39970 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-07-09T19:27:17.230+07:00  INFO 39970 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************'
2025-07-09T19:27:17.276+07:00  INFO 39970 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-07-09T19:27:17.278+07:00  INFO 39970 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@3bc57af4{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.5752330764489760991/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@30628ef9{STARTED}}
2025-07-09T19:27:17.278+07:00  INFO 39970 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@3bc57af4{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.5752330764489760991/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@30628ef9{STARTED}}
2025-07-09T19:27:17.279+07:00  INFO 39970 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@3b5d0c4d{STARTING}[12.0.15,sto=0] @3009ms
2025-07-09T19:27:17.332+07:00  INFO 39970 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09T19:27:17.358+07:00  INFO 39970 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-09T19:27:17.372+07:00  INFO 39970 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-09T19:27:17.508+07:00  INFO 39970 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-09T19:27:17.539+07:00  WARN 39970 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-09T19:27:18.185+07:00  INFO 39970 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-09T19:27:18.203+07:00  INFO 39970 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@3fe19a69] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-09T19:27:18.297+07:00  INFO 39970 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09T19:27:18.482+07:00  INFO 39970 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages: 
 [ "net.datatp.module.project", "cloud.datatp.fforwarder.mgmt", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "cloud.datatp.fforwarder.price", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "cloud.datatp.tms", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "cloud.datatp.fforwarder.sales", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-07-09T19:27:18.484+07:00  INFO 39970 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "validate",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-07-09T19:27:18.492+07:00  INFO 39970 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09T19:27:18.493+07:00  INFO 39970 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-09T19:27:18.523+07:00  INFO 39970 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-09T19:27:18.526+07:00  WARN 39970 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-09T19:27:21.377+07:00  INFO 39970 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-09T19:27:21.377+07:00  INFO 39970 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@5df976a3] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-09T19:27:21.478+07:00  INFO 39970 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09T19:27:21.512+07:00  INFO 39970 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-07-09T19:27:21.517+07:00  INFO 39970 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-07-09T19:27:21.518+07:00  INFO 39970 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-09T19:27:21.523+07:00  WARN 39970 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-09T19:27:21.657+07:00  INFO 39970 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-09T19:27:22.118+07:00  INFO 39970 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-09T19:27:22.121+07:00  INFO 39970 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-09T19:27:22.156+07:00  INFO 39970 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-09T19:27:22.193+07:00  INFO 39970 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-07-09T19:27:22.206+07:00  INFO 39970 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-09T19:27:22.234+07:00  WARN 39970 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Attempting to allocate 100.0MB of offheap when there is only 96.4MB of free physical memory - some paging will therefore occur.
2025-07-09T19:27:22.234+07:00  INFO 39970 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-09T19:27:22.269+07:00  INFO 39970 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 16030467ms : this is harmless.
2025-07-09T19:27:22.278+07:00  INFO 39970 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-07-09T19:27:22.281+07:00  WARN 39970 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Attempting to allocate 100.0MB of offheap when there is only 92.5MB of free physical memory - some paging will therefore occur.
2025-07-09T19:27:22.281+07:00  INFO 39970 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-09T19:27:22.295+07:00  INFO 39970 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 97770358ms : this is harmless.
2025-07-09T19:27:22.297+07:00  INFO 39970 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-07-09T19:27:22.309+07:00  INFO 39970 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-07-09T19:27:22.310+07:00  INFO 39970 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-07-09T19:27:25.094+07:00  INFO 39970 --- [main] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 09/07/2025@19:15:00+0700 to 09/07/2025@19:30:00+0700
2025-07-09T19:27:25.095+07:00  INFO 39970 --- [main] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 09/07/2025@19:15:00+0700 to 09/07/2025@19:30:00+0700
2025-07-09T19:27:25.700+07:00  INFO 39970 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-07-09T19:27:25.700+07:00  INFO 39970 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-09T19:27:25.701+07:00  WARN 39970 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-09T19:27:26.123+07:00  INFO 39970 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-07-09T19:27:26.123+07:00  INFO 39970 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/templates
2025-07-09T19:27:26.123+07:00  INFO 39970 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/datatp-crm/templates
2025-07-09T19:27:26.123+07:00  INFO 39970 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/logistics/templates
2025-07-09T19:27:26.123+07:00  INFO 39970 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/document-ie/templates
2025-07-09T19:27:27.733+07:00  WARN 39970 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 1863b1d4-0d01-472f-a5f3-efbb08d09af7

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-09T19:27:27.736+07:00  INFO 39970 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-09T19:27:28.056+07:00  INFO 39970 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-09T19:27:28.056+07:00  INFO 39970 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-07-09T19:27:28.056+07:00  INFO 39970 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-07-09T19:27:28.057+07:00  INFO 39970 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-09T19:27:28.057+07:00  INFO 39970 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-07-09T19:27:28.057+07:00  INFO 39970 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-07-09T19:27:28.057+07:00  INFO 39970 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-09T19:27:28.057+07:00  INFO 39970 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-07-09T19:27:28.057+07:00  INFO 39970 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-07-09T19:27:28.057+07:00  INFO 39970 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-09T19:27:28.057+07:00  INFO 39970 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-07-09T19:27:28.057+07:00  INFO 39970 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-07-09T19:27:28.059+07:00  INFO 39970 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-09T19:27:28.060+07:00  INFO 39970 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-09T19:27:28.060+07:00  INFO 39970 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-09T19:27:28.076+07:00  INFO 39970 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09T19:27:28.076+07:00  INFO 39970 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-09T19:27:28.079+07:00  INFO 39970 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-07-09T19:27:28.088+07:00  INFO 39970 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@4c8eff88{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-09T19:27:28.089+07:00  INFO 39970 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-07-09T19:27:28.090+07:00  INFO 39970 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-07-09T19:27:28.120+07:00  INFO 39970 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-07-09T19:27:28.120+07:00  INFO 39970 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-07-09T19:27:28.126+07:00  INFO 39970 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.613 seconds (process running for 13.855)
2025-07-09T19:27:32.867+07:00  INFO 39970 --- [qtp1514834483-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01mqsmemhdsb4wwr20ntzgtzx91
2025-07-09T19:27:32.867+07:00  INFO 39970 --- [qtp1514834483-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01ue9k0mnibn8g12kur346vl68m0
2025-07-09T19:27:33.029+07:00  INFO 39970 --- [qtp1514834483-37] n.d.module.session.ClientSessionManager  : Add a client session id = node01mqsmemhdsb4wwr20ntzgtzx91, token = df113b69017c849e9585cf6f67c89dda
2025-07-09T19:27:33.029+07:00  INFO 39970 --- [qtp1514834483-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01ue9k0mnibn8g12kur346vl68m0, token = df113b69017c849e9585cf6f67c89dda
2025-07-09T19:27:33.471+07:00  INFO 39970 --- [qtp1514834483-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-09T19:27:33.471+07:00  INFO 39970 --- [qtp1514834483-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-09T19:27:34.906+07:00 DEBUG 39970 --- [qtp1514834483-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:27:34.945+07:00 DEBUG 39970 --- [qtp1514834483-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:28:03.148+07:00  INFO 39970 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-09T19:28:31.233+07:00  INFO 39970 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-07-09T19:28:31.274+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:28:31.274+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ue9k0mnibn8g12kur346vl68m0, remote user nhat.le
2025-07-09T19:28:31.275+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:28:31.275+07:00  INFO 39970 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-09T19:28:34.957+07:00 DEBUG 39970 --- [qtp1514834483-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:29:04.332+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:29:04.333+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ue9k0mnibn8g12kur346vl68m0, remote user nhat.le
2025-07-09T19:29:04.333+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:29:06.343+07:00  INFO 39970 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-09T19:29:34.393+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:29:34.395+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ue9k0mnibn8g12kur346vl68m0, remote user nhat.le
2025-07-09T19:29:34.395+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:29:34.913+07:00 DEBUG 39970 --- [qtp1514834483-65] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:30:02.439+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:30:02.440+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ue9k0mnibn8g12kur346vl68m0, remote user nhat.le
2025-07-09T19:30:02.440+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:30:02.441+07:00  INFO 39970 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-09T19:30:02.447+07:00  INFO 39970 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-09T19:30:02.455+07:00  INFO 39970 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-09T19:30:02.464+07:00  INFO 39970 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 09/07/2025@19:30:02+0700
2025-07-09T19:30:02.481+07:00  INFO 39970 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 09/07/2025@19:30:00+0700 to 09/07/2025@19:45:00+0700
2025-07-09T19:30:02.482+07:00  INFO 39970 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 09/07/2025@19:30:00+0700 to 09/07/2025@19:45:00+0700
2025-07-09T19:30:08.524+07:00 DEBUG 39970 --- [botTaskExecutor-2] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "09/07/2025 00:00:00 GMT+0700",
      "toDate" : "09/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-09T19:30:30.569+07:00  INFO 39970 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-07-09T19:30:30.585+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:30:30.586+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ue9k0mnibn8g12kur346vl68m0, remote user nhat.le
2025-07-09T19:30:30.587+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:30:30.587+07:00  INFO 39970 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-09T19:30:34.941+07:00 DEBUG 39970 --- [qtp1514834483-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:31:04.637+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:31:04.638+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ue9k0mnibn8g12kur346vl68m0, remote user nhat.le
2025-07-09T19:31:04.642+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:31:05.647+07:00  INFO 39970 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-09T19:31:33.691+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:31:33.692+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ue9k0mnibn8g12kur346vl68m0, remote user nhat.le
2025-07-09T19:31:33.693+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:31:34.960+07:00 DEBUG 39970 --- [qtp1514834483-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:32:01.733+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:32:01.735+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ue9k0mnibn8g12kur346vl68m0, remote user nhat.le
2025-07-09T19:32:01.736+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:32:06.741+07:00  INFO 39970 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-09T19:32:34.785+07:00  INFO 39970 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-09T19:32:34.792+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-09T19:32:34.792+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ue9k0mnibn8g12kur346vl68m0, remote user nhat.le
2025-07-09T19:32:34.792+07:00 DEBUG 39970 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-09T19:32:34.793+07:00  INFO 39970 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-09T19:32:34.910+07:00 DEBUG 39970 --- [qtp1514834483-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-09T19:32:42.750+07:00  INFO 39970 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@4c8eff88{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-09T19:32:42.752+07:00  INFO 39970 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-09T19:32:42.752+07:00  INFO 39970 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-09T19:32:42.752+07:00  INFO 39970 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-09T19:32:42.752+07:00  INFO 39970 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-09T19:32:42.752+07:00  INFO 39970 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-07-09T19:32:42.752+07:00  INFO 39970 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-07-09T19:32:42.752+07:00  INFO 39970 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-09T19:32:42.752+07:00  INFO 39970 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-07-09T19:32:42.752+07:00  INFO 39970 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-07-09T19:32:42.753+07:00  INFO 39970 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-09T19:32:42.753+07:00  INFO 39970 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-07-09T19:32:42.753+07:00  INFO 39970 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-07-09T19:32:42.753+07:00  INFO 39970 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-09T19:32:42.753+07:00  INFO 39970 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-07-09T19:32:42.753+07:00  INFO 39970 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-07-09T19:32:42.772+07:00  INFO 39970 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-09T19:32:42.843+07:00  INFO 39970 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-07-09T19:32:42.848+07:00  INFO 39970 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-07-09T19:32:42.875+07:00  INFO 39970 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-09T19:32:42.877+07:00  INFO 39970 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-09T19:32:42.878+07:00  INFO 39970 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-07-09T19:32:42.879+07:00  INFO 39970 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-07-09T19:32:42.879+07:00  INFO 39970 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-09T19:32:43.027+07:00  INFO 39970 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-09T19:32:43.027+07:00  INFO 39970 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-07-09T19:32:43.028+07:00  INFO 39970 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-07-09T19:32:43.029+07:00  INFO 39970 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-07-09T19:32:43.029+07:00  INFO 39970 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-07-09T19:32:43.032+07:00  INFO 39970 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@3b5d0c4d{STOPPING}[12.0.15,sto=0]
2025-07-09T19:32:43.034+07:00  INFO 39970 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-09T19:32:43.035+07:00  INFO 39970 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@3bc57af4{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.5752330764489760991/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@30628ef9{STOPPED}}

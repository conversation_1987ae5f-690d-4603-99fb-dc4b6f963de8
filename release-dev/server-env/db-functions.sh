#!/usr/bin/env bash

source ./db-env.sh

function has_opt() {
  OPT_NAME=$1
  shift
  #Par the parameters
  for i in "$@"; do
    if [[ $i == $OPT_NAME ]] ; then
      echo "true"
      return
    fi
  done
  echo "false"
}

function get_opt() {
  OPT_NAME=$1
  DEFAULT_VALUE=$2
  shift

  #Par the parameters
  for i in "$@"; do
    index=$(($index+1))
    if [[ $i == $OPT_NAME* ]] ; then
      value="${i#*=}"
      echo "$value"
      return
    fi
  done
  echo $DEFAULT_VALUE
}

function drop_user() {
  DBUSER=$1
  echo "Drop user $DBUSER"
  PGPASSWORD=$PG_ADMIN_PASSWORD dropuser  --if-exists  -h $DB_HOST -U $PG_ADMIN_USER  $DBUSER
}

function create_user() {
  DBUSER=$1
  echo "Drop user $DBUSER if exists"
  PGPASSWORD=$PG_ADMIN_PASSWORD dropuser  --if-exists  -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER  $DBUSER
  echo "Create user $DBUSER"
  PGPASSWORD=$PG_ADMIN_PASSWORD createuser -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER $DBUSER
  echo "Create user password for $DBUSER"
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER -c "ALTER USER \"$DBUSER\" WITH ENCRYPTED PASSWORD '$DBUSER'" postgres
}

function create_ro_user() {
  DBUSER=$1
  DBNAME=$2
  echo "Revoke all privileges from $DBUSER on database $DBNAME if exists"
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER $DBNAME -c "REVOKE ALL PRIVILEGES ON ALL TABLES IN SCHEMA public FROM \"$DBUSER\"" 2>/dev/null || true
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER $DBNAME -c "REVOKE ALL PRIVILEGES ON SCHEMA public FROM \"$DBUSER\"" 2>/dev/null || true
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER -c "REVOKE ALL PRIVILEGES ON DATABASE $DBNAME FROM \"$DBUSER\"" 2>/dev/null || true

  echo "Drop user $DBUSER if exists"
  PGPASSWORD=$PG_ADMIN_PASSWORD dropuser  --if-exists  -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER  "$DBUSER" 2> /dev/null || true

  echo "Create read-only user $DBUSER"
  PGPASSWORD=$PG_ADMIN_PASSWORD createuser -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER "$DBUSER" 2>/dev/null || true

  echo "Create user password for $DBUSER"
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER -c "ALTER USER \"$DBUSER\" WITH ENCRYPTED PASSWORD '${DBUSER}-ro!@#123'"

  echo "Grant read-only permissions to $DBUSER on database $DBNAME"
  # Grant connect to database
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER -c "GRANT CONNECT ON DATABASE $DBNAME TO \"$DBUSER\""

  # Grant usage on schema public
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER $DBNAME -c "GRANT USAGE ON SCHEMA public TO \"$DBUSER\""

  # Grant select on all existing tables in public schema
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER $DBNAME -c "
    DO
    \$do\$
    DECLARE
      r RECORD;
    BEGIN
      FOR r IN SELECT tablename FROM pg_tables WHERE schemaname = 'public'
      LOOP
        EXECUTE 'GRANT SELECT ON ' || quote_ident(r.tablename) || ' TO \"$DBUSER\"';
      END LOOP;
    END
    \$do\$;
  "

  # Grant select on future tables
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER $DBNAME -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO \"$DBUSER\""
}

function db_drop() {
  DBUSER=$1
  DBNAME=$2
  echo "Drop DB $DBNAME if exists"
  PGPASSWORD=$DBUSER psql -h $DB_HOST -p $DB_PORT -U $DBUSER -c "DROP DATABASE IF EXISTS $DBNAME" postgres
}

function db_new() {
  DB_ADMIN_USER=$1
  DB_NAME=$2
  db_drop $DB_ADMIN_USER "$DB_NAME"
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER -c "CREATE DATABASE \"$DB_NAME\"" postgres
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER -c "ALTER DATABASE \"$DB_NAME\" OWNER TO \"$DB_ADMIN_USER\";" postgres
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER -c "GRANT ALL PRIVILEGES ON DATABASE \"$DB_NAME\" TO \"$DB_ADMIN_USER\"" postgres
}

function db_dump() {
  echo "args = $@"
  dbUser=$(get_opt --db-user '' $@)
  dbName=$(get_opt --db-name '' $@)
  format=$(get_opt --format 'c' $@)
  backup_file=$(get_opt --file '' $@)
  if [ "$backup_file" = "" ]; then
    mkdir -p dbbackup
    backup_file="dbbackup/$dbName-latest.dump"
  fi
  PGPASSWORD=$dbUser pg_dump \
    -v --blobs --no-owner \
    -h $DB_HOST -U $dbUser -F$format -f $backup_file $dbName
}

function db_restore() {
  dbUser=$(get_opt --db-user '' $@)
  dbName=$(get_opt --db-name '' $@)
  format=$(get_opt --format 'c' $@)
  jobs=$(get_opt --jobs '' $@)

  JOB_OPTIONS="-j $jobs"
  if [ "$jobs" = "" ]; then
    JOB_OPTIONS=""
  fi

  backup_file=$(get_opt --file '' $@)
  if [ "$backup_file" = "" ]; then
    backup_file="dbbackup/$dbName.dump"
  fi
  #echo "Drop And Recreate DB $DB_NAME"
  echo "Start Restoring DB $dbName"
  PGPASSWORD=$dbUser pg_restore \
    --verbose --exit-on-error --no-owner --no-acl $JOB_OPTIONS \
    --host=$DB_HOST --username=$dbUser --dbname=$dbName  "$backup_file"
  echo "Restored DB $DB_NAME!!!"
}

function db_run() {
  DB_NAME=$1
  CMD=$2
  DB_USER=$(get_opt --db-user '' $@)
  DB_ADMIN_USER=$(get_opt --db-admin-user '' $@)
  DB_RO_USER=$(get_opt --db-ro-user '' $@)
  echo Config:
  echo "  DB_NAME         = $DB_NAME"
  echo "  CMD             = $CMD"
  echo "  DB_ADMIN_USER   = $DB_ADMIN_USER"
  echo "  DB_USER         = $DB_USER"
  echo "  DB_RO_USER      = $DB_RO_USER"
  if [ "$CMD" = "create-admin" ] ; then
    create_user $DB_ADMIN_USER
    PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER -c "ALTER USER $DB_ADMIN_USER CREATEDB" postgres
  elif [ "$CMD" = "create-user" ] ; then
    create_user $DB_USER
  elif [ "$CMD" = "create-ro-user" ] ; then
    create_ro_user "$DB_RO_USER" $DB_NAME
  elif [ "$CMD" = "new" ] ; then
    db_new $DB_ADMIN_USER $DB_NAME
  elif [ "$CMD" = "drop" ] ; then
    db_drop $DB_USER $DB_NAME
  elif [ "$CMD" = "vacuum" ] ; then
    PGPASSWORD=$DB_USER vacuumlo -h $DB_HOST -p $DB_PORT -U $DB_USER -v $DB_NAME
  elif [ "$CMD" = "psql" ] ; then
    PGPASSWORD=$DB_USER psql -h $DB_HOST -p $DB_PORT -U $DB_USER $DB_NAME
  elif [ "$CMD" = "dump" ] ; then
    time db_dump --format=c --db-name=$DB_NAME $@
  elif [ "$CMD" = "restore" ] ; then
    time db_restore --db-name=$DB_NAME $@
  else
    echo 'Usage DB: '
    echo "  db create-admin       Create the db admin user"
    echo "  db create-user        Create the db r/w   user"
    echo "  db create-ro-user     Create the db read only user"
    echo "  db new                Drop and then create the new db"
    echo "  db drop               Drop the db"
  fi
}


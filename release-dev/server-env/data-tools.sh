#!/usr/bin/env bash

source ./common.sh
source ./env.sh

function run_backup() {
  time=`date '+%Y%m%dT%H%M'`
  BACKUP_DIR=$(get_opt --backup-dir "$BASE_DIR/../backup-$time" $@)
  if [ -d "$BACKUP_DIR" ]; then
    echo "Backup Dir $BACKUP_DIR exists!!!"
    exit
  fi

  mkdir -p $BACKUP_DIR
  echo "Backup server to $BACKUP_DIR"
  cp -r $BASE_DIR/server $BACKUP_DIR/server

  echo "Backup server-env to $BACKUP_DIR"
  cp -r $BASE_DIR/server-env $BACKUP_DIR/server-env

  echo "Backup storage data to $BACKUP_DIR"
  cp -r $BASE_DIR/server-data $BACKUP_DIR/server-data

  ./postgres.sh dump $@ --file=$BACKUP_DIR/backupdb.tar
}

function run_restore() {
  BACKUP_DIR="../"
  ./postgres.sh restore --file=$BACKUP_DIR/backupdb.tar
}

function run_clean() {
  rm -rf $BASE_DIR/server-instances/*
  echo "Clean server-instances directory $BASE_DIR/server-instances/*"
  DATA_OPT=$(has_opt "-data" $@ )
  if [ "$DATA_OPT" = "true" ] ; then
    rm -rf $BASE_DIR/server/data
    echo "Clean server-instances directory $BASE_DIR/server/data/*"

    rm -rf $BASE_DIR/server-data/*
    echo "Clean server-data directory"

    ./postgres.sh newDb $@
    echo "Create new database"
  fi
}

PG_CMD=$1
shift

if [ "$PG_CMD" = "backup" ] ; then
  run_backup  $@
elif [ "$PG_CMD" = "restore" ] ; then
  run_restore  $@
elif [ "$PG_CMD" = "new:env" ] ; then
  run_clean -data
else
  echo 'Usage: '
  echo "  ./tools.sh beta:backup                      Backup data and storage for beta env"
  echo "      [--backup-dir=dir]                      Backup directory"
  echo ""
  echo "  ./tools.sh beta:restore                     Restore data and storage for beta env"
  echo "      [--backup-dir=dir]                      Backup directory"
  echo ""
  echo "  ./tools.sh beta:new:env                     Create beta new env, clean db, remove server-data, server-instances..."
  echo "      [--backup-dir=dir]                      Backup directory"
fi

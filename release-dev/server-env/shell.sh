#!/usr/bin/env bash

source ./env.sh

SHELL_CMD="$RELEASE_DIR/server/bin/shell.sh"

CMD=$1

COMPANY=$(get_opt --company '' $@)
if [  "$COMPANY" ];then
  echo "SET COMPANY ENV = $COMPANY"
  SERVER_USER=$COMPANY
  SERVER_USER_PASSWORD=$COMPANY
fi

if [ "$CMD" = "script:exec" ] ; then
  $SHELL_CMD $@ --script-dir $SCRIPT_CODE_DIR --script-data-dir $SCRIPT_DATA_DIR
elif [ "$CMD" = "server:script:exec" ] ; then
  echo "-------------------------------------"
  $SHELL_CMD $@  \
    --user $SERVER_USER --password $SERVER_USER_PASSWORD --rest-url $SERVER_REST_URL  \
    --script-dir $SCRIPT_CODE_DIR --script-data-dir $SCRIPT_DATA_DIR
else
  echo "Usase:"
  echo " - Edit the env.sh file to update SCRIPT_DIR, SERVER_USER, SERVER_USER_PASSWORD, SERVER_REST_URL according to your env"
  echo "To run the script only:"
  echo "  ./shell.sh script:exec --script hello/Main.groovy"
  echo "To run the script in the server:"
  echo "  ./shell.sh server:script:exec --script hello/Main.groovy"
fi

if [ "$CMD" = "server:script:exec:beescs" ] ; then
  echo "-------------------------------------"
  $SHELL_CMD $@  \
    --user beescs --password beescs --rest-url $SERVER_REST_URL  \
    --script-dir $SCRIPT_CODE_DIR --script-data-dir $SCRIPT_DATA_DIR
fi


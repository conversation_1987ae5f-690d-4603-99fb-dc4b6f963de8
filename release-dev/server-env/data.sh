#!/usr/bin/env bash

source ./common.sh
source ./env.sh

function run_backup() {
  time=`date '+%Y%m%dT%H%M'`
  BACKUP_DIR=$(get_opt --backup-dir "$BASE_DIR/../backup-$time" $@)
  if [ -d "$BACKUP_DIR" ]; then
    echo "Backup Dir $BACKUP_DIR exists!!!"
    exit
  fi

  mkdir -p $BACKUP_DIR
  echo "Backup server to $BACKUP_DIR"
  cp -r $BASE_DIR/server $BACKUP_DIR/server

  echo "Backup server-env to $BACKUP_DIR"
  cp -r $BASE_DIR/server-env $BACKUP_DIR/server-env

  echo "Backup storage data to $BACKUP_DIR"
  cp -r $BASE_DIR/server-data $BACKUP_DIR/server-data

  mkdir -p  $BACKUP_DIR/dbbackup

  ./db.sh datatpdb dump --file=$BACKUP_DIR/dbbackup/datatpdb_backup.tar $@
  ./db.sh document_ie_db dump --file=$BACKUP_DIR/dbbackup/document_ie_db_backup.tar $@
}

function run_restore() {
  BACKUP_DIR=".."
  ./db.sh datatpdb restore --file=$BACKUP_DIR/dbbackup/datatpdb_backup.tar $@
  ./db.sh document_ie_db restore --file=$BACKUP_DIR/dbbackup/document_ie_db_backup.tar $@
}

PG_CMD=$1
shift

if [ "$PG_CMD" = "backup" ] ; then
  run_backup  $@
elif [ "$PG_CMD" = "restore" ] ; then
  run_restore  $@
else
  echo 'Usage: '
  echo "  ./data.sh backup                      Backup data "
  echo "  ./data.sh restore                     Restore data "
fi

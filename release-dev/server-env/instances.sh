#!/usr/bin/env bash

source ./env.sh

function ask_yes_or_no() {
  if [ "$SERVER_ENV" = "prod" ] ; then
    echo "The server env is in the production mode. You need to change to the update mode to continue";
    exit;
  fi
  MESSAGE=$1;
  read -p "$MESSAGE (yes or No): "
  case $(echo $REPLY | tr '[A-Z]' '[a-z]') in
    yes) echo "You choose yes" ;;
    *)     echo "You choose no" && exit ;;
  esac
}

function log_tail() {
  INSTANCES_DIR=(`ls  $ROOT_DIR/server-instances/`)
  for instance in "${INSTANCES_DIR[@]}"; do
    echo "#######################################################################################"
    echo "Instance: $instance"
    echo "#######################################################################################"
    LOG_DIRS=(`ls -d $ROOT_DIR/server-instances/$instance/logs/*/ | sort -r`)
    LOG_FILE=${LOG_DIRS[0]}/server.log
    tail $@ $LOG_FILE
  done
}

function log_grep() {
  INSTANCES_DIR=(`ls  $ROOT_DIR/server-instances/`)
  for instance in "${INSTANCES_DIR[@]}"; do
    echo "#######################################################################################"
    echo "Instance: $instance"
    echo "#######################################################################################"
    LOG_DIRS=(`ls -d $ROOT_DIR/server-instances/$instance/logs/*/ | sort -r`)
    LOG_FILE=${LOG_DIRS[0]}/server.log
    grep $@ $LOG_FILE
  done
}

function run_clean() {
  rm -rf $BASE_DIR/server-instances/*
  echo "Clean server-instances directory $BASE_DIR/server-instances/*"

  rm -rf $BASE_DIR/server/data
  echo "Clean server-instances directory $BASE_DIR/server/data/*"

  rm -rf $BASE_DIR/server-data/*
  echo "Clean server-data directory"
}

COMMAND=$1
shift

SERVER_CMD="$RELEASE_DIR/server/bin/server.sh"

if [ "$COMMAND" = "start" ] ; then
  $SERVER_CMD start -daemon --env=$SERVER_ENV $@
elif [ "$COMMAND" = "run" ] ; then
  $SERVER_CMD start --env=$SERVER_ENV --profile=$SERVER_ENV-console $@
elif [ "$COMMAND" = "run:debug" ] ; then
  $SERVER_CMD start --env=$SERVER_ENV  --profile=$SERVER_ENV-debug $@
elif [ "$COMMAND" = "run:update" ] ; then
  $SERVER_CMD start --env=$SERVER_ENV  --profile=$SERVER_ENV-update $@
elif [ "$COMMAND" = "stop" ] ; then
  $SERVER_CMD stop $@
elif [ "$COMMAND" = "clean" ] ; then
  run_clean
elif [ "$COMMAND" = "log:watch" ] ; then
  $SERVER_CMD log:watch $@
elif [ "$COMMAND" = "log:tail" ] ; then
  log_tail $@
elif [ "$COMMAND" = "log:grep" ] ; then
  log_grep $@
else
  echo 'Usage: '
  echo "  start                  Start the server with the $SERVER_ENV env"
  echo "  run                    Run the server with the console mode and the $SERVER_ENV env"
  echo "  run:debug              Run the server with the console + debug mode and the $SERVER_ENV env"
  echo "  run:update             Run the server with the console + update db schema mode and the $SERVER_ENV env"
  echo "  stop                   Kill the running server instance, the $SERVER_ENV env"
  echo '  log:watch              Log Monitor'
fi

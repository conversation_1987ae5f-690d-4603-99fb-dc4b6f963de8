#!/usr/bin/env bash

window=false
bin=`dirname "$0"`
if [ "$OSTYPE" = "msys" ] ; then
  window=true;
elif [[ "$OSTYPE" == "cygwin" ]]; then
  window=true;
elif [[ "$OSTYPE" == "win32" ]]; then
  window=true;
elif [[ "$OSTYPE" == "darwin20.0" ]]; then
  window=true;
fi

SERVER_ENV_DIR=`cd "$bin"; pwd`
BASE_DIR="$SERVER_ENV_DIR/.."
ROOT_DIR="$BASE_DIR"
RELEASE_DIR="$BASE_DIR"

echo "BASE_DIR = $BASE_DIR"

if $window; then
  RELEASE_DIR=`cygpath --absolute --windows "$RELEASE_DIR"`
fi

SERVER_ENV="prod"

#SCRIPT_DIR="/Users/<USER>/projects/datatp/datatp-build/app/scripts"
#SCRIPT_CODE_DIR="$SCRIPT_DIR/src/main/groovy"
#SCRIPT_DATA_DIR="$SCRIPT_DIR/data"

#SERVER_USER="tony.nguyen"
#SERVER_USER_PASSWORD="DatTua!23"
#SERVER_REST_URL="http://localhost:7080/rest/v1.0.0"


#!/usr/bin/env bash

source ./common.sh
source ./env.sh

function dump_db() {
  echo "args = $@"
  format=$(get_opt --format 'c' $@)
  backup_file=$(get_opt --file '' $@)
  if [ "$backup_file" = "" ]; then
    mkdir -p dbbackup
    backup_file="dbbackup/$DB_NAME-latest.dump"
  fi
  PGPASSWORD=$DB_USER pg_dump -v --blobs -h $DB_HOST -U $DB_USER -F$format -f $backup_file $DB_NAME
}

function restore_db() {
  jobs=$(get_opt --jobs '' $@)
  JOB_OPTIONS="-j $jobs"
  if [ "$jobs" = "" ]; then
    JOB_OPTIONS=""
  fi

  backup_file=$(get_opt --file '' $@)
  if [ "$backup_file" = "" ]; then
    backup_file="dbbackup/$DB_NAME.dump"
  fi
  #echo "Drop And Recreate DB $DB_NAME"
  echo "Start Restoring DB $DB_NAME"
  #PGPASSWORD=$PG_ADMIN_PASSWORD pg_restore \
  #  --verbose --exit-on-error --no-owner $JOB_OPTIONS \
  #  --host=$DB_HOST --username=$PG_ADMIN_USER --dbname=$DB_NAME "$backup_file"
  PGPASSWORD=$DB_USER pg_restore \
    --verbose --exit-on-error --no-owner $JOB_OPTIONS \
    --host=$DB_HOST --username=$DB_USER --dbname=$DB_NAME "$backup_file"
  echo "Restored DB $DB_NAME!!!"
}

PG_CMD=$1
shift

if [ "$PG_CMD" = "psql" ] ; then
  echo "PGPASSWORD=$DB_USER psql -h $DB_HOST -p $DB_PORT -U $DB_USER $DB_NAME"
  PGPASSWORD=$DB_USER psql -h $DB_HOST -p $DB_PORT -U $DB_USER $DB_NAME
elif [ "$PG_CMD" = "dump" ] ; then
  time dump_db --format=c $@
elif [ "$PG_CMD" = "restore" ] ; then
  time restore_db $@
else
  echo 'Usage: '
  echo "  ./postgres.sh psql        Run psql client with $PG_ADMIN_USER and $DB_NAME"
  echo ""
  echo "  ./postgres.sh dump        Dump data from db $DB_NAME to  dbbackup/$DB_NAME.tar"
  echo "              [--file=path/name]     Optional file name instead default dbbackup/$DB_NAME.tar"
  echo ""
  echo "  ./postgres.sh restore     Restore data from dbbackup/$DB_NAME.tar to db $DB_NAME"
  echo "              [--file=path/name]     Optional file name instead default dbbackup/$DB_NAME.tar"
  echo ""
fi

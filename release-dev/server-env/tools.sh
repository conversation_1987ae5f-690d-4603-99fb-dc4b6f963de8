#!/usr/bin/env bash

window=false
if [ "$OSTYPE" = "msys" ] ; then
  window=true;
elif [[ "$OSTYPE" == "cygwin" ]]; then
  window=true;
elif [[ "$OSTYPE" == "win32" ]]; then
  window=true;
elif [[ "$OSTYPE" == "darwin20.0" ]]; then
  window=true;
fi

bin=`dirname "$0"`
BASE_DIR=`cd $bin/..; pwd; cd $bin`

if $window; then
  BASE_DIR=`cygpath --absolute --windows "$ROOT_DIR"`
fi

COMMAND=$1
shift


if [ "$COMMAND" = "backup" ] ; then
  (cd $BASE_DIR/server-env; ./data-tools.sh backup $@)
elif [ "$COMMAND" = "restore" ] ; then
  (cd $BASE_DIR/server-env; ./data-tools.sh restore $@)
elif [ "$COMMAND" = "new:env" ] ; then
  (cd $BASE_DIR/server-env; ./data-tools.sh new:env $@)
else
  echo "Usage: "
fi

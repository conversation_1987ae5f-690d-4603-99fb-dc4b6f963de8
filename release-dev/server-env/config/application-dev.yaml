# App Configuration ###########################
app:
  env: dev

---
spring:
  profiles:
    group:
      dev-debug: log-debug

---
spring:
  profiles:
    group:
      dev-postgres: postgres

---
spring:
  profiles:
    group:
      dev-postgres-debug: postgres, log-debug

---
spring:
  config:
    activate:
      on-profile: hsql
  datasource:
    rw:
      jdbcUrl: ******************************
      driverClassName: org.hsqldb.jdbcDriver
      username: sa
      password:
    hibernate:
      show_sql: false
      dialect: org.hibernate.dialect.HSQLDialect
      hbm2ddl:
        auto: update
---
spring:
  config:
    activate:
      on-profile: postgres
  datasource:
    db-host: localhost
    rw:
      type: com.zaxxer.hikari.HikariDataSource
      hikari:
        connectionTimeout: 30000
        idleTimeout: 600000
        maxLifetime: 600000
      auto-commit: false
      driverClassName: org.postgresql.Driver
      jdbcUrl: jdbc:postgresql://${spring.datasource.db-host}:5432/ofonedb
      username: ofone
      password: ofone
    hibernate:
      show_sql: false
      dialect: org.hibernate.dialect.PostgreSQLDialect
      hbm2ddl:
        auto: update
  batch:
    datasource:
      db-type: postgresql


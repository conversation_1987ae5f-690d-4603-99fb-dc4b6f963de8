<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <property name="LOG_FILE" value="${app.instance.log.dir}/server.log}"/>

  <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
  <include resource="org/springframework/boot/logging/logback/console-appender.xml" />
  <include resource="org/springframework/boot/logging/logback/file-appender.xml" />

  <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
    <resetJUL>true</resetJUL>
  </contextListener>

  <root level="INFO">
    <appender-ref ref="CONSOLE" />
    <appender-ref ref="FILE" />
  </root>
  <!--
  <logger name="org.hibernate" level="DEBUG" />
  <logger name="org.springframework" level="DEBUG" />
  <logger name="org.eclipse.jetty.server" level="DEBUG" />
  <logger name="org.eclipse.jetty.websocket" level="DEBUG" />
  <logger name="net.datatp" level="DEBUG"/>
  <logger name="cloud.datatp" level="INFO"/>
  <logger name="net.datatp.module.monitor.call.EndpointCallContext" level="DEBUG"/>
  <logger name="net.datatp.module.monitor.call.EndpointCallService" level="DEBUG"/>
  <logger name="net.datatp.module.groovy"   level="DEBUG"/>
  <logger name="net.datatp.module.data.db.util"   level="DEBUG"/>
  <logger name="net.datatp.module.storage" level="DEBUG"/>
  <logger name="net.datatp.module.document"   level="DEBUG"/>
  <logger name="net.datatp.module.wfms"   level="DEBUG"/>
  <logger name="net.datatp.module.kpi"   level="DEBUG"/>
  -->
  <logger name="net.datatp.module.bot"   level="DEBUG"/>
  <logger name="net.datatp.module.websocket"   level="DEBUG"/>
</configuration>

#!/usr/bin/env bash

source ./db-functions.sh

PG_CMD=$1
echo "PG_CMD = $PG_CMD"
shift

if [ "$PG_CMD" = "psql" ] ; then
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER postgres
elif [ "$PG_CMD" = "datatpdb" ] ; then
  db_run datatpdb $@ --db-admin-user=datatp --db-user=datatp --db-ro-user=datatp-ro
elif [ "$PG_CMD" = "datatp_basic_db" ] ; then
  db_run datatp_basic_db $@ --db-admin-user=datatp --db-user=datatp --db-ro-user=datatp-ro
elif [ "$PG_CMD" = "document_ie_db" ] ; then
  db_run document_ie_db $@ --db-admin-user=document_ie --db-user=document_ie --db-ro-user=document-ie-ro
else
  echo 'Usage: '
  echo "  ./db.sh psql            Run psql client with $PG_ADMIN_USER and $DB_NAME"
fi

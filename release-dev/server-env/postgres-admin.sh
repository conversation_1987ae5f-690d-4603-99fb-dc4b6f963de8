#!/usr/bin/env bash

source ./common.sh
source ./env.sh

function drop_user() {
  DBUSER=$1
  echo "Drop user $DBUSER"
  PGPASSWORD=$PG_ADMIN_PASSWORD dropuser  --if-exists  -h $DB_HOST -U $PG_ADMIN_USER  $DBUSER
}

function create_user() {
  DBUSER=$1
  echo "Drop user $DBUSER if exists"
  PGPASSWORD=$PG_ADMIN_PASSWORD dropuser  --if-exists  -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER  $DBUSER
  echo "Create user $DBUSER"
  PGPASSWORD=$PG_ADMIN_PASSWORD createuser -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER $DBUSER
  echo "Create user password for $DB_USER"
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER -c "ALTER USER $DBUSER WITH ENCRYPTED PASSWORD '$DB_USER'"
}

function create_read_user() {
  DBUSER=$1
  DBNAME=$2
  echo "Revoke all privileges from $DBUSER on database $DBNAME if exists"
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER $DBNAME -c "REVOKE ALL PRIVILEGES ON ALL TABLES IN SCHEMA public FROM $DBUSER" 2>/dev/null || true
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER $DBNAME -c "REVOKE ALL PRIVILEGES ON SCHEMA public FROM $DBUSER" 2>/dev/null || true
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER -c "REVOKE ALL PRIVILEGES ON DATABASE $DBNAME FROM $DBUSER" 2>/dev/null || true

  echo "Drop user $DBUSER if exists"
  PGPASSWORD=$PG_ADMIN_PASSWORD dropuser  --if-exists  -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER  $DBUSER 2>/dev/null || true

  echo "Create read-only user $DBUSER"
  PGPASSWORD=$PG_ADMIN_PASSWORD createuser -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER $DBUSER 2>/dev/null || true

  echo "Create user password for $DBUSER"
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER -c "ALTER USER $DBUSER WITH ENCRYPTED PASSWORD '$DB_USER'"

  echo "Grant read-only permissions to $DBUSER on database $DBNAME"
  # Grant connect to database
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER -c "GRANT CONNECT ON DATABASE $DBNAME TO $DBUSER"

  # Grant usage on schema public
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER $DBNAME -c "GRANT USAGE ON SCHEMA public TO $DBUSER"

  # Grant select on all existing tables in public schema
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER $DBNAME -c "
    DO
    \$do\$
    DECLARE
      r RECORD;
    BEGIN
      FOR r IN SELECT tablename FROM pg_tables WHERE schemaname = 'public'
      LOOP
        EXECUTE 'GRANT SELECT ON ' || quote_ident(r.tablename) || ' TO $DBUSER';
      END LOOP;
    END
    \$do\$;
  "

  # Grant select on future tables
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER $DBNAME -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO $DBUSER"
}

function drop_db() {
  DBNAME=$1
  echo "Drop DB $DBNAME if exists"
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER -c "DROP DATABASE IF EXISTS $DBNAME"
}

function new_db() {
  DBNAME=$1
  drop_db $DBNAME
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER -c "CREATE DATABASE $DBNAME"
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER -c "ALTER DATABASE $DBNAME OWNER TO $DB_USER;"
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER -c "GRANT ALL PRIVILEGES ON DATABASE $DBNAME TO $DB_USER"
}

PG_CMD=$1
shift

if [ "$PG_CMD" = "psql" ] ; then
  echo "PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER $DB_NAME"
  PGPASSWORD=$DB_USER psql -h $DB_HOST -p $DB_PORT -U $DB_USER $DB_NAME
elif [ "$PG_CMD" = "create-db-admin" ] ; then
  DBADMIN=$(get_opt --db-admin "$DB_ADMIN" $@)
  create_user $DBADMIN
  PGPASSWORD=$PG_ADMIN_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER -c "ALTER USER $DB_ADMIN CREATEDB"
elif [ "$PG_CMD" = "create-db-user" ] ; then
  DBUSER=$(get_opt --db-user "$DB_USER" $@)
  create_user $DBUSER
elif [ "$PG_CMD" = "create-read-user" ] ; then
  DBUSER=$(get_opt --db-user "$DB_USER_READONLY" $@)
  DBNAME=$(get_opt --db-name "$DB_NAME" $@)
  create_read_user $DBUSER $DBNAME
elif [ "$PG_CMD" = "new-db" ] ; then
  DBNAME=$(get_opt --db-name "$DB_NAME" $@)
  new_db $DBNAME
elif [ "$PG_CMD" = "drop-db" ] ; then
  DBNAME=$(get_opt --db-name "$DB_NAME" $@)
  drop_db $DBNAME
elif [ "$PG_CMD" = "clean-all" ] ; then
  drop_db   $DB_NAME
  drop_user $DB_USER
else
  echo 'Usage: '
  echo "  ./postgres.sh psql            Run psql client with $PG_ADMIN_USER and $DB_NAME"
  echo ""
  echo "  ./postgres.sh create-db-user  Create user $DB_USER and password "
  echo ""
  echo "  ./postgres.sh create-read-user Create read-only user $DB_USER_READONLY with access to $DB_NAME database"
  echo ""
  echo "  ./postgres.sh new-db          Drop, Create db $DB_NAME, grant $DB_USER user permissions "
  echo ""
  echo "  ./postgres.sh clean-all       Clean $DB_USER user and $DB_NAME db"
fi
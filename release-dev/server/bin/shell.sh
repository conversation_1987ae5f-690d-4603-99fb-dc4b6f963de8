#!/bin/bash

window=false
if [ "$OSTYPE" = "msys" ] ; then
  window=true;
elif [[ "$OSTYPE" == "cygwin" ]]; then
  window=true;
elif [[ "$OSTYPE" == "win32" ]]; then
  window=true;
fi

bin=`dirname "$0"`
bin=`cd "$bin"; pwd`

JAVACMD="$JAVA_HOME/bin/java"
APP_HOME=`cd $bin/..; pwd; cd $bin`

if $window; then
  JAVA_HOME=`cygpath --absolute --windows "$JAVA_HOME"`
  APP_HOME=`cygpath --path --windows "$APP_HOME"`
fi

LIB="$APP_HOME:$APP_HOME/lib/*:$APP_HOME/lib/spring/*:$APP_HOME/lib/hibernate/*:$APP_HOME/lib/jetty/*"

echo "----------------------------------------------------------------------------------"
echo "JAVA_HOME:        $JAVA_HOME"
echo "JAVA_OPTS:        $JAVA_OPTS"
echo "APP_HOME:         $APP_HOME"
ADDONS=''
ADDON_CONFIGS=''
for addon in $(cd $APP_HOME/addons && ls -d *); 
do 
  if [ -z "$ADDONS" ]
  then
    ADDONS="$addon"
  else
    ADDONS="$ADDONS,$addon"
  fi
  LIB="$LIB:$APP_HOME/addons/${addon%%/}"
  LIB="$LIB:$APP_HOME/addons/${addon%%/}/lib/*"
done
echo "ADDONS:        $ADDONS"
echo "LIB:           $LIB"
echo "ADDON_CONFIGS: $ADDON_CONFIGS"
echo "----------------------------------------------------------------------------------"

CLASSPATH="$JAVA_HOME/lib/tools.jar"
CLASSPATH="${CLASSPATH}:$LIB"

if $window; then
  CLASSPATH=`cygpath --path --windows "$CLASSPATH"`
fi

function run_shell() {
  echo -e "\n"
  CLASS="net.datatp.server.shell.CmdShell"
  exec "$JAVACMD" -cp "$CLASSPATH" $JAVA_OPTS $CLASS $@
}

cd $APP_HOME

echo "Usage: "
echo "  To run"
echo "    ./shell.sh --user=user --password=password script --script-dir=dir"

#SCRIPT_DIR=/Users/<USER>/projects/datatp/datatp-erp/app/scripts/src/hello
#run_shell --user admin --password admin script --script-dir=$SCRIPT_DIR

run_shell $@


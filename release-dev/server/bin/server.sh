#!/bin/bash

window=false
if [ "$OSTYPE" = "msys" ] ; then
  window=true;
elif [[ "$OSTYPE" == "cygwin" ]]; then
  window=true;
elif [[ "$OSTYPE" == "win32" ]]; then
  window=true;
fi

if [ -z "$HOSTNAME" ]
then
  HOSTNAME="localhost"
fi

function has_opt() {
  OPT_NAME=$1
  shift
  #Par the parameters
  for i in "$@"; do
    if [[ $i == $OPT_NAME ]] ; then
      echo "true"
      return
    fi
  done
  echo "false"
}

function get_opt() {
  OPT_NAME=$1
  DEFAULT_VALUE=$2
  shift
  
  #Par the parameters
  for i in "$@"; do
    index=$(($index+1))
    if [[ $i == $OPT_NAME* ]] ; then
      value="${i#*=}"
      echo "$value"
      return
    fi
  done
  echo $DEFAULT_VALUE
}

bin=`dirname "$0"`
bin=`cd "$bin"; pwd`

JAVACMD="$JAVA_HOME/bin/java"
APP_HOME=`cd $bin/..; pwd; cd $bin`
SHELL_HOST_DIR="$APP_HOME/../server-instances/$HOSTNAME"
HOST_DIR="$SHELL_HOST_DIR"

if $window; then
  JAVA_HOME=`cygpath --absolute --windows "$JAVA_HOME"`
  APP_HOME=`cygpath --path --windows "$APP_HOME"`
  HOST_DIR=`cygpath --path --windows "$HOST_DIR"`
fi

DATETIME=$(date '+%Y/%m/%d@%H:%M:%S')
DATETIME_ID=$(date '+%Y%m%d_%H%M%S')
INSTANCE_LOG_DIR="$HOST_DIR/logs/$DATETIME_ID"
INSTANCE_LOG_FILE="$INSTANCE_LOG_DIR/server.stdout"
INSTANCE_PID_FILE="$HOST_DIR/server.pid"
SERVER_DATA_DIR="$APP_HOME/../server-data"

LIB="$APP_HOME:$APP_HOME/lib/*:$APP_HOME/lib/spring/*:$APP_HOME/lib/hibernate/*:$APP_HOME/lib/jetty/*"

echo "----------------------------------------------------------------------------------"
echo "JAVA_HOME:        $JAVA_HOME"
echo "JAVA_OPTS:        $JAVA_OPTS"
echo "APP_HOME:         $APP_HOME"
echo "HOST_DIR:         $HOST_DIR"
echo "INSTANCE_DIR:     $HOST_DIR"
echo "INSTANCE_LOG_DIR: $INSTANCE_LOG_DIR"
ADDONS=''
ADDON_CONFIGS=''
for addon in $(cd $APP_HOME/addons && ls -d *); 
do 
  if [ -z "$ADDONS" ]
  then
    ADDONS="$addon"
    ADDON_CONFIGS="classpath:config/addon-$addon-config.yaml"
  else
    ADDONS="$ADDONS,$addon"
    ADDON_CONFIGS="$ADDON_CONFIGS,classpath:config/addon-$addon-config.yaml"
  fi
  LIB="$LIB:$APP_HOME/addons/${addon%%/}"
  LIB="$LIB:$APP_HOME/addons/${addon%%/}/lib/*"
done
echo "ADDONS:        $ADDONS"
echo "LIB:           $LIB"
echo "ADDON_CONFIGS: $ADDON_CONFIGS"
echo "----------------------------------------------------------------------------------"

CLASSPATH="$JAVA_HOME/lib/tools.jar"
CLASSPATH="${CLASSPATH}:$LIB:$APP_HOME/config:$APP_HOME/download:$APP_HOME/customer"

if $window; then
  CLASSPATH=`cygpath --path --windows "$CLASSPATH"`
fi

function start_server() {
  ENVIRONMENT=$(get_opt --env 'dev' $@)

  PROFILE=$(get_opt --profile "$ENVIRONMENT" $@)

  CONFIG_FILES="file:$APP_HOME/config/application.yaml"
  CONFIG_FILES="$CONFIG_FILES,$ADDON_CONFIGS"
  CONFIG_FILES="$CONFIG_FILES,file:$APP_HOME/config/application-$ENVIRONMENT.yaml"
  SERVER_ENV_CONFIG="$APP_HOME/../server-env/config"
  if [ -d "$SERVER_ENV_CONFIG" ] 
  then
    CONFIG_FILES="$CONFIG_FILES,file:$SERVER_ENV_CONFIG/application-$ENVIRONMENT.yaml"
  fi
  CONFIG_FILES="$CONFIG_FILES,file:$APP_HOME/config/application-profiles.yaml"

  #JAVA_OPTS="-server -XX:+UseParallelGC -Xshare:auto -Xms128m -Xmx1024m -Dfile.encoding=UTF-8"
  JAVA_OPTS="-server -XX:+UseParallelGC -Xshare:auto -Xms128m -Dfile.encoding=UTF-8"
  JAVA_OPTS="$JAVA_OPTS -Duser.dir=$APP_HOME -Dapp.home=$APP_HOME"
  JAVA_OPTS="$JAVA_OPTS -Dapp.instance.dir=$HOST_DIR -Dapp.instance.log.dir=$INSTANCE_LOG_DIR"
  JAVA_OPTS="--add-opens=java.base/java.util=ALL-UNNAMED  $JAVA_OPTS"
  JAVA_OPTS="--add-opens=java.base/java.lang=ALL-UNNAMED  $JAVA_OPTS"

  CLASS="net.datatp.server.ServerApp"
  
  ARGS="--app.home=$APP_HOME --app.config.dir=$APP_HOME/config --app.tmp.dir=$HOST_DIR/tmp"
  ARGS="$ARGS --app.data.dir=$SERVER_DATA_DIR"
  ARGS="$ARGS --app.addons=$ADDONS --build.version=$DATETIME"

  DAEMON_OPT=$(has_opt "-daemon" $@ )
  
  REMOTE_DEBUG="-agentlib:jdwp=transport=dt_socket,server=y,address=*:8000,suspend=n"

  mkdir -p $SERVER_DATA_DIR
  mkdir -p $INSTANCE_LOG_DIR

  if [ "$DAEMON_OPT" = "true" ]; then
    #JAVA_OPTS="$JAVA_OPTS -Xmx6144m -Dspring.profiles.active=$ADDONS,$PROFILE"
    JAVA_OPTS="$JAVA_OPTS -Xmx6000m -Dspring.profiles.active=$ADDONS,$PROFILE"
    ARGS="$ARGS --spring.config.location=$CONFIG_FILES"
    echo -e "\n"
    nohup "$JAVACMD" -cp "$CLASSPATH" $JAVA_OPTS $CLASS $ARGS $@ > $INSTANCE_LOG_FILE 2>&1 < /dev/null &
    printf '%d' $! > $INSTANCE_PID_FILE
  else
    JAVA_OPTS="$REMOTE_DEBUG $JAVA_OPTS -Xmx1024m -Dspring.profiles.active=$ADDONS,$PROFILE"
    ARGS="$ARGS --spring.config.location=$CONFIG_FILES"
    echo -e "\n"
    exec "$JAVACMD" -cp "$CLASSPATH" $JAVA_OPTS $CLASS $ARGS $@
  fi
}

function pid_file_kill() {
  PID=`cat $INSTANCE_PID_FILE`
  kill -9 $PID
  echo "Stopped processs $PID"
}

COMMAND=$1; 
shift

cd $APP_HOME

if [ "$COMMAND" = "start" ] ; then
  start_server $@
elif [ "$COMMAND" = "stop" ] ; then
  pid_file_kill $@
elif [ "$COMMAND" = "migrate" ] ; then
  start_server -migrate $@
elif [ "$COMMAND" = "clean:env" ] ; then
  echo "Clean Environment"
  rm -rf $SHELL_HOST_DIR/*
elif [ "$COMMAND" = "log:watch" ] ; then
  LOG_DIRS=(`ls -d $SHELL_HOST_DIR/logs/*/ | sort -r`)
  tail -f ${LOG_DIRS[0]}/server.log
elif [ "$COMMAND" = "log:less" ] ; then
  LOG_DIRS=(`ls -d $SHELL_HOST_DIR/logs/*/ | sort -r`)
  less ${LOG_DIRS[0]}/server.log
elif [ "$COMMAND" = "log:grep" ] ; then
  LOG_DIRS=(`ls -d $SHELL_HOST_DIR/logs/*/ | sort -r`)
  grep $@ ${LOG_DIRS[0]}/server.log
else
  echo "Usage: "
  echo "  To run the server as daemon"
  echo "    ./server.sh -daemon "
  echo "  To stop the daemon server"
  echo "    ./server.sh -stop "
  echo "  To run the server as console"
  echo "    ./server.sh"
  echo "  Optional parameters for the console mode:"
  echo "    --app.db.load=[test,none] to load the sample test data or an empty database"
  echo "    --server.port=7080 to override the default web server port"
fi

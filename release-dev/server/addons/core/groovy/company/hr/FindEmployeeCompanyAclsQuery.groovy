package company.hr;

/**
 * Employee - many to many - Department
 * @return Employee, Department, Role
 */

def static buildQuery() {
  return """
      SELECT
        emp."priority"        AS priority,
        com."parent_id"       AS company_parent_id,
        com."code"            AS company_code,
        com."label"           AS company_label,
        com."id"              AS company_id,
        'Employee'            as access_type,
        aa."login_id"         AS login_id
      FROM company_hr_employee emp
        JOIN company_company com ON com.id = emp.company_id 
        JOIN account_account  aa ON aa.id = emp.account_id 
      WHERE aa.login_id = :login_id AND emp.storage_state = 'ACTIVE'
      ORDER BY priority ASC 
  """
}

buildQuery();
package company;

def buildQuery() {
  return """
      SELECT DISTINCT dept_ref.employee_id as participant_employee_id 
      FROM company_hr_department_employee_rel AS dept_ref 
      WHERE dept_ref.department_id IN (
          SELECT sub_dept_ref.department_id 
          FROM company_hr_department_employee_rel AS sub_dept_ref 
          WHERE sub_dept_ref.company_id = :company_id 
              AND sub_dept_ref.role IN ('Manager', 'Leader') 
              AND sub_dept_ref.employee_id = :manager_employee_id
      )
      """
}

buildQuery()
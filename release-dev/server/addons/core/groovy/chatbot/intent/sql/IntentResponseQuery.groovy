package chatbot.intent.sql;

def query = """
    SELECT 
      id AS id, 
      text, 
      intent_id, 
      language,
      created_by, 
      created_time, 
      modified_by, 
      modified_time, 
      storage_state, 
      version
    FROM chatbot_intent_response
    WHERE 
      storage_state = COALESCE(:storage_state, storage_state) AND 
      text   LIKE :filter_val AND
      --language =    :language 
      intent_id  =  :intent_id
    ORDER BY 
      modified_time DESC
    LIMIT :max_return
"""

return query;
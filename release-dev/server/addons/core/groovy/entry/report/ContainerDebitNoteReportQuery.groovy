package entry.report;

/**
 * Build an SQL query to retrieve a list of cost details along with associated shipment information.
 *
 * @param {@code mode} The Transportation Mode associated with the table.
 * @param {@code hb_table} House Bill table name.
 * @param {@code mb_table} Master Bill table name.
 * @param {@code domain} The conditions, filters of the query.
 *
 * @return A string containing the SQL query.
 */

def buildQuery() {
  return """
      SELECT 
          '${mode}' as transportation_mode,
          mb.issued_date as issued_date,
          el.case_reference as case_reference,
          p.legacy_company_login_id  as partner_login_id,
          aop.tax_code as partner_tax_code,
          p."name" as partner_name,
          hb.code as hawb_no,
          mb.master_bill_no as mawb_no,
          
          -- <PERSON><PERSON> tờ khai của lô hàng (theo Booking Process), concatenated by comma
          (SELECT STRING_AGG(custom.cds_no, ', ') AS customs_no
                         FROM lgc_mgmt_cc_house_bill custom
                         WHERE custom.booking_process_id = bp.id) as customs_no,
          null as commodity,
          en.invoice_no,
          el.label as description,
          el.quantity as quantity,
          el.unit as unit,
          el.currency,
          el.unit_price as unit_price,
          NULL as adjust_vat,
          NULL as adjust_amount,
          en.type as entry_type,
          en.code as entry_code,
          ai.origin_invoice_no as origin_invoice_no,
          el.total_amount as total_amount,
          el.total_tax as total_tax,
          el.final_amount as final_amount,
          el.domestic_total_amount as domestic_total,
          el.domestic_total_tax_amount as domestic_total_tax,
          el.domestic_final_amount as domestic_final_amount,
          en.exchange_rate as profit_exchange_rate,
          hb.notify_party_label as other_partner_customer,
          hb.handling_agent_label as other_partner_agent,
          mb.carrier_label as other_partner_coloader,
          hb.notify_other_party_label as other_partner_3rd_partner,
          hb.sales_label as other_partner_sale_man,
          container.container_no as other_info_container_no,
          container.container_types as other_info_container_types,
          container.tues as other_info_container_tues,
          el.note as other_info_note_rate,
          hb.cargo_gross_weight as other_info_gw,
          hb.cargo_chargeable_weight as other_info_cw,
          hb.cargo_volume as other_info_cbm,
          hb.package_quantity as other_info_pkg_qty,
          hb.packaging_type as other_info_pkg_type,
          plan.arrival_time as other_info_eta,
          plan.depart_time as other_info_etd,
          plan.from_location_label as other_info_pol,
          plan.to_location_label as other_info_pod,
          null as other_info_credit_no,
          en.note as note
      FROM accounting_journal_entry_line el 
          LEFT JOIN accounting_journal_entry en 
              ON en.id  = el.entry_id 
          LEFT JOIN partner p
              ON p.id = en.partner_id 
              AND p.company_id = :company_id 
          LEFT JOIN account_account aa
              ON aa.id = p.id
          LEFT JOIN account_org_profile aop
              ON aop.login_id = aa.login_id 
          LEFT JOIN accounting_invoice ai 
              ON ai.id = el.invoice_id 
              AND ai.company_id = :company_id 
          LEFT JOIN accounting_invoice_link ail 
              ON ail.invoice_id = ai.id and ail.link_name = '${hb_table}'
          LEFT JOIN ${hb_table} hb 
              ON hb.id = ail.entity_id 
              AND hb.company_id = :company_id 
          LEFT JOIN lgc_mgmt_order_transport_plan plan
              ON hb.order_process_id  = plan.order_process_id 
              AND plan.company_id = :company_id 
          LEFT JOIN ${mb_table} mb 
              ON mb.id = hb.master_bill_id 
              AND mb.company_id = :company_id 
          LEFT JOIN lgc_mgmt_booking_process bp
              ON bp.id = hb.booking_process_id  
              AND bp.company_id = :company_id 
              
          -- Report số lượng container, tính số tues của lô hàng (theo Master Bill), concatenated by comma
          FULL JOIN (
              SELECT mb.id as master_bill_id,
                  mb.code as case_reference,
                  COUNT(*) as count,
                  STRING_AGG(t."label", ', ') AS container_no,
                  STRING_AGG(t.container_type, ', ') AS container_types,
                  CASE 
                      WHEN t.container_type LIKE '2%' THEN COUNT(t.id)
                      WHEN t.container_type LIKE '4%' THEN COUNT(t.id) * 2
                      ELSE NULL 
                  END AS tues
              FROM ${mb_table} mb
                  LEFT JOIN lgc_mgmt_trackable_container as t 
                      ON mb.code = t.case_reference 
                      AND mb.company_id =  t.company_id 
              WHERE mb.company_id = :company_id
                  GROUP BY mb.id, mb.code, t.container_type
          ) as container ON container.case_reference = el.case_reference 
          
      WHERE el."source" = 'original' AND en.company_id = :company_id
            ${domain} 
      """
}

buildQuery()
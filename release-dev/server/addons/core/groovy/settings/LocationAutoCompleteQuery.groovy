package settings;

/**
 * Search Locations
 * @return Location with Alias, type (sea, air, truck, ...) or locations by all types
 * NOTE:
 *  When a joined table isn’t used anywhere other than in the WHERE clause,
 *  it's equivalent to an EXISTS subquery, which often performs better.
 *  Reduced Data Transfer:
 *    EXISTS doesn't need to fetch and return all columns from secondary_table. It simply checks for the existence of a match
 *
 */

def buildQuery() {
  return """
    SELECT
        loc.id              AS id,
        loc.code            AS code,
        loc.short_label     AS short_label,
        loc.label           AS label,
        loc.location_type   AS location_type,
        loc.state_id        AS state_id,
        loc.state_label     AS state_label,
        loc.country_id      AS country_id,
        loc.country_label   AS country_label
    FROM settings_location loc 
    WHERE loc.storage_state = 'ACTIVE' 
        -- if code not empty, => compare loc.code = code 
        AND (COALESCE(:code, '') = '' OR loc.code = :code)
        AND (loc.code ILIKE :filter_val OR loc.short_label ILIKE CONCAT('%', :filter_val))
        AND (:all_type IS TRUE OR loc.location_type IN (:location_types))
        AND (
          COALESCE(:tags, '') = '' OR 
          -- use EXISTS pattern instead of JOIN (see the note above)
          EXISTS (
                SELECT 1 
                FROM settings_location_tag_rel rel 
                JOIN settings_location_tag loc_tag 
                        ON loc_tag.id = rel.location_tag_id 
                        AND loc_tag.type IN (:tags)
                WHERE rel.location_id = loc.id)) 
    GROUP BY loc.id 
    ORDER BY loc.code, loc.short_label ASC 
    LIMIT :max_return
  """;
}

buildQuery();
package settings;

/**
 * Search Locations by Coordinates
 */

static def buildQuery() {
  return """
    SELECT 
    loc.id                        AS id, 
    loc.code                      AS code, 
    loc.short_label               AS short_label, 
    loc.label                     AS label, 
    loc.address                   AS address, 
    loc.subdistrict_id            AS subdistrict_id, 
    loc.subdistrict_label         AS subdistrict_label, 
    loc.district_id               AS district_id, 
    loc.district_label            AS district_label, 
    loc.state_id                  AS state_id, 
    loc.state_label               AS state_label, 
    loc.country_id                AS country_id, 
    loc.country_label             AS country_label, 
    loc.postal_code               AS postal_code, 
    loc.location_type             AS location_type,
    loc.latitude                  AS latitude,
    loc.longitude                 AS longitude,
    6371 * acos(
        cos(radians(:latitude)) * cos(radians(loc.latitude)) *
        cos(radians(loc.longitude) - radians(:longitude)) +
        sin(radians(:latitude)) * sin(radians(loc.latitude))
    ) AS distance
    FROM settings_location loc
    WHERE loc.storage_state = 'ACTIVE'
      AND loc.location_type = :location_type
      ORDER BY distance ASC
    LIMIT 5
  """;
}

buildQuery();
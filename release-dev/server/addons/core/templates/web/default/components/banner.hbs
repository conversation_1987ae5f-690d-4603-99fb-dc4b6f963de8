<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
  <div class="container">
    <a class="navbar-brand" href="#!">Start Bootstrap</a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent"
      aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation"><span
        class="navbar-toggler-icon"></span></button>
    <div class="collapse navbar-collapse" id="navbarSupportedContent">
      <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
        <li class="nav-item"><a class="nav-link active" aria-current="page" href="#">Home</a></li>
        <li class="nav-item"><a class="nav-link" href="web/page/file:web:about.hbs">About</a></li>
        <li class="nav-item"><a class="nav-link" href="web/page/file:web:contact.hbs">Contact</a></li>
        <li class="nav-item"><a class="nav-link" href="web/page/file:web:blog.hbs">Blog</a></li>
      </ul>
    </div>
  </div>
  <script>
    var navlink = document.getElementsByClassName("nav-link");
    for (var i = 0; i < navlink.length; i++) {
      navlink[i].addEventListener("click", function() {
        var current = document.getElementsByClassName("active");
        current[0].className = current[0].className.replace(" active", "");
        this.className += " active";
      });
    }
  </script>
</nav>
/*! For license information please see host-293-1b90a6c1f8d5af085fb1.js.LICENSE.txt */
"use strict";(self.webpackChunkdatatp_ui_host=self.webpackChunkdatatp_ui_host||[]).push([[293],{533:(e,t,r)=>{function n(){return n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},n.apply(this,arguments)}var a;r.d(t,{Gh:()=>T,HS:()=>L,Oi:()=>l,Rr:()=>h,pX:()=>D,pb:()=>U,rc:()=>a,tH:()=>F,ue:()=>f,yD:()=>k,zR:()=>i}),function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(a||(a={}));const o="popstate";function i(e){return void 0===e&&(e={}),function(e,t,r,i){void 0===i&&(i={});let{window:s=document.defaultView,v5Compat:h=!1}=i,d=s.history,f=a.Pop,v=null,m=g();function g(){return(d.state||{idx:null}).idx}function y(){f=a.Pop;let e=g(),t=null==e?null:e-m;m=e,v&&v({action:f,location:x.location,delta:t})}function b(e){let t="null"!==s.location.origin?s.location.origin:s.location.href,r="string"==typeof e?e:p(e);return r=r.replace(/ $/,"%20"),l(t,"No window.location.(origin|href) available to create URL for href: "+r),new URL(r,t)}null==m&&(m=0,d.replaceState(n({},d.state,{idx:m}),""));let x={get action(){return f},get location(){return e(s,d)},listen(e){if(v)throw new Error("A history only accepts one active listener");return s.addEventListener(o,y),v=e,()=>{s.removeEventListener(o,y),v=null}},createHref:e=>t(s,e),createURL:b,encodeLocation(e){let t=b(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){f=a.Push;let n=c(x.location,e,t);r&&r(n,e),m=g()+1;let o=u(n,m),i=x.createHref(n);try{d.pushState(o,"",i)}catch(e){if(e instanceof DOMException&&"DataCloneError"===e.name)throw e;s.location.assign(i)}h&&v&&v({action:f,location:x.location,delta:1})},replace:function(e,t){f=a.Replace;let n=c(x.location,e,t);r&&r(n,e),m=g();let o=u(n,m),i=x.createHref(n);d.replaceState(o,"",i),h&&v&&v({action:f,location:x.location,delta:0})},go:e=>d.go(e)};return x}(function(e,t){let{pathname:r,search:n,hash:a}=e.location;return c("",{pathname:r,search:n,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"==typeof t?t:p(t)},null,e)}function l(e,t){if(!1===e||null==e)throw new Error(t)}function s(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw new Error(t)}catch(e){}}}function u(e,t){return{usr:e.state,key:e.key,idx:t}}function c(e,t,r,a){return void 0===r&&(r=null),n({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?h(t):t,{state:r,key:t&&t.key||a||Math.random().toString(36).substr(2,8)})}function p(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&"?"!==r&&(t+="?"===r.charAt(0)?r:"?"+r),n&&"#"!==n&&(t+="#"===n.charAt(0)?n:"#"+n),t}function h(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}var d;function f(e,t,r){return void 0===r&&(r="/"),function(e,t,r,n){let a=U(("string"==typeof t?h(t):t).pathname||"/",r);if(null==a)return null;let o=v(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){return e.length===t.length&&e.slice(0,-1).every((e,r)=>e===t[r])?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(o);let i=null;for(let e=0;null==i&&e<o.length;++e){let t=O(a);i=R(o[e],t,n)}return i}(e,t,r,!1)}function v(e,t,r,n){void 0===t&&(t=[]),void 0===r&&(r=[]),void 0===n&&(n="");let a=(e,a,o)=>{let i={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};i.relativePath.startsWith("/")&&(l(i.relativePath.startsWith(n),'Absolute route path "'+i.relativePath+'" nested under path "'+n+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),i.relativePath=i.relativePath.slice(n.length));let s=L([n,i.relativePath]),u=r.concat(i);e.children&&e.children.length>0&&(l(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+s+'".'),v(e.children,t,u,s)),(null!=e.path||e.index)&&t.push({path:s,score:C(s,e.index),routesMeta:u})};return e.forEach((e,t)=>{var r;if(""!==e.path&&null!=(r=e.path)&&r.includes("?"))for(let r of m(e.path))a(e,t,r);else a(e,t)}),t}function m(e){let t=e.split("/");if(0===t.length)return[];let[r,...n]=t,a=r.endsWith("?"),o=r.replace(/\?$/,"");if(0===n.length)return a?[o,""]:[o];let i=m(n.join("/")),l=[];return l.push(...i.map(e=>""===e?o:[o,e].join("/"))),a&&l.push(...i),l.map(t=>e.startsWith("/")&&""===t?"/":t)}!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(d||(d={})),new Set(["lazy","caseSensitive","path","id","index","children"]);const g=/^:[\w-]+$/,y=3,b=2,x=1,w=10,S=-2,E=e=>"*"===e;function C(e,t){let r=e.split("/"),n=r.length;return r.some(E)&&(n+=S),t&&(n+=b),r.filter(e=>!E(e)).reduce((e,t)=>e+(g.test(t)?y:""===t?x:w),n)}function R(e,t,r){void 0===r&&(r=!1);let{routesMeta:n}=e,a={},o="/",i=[];for(let e=0;e<n.length;++e){let l=n[e],s=e===n.length-1,u="/"===o?t:t.slice(o.length)||"/",c=P({path:l.relativePath,caseSensitive:l.caseSensitive,end:s},u),p=l.route;if(!c&&s&&r&&!n[n.length-1].route.index&&(c=P({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},u)),!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:L([o,c.pathname]),pathnameBase:B(L([o,c.pathnameBase])),route:p}),"/"!==c.pathnameBase&&(o=L([o,c.pathnameBase]))}return i}function P(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=function(e,t,r){void 0===t&&(t=!1),void 0===r&&(r=!0),s("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let n=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,r)=>(n.push({paramName:t,isOptional:null!=r}),r?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),n]}(e.path,e.caseSensitive,e.end),a=t.match(r);if(!a)return null;let o=a[0],i=o.replace(/(.)\/+$/,"$1"),l=a.slice(1);return{params:n.reduce((e,t,r)=>{let{paramName:n,isOptional:a}=t;if("*"===n){let e=l[r]||"";i=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const s=l[r];return e[n]=a&&!s?void 0:(s||"").replace(/%2F/g,"/"),e},{}),pathname:o,pathnameBase:i,pattern:e}}function O(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return s(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function U(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&"/"!==n?null:e.slice(r)||"/"}function _(e,t,r,n){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(n)+"].  Please separate it out to the `to."+r+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function k(e,t){let r=function(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}(e);return t?r.map((e,t)=>t===r.length-1?e.pathname:e.pathnameBase):r.map(e=>e.pathnameBase)}function T(e,t,r,a){let o;void 0===a&&(a=!1),"string"==typeof e?o=h(e):(o=n({},e),l(!o.pathname||!o.pathname.includes("?"),_("?","pathname","search",o)),l(!o.pathname||!o.pathname.includes("#"),_("#","pathname","hash",o)),l(!o.search||!o.search.includes("#"),_("#","search","hash",o)));let i,s=""===e||""===o.pathname,u=s?"/":o.pathname;if(null==u)i=r;else{let e=t.length-1;if(!a&&u.startsWith("..")){let t=u.split("/");for(;".."===t[0];)t.shift(),e-=1;o.pathname=t.join("/")}i=e>=0?t[e]:"/"}let c=function(e,t){void 0===t&&(t="/");let{pathname:r,search:n="",hash:a=""}="string"==typeof e?h(e):e,o=r?r.startsWith("/")?r:function(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?r.length>1&&r.pop():"."!==e&&r.push(e)}),r.length>1?r.join("/"):"/"}(r,t):t;return{pathname:o,search:M(n),hash:N(a)}}(o,i),p=u&&"/"!==u&&u.endsWith("/"),d=(s||"."===u)&&r.endsWith("/");return c.pathname.endsWith("/")||!p&&!d||(c.pathname+="/"),c}const L=e=>e.join("/").replace(/\/\/+/g,"/"),B=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),M=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",N=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";class F extends Error{}function D(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}const j=["post","put","patch","delete"],I=(new Set(j),["get",...j]);new Set(I),new Set([301,302,303,307,308]),new Set([307,308]),Symbol("deferred")},548:e=>{e.exports=function(e){var t=[];return t.toString=function(){return this.map(function(t){var r="",n=void 0!==t[5];return t[4]&&(r+="@supports (".concat(t[4],") {")),t[2]&&(r+="@media ".concat(t[2]," {")),n&&(r+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),r+=e(t),n&&(r+="}"),t[2]&&(r+="}"),t[4]&&(r+="}"),r}).join("")},t.i=function(e,r,n,a,o){"string"==typeof e&&(e=[[null,e,void 0]]);var i={};if(n)for(var l=0;l<this.length;l++){var s=this[l][0];null!=s&&(i[s]=!0)}for(var u=0;u<e.length;u++){var c=[].concat(e[u]);n&&i[c[0]]||(void 0!==o&&(void 0===c[5]||(c[1]="@layer".concat(c[5].length>0?" ".concat(c[5]):""," {").concat(c[1],"}")),c[5]=o),r&&(c[2]?(c[1]="@media ".concat(c[2]," {").concat(c[1],"}"),c[2]=r):c[2]=r),a&&(c[4]?(c[1]="@supports (".concat(c[4],") {").concat(c[1],"}"),c[4]=a):c[4]="".concat(a)),t.push(c))}},t}},2107:e=>{e.exports=function(e){return e[1]}},7069:(e,t,r)=>{var n=r(9236);t.H=n.createRoot,n.hydrateRoot},8951:(e,t,r)=>{var n,a;r.d(t,{Kd:()=>c});var o=r(1027),i=r(9236),l=r(2177),s=r(533);new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);try{window.__reactRouterVersion="6"}catch(e){}new Map;const u=(n||(n=r.t(o,2))).startTransition;function c(e){let{basename:t,children:r,future:n,window:a}=e,i=o.useRef();null==i.current&&(i.current=(0,s.zR)({window:a,v5Compat:!0}));let c=i.current,[p,h]=o.useState({action:c.action,location:c.location}),{v7_startTransition:d}=n||{},f=o.useCallback(e=>{d&&u?u(()=>h(e)):h(e)},[h,d]);return o.useLayoutEffect(()=>c.listen(f),[c,f]),o.useEffect(()=>(0,l.V8)(n),[n]),o.createElement(l.Ix,{basename:t,children:r,location:p.location,navigationType:p.action,navigator:c,future:n})}var p,h;(a||(a=r.t(i,2))).flushSync,(n||(n=r.t(o,2))).useId,"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement,function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"}(p||(p={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(h||(h={}))},2177:(e,t,r)=>{var n;r.d(t,{BV:()=>L,C5:()=>_,Ix:()=>T,V8:()=>U,Zp:()=>m,g:()=>g,qh:()=>k,zy:()=>f});var a=r(1027),o=r(533);function i(){return i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},i.apply(this,arguments)}const l=a.createContext(null),s=a.createContext(null),u=a.createContext(null),c=a.createContext(null),p=a.createContext({outlet:null,matches:[],isDataRoute:!1}),h=a.createContext(null);function d(){return null!=a.useContext(c)}function f(){return d()||(0,o.Oi)(!1),a.useContext(c).location}function v(e){a.useContext(u).static||a.useLayoutEffect(e)}function m(){let{isDataRoute:e}=a.useContext(p);return e?function(){let{router:e}=function(){let e=a.useContext(l);return e||(0,o.Oi)(!1),e}(E.UseNavigateStable),t=R(C.UseNavigateStable),r=a.useRef(!1);return v(()=>{r.current=!0}),a.useCallback(function(n,a){void 0===a&&(a={}),r.current&&("number"==typeof n?e.navigate(n):e.navigate(n,i({fromRouteId:t},a)))},[e,t])}():function(){d()||(0,o.Oi)(!1);let e=a.useContext(l),{basename:t,future:r,navigator:n}=a.useContext(u),{matches:i}=a.useContext(p),{pathname:s}=f(),c=JSON.stringify((0,o.yD)(i,r.v7_relativeSplatPath)),h=a.useRef(!1);return v(()=>{h.current=!0}),a.useCallback(function(r,a){if(void 0===a&&(a={}),!h.current)return;if("number"==typeof r)return void n.go(r);let i=(0,o.Gh)(r,JSON.parse(c),s,"path"===a.relative);null==e&&"/"!==t&&(i.pathname="/"===i.pathname?t:(0,o.HS)([t,i.pathname])),(a.replace?n.replace:n.push)(i,a.state,a)},[t,n,c,s,e])}()}function g(){let{matches:e}=a.useContext(p),t=e[e.length-1];return t?t.params:{}}function y(e,t,r,n){d()||(0,o.Oi)(!1);let{navigator:l,static:s}=a.useContext(u),{matches:h}=a.useContext(p),v=h[h.length-1],m=v?v.params:{},g=(v&&v.pathname,v?v.pathnameBase:"/");v&&v.route;let y,b=f();if(t){var E;let e="string"==typeof t?(0,o.Rr)(t):t;"/"===g||(null==(E=e.pathname)?void 0:E.startsWith(g))||(0,o.Oi)(!1),y=e}else y=b;let C=y.pathname||"/",R=C;if("/"!==g){let e=g.replace(/^\//,"").split("/");R="/"+C.replace(/^\//,"").split("/").slice(e.length).join("/")}let O=!s&&r&&r.matches&&r.matches.length>0?r.matches:(0,o.ue)(e,{pathname:R}),U=function(e,t,r,n){var i;if(void 0===t&&(t=[]),void 0===r&&(r=null),void 0===n&&(n=null),null==e){var l;if(!r)return null;if(r.errors)e=r.matches;else{if(!(null!=(l=n)&&l.v7_partialHydration&&0===t.length&&!r.initialized&&r.matches.length>0))return null;e=r.matches}}let s=e,u=null==(i=r)?void 0:i.errors;if(null!=u){let e=s.findIndex(e=>e.route.id&&void 0!==(null==u?void 0:u[e.route.id]));e>=0||(0,o.Oi)(!1),s=s.slice(0,Math.min(s.length,e+1))}let c=!1,p=-1;if(r&&n&&n.v7_partialHydration)for(let e=0;e<s.length;e++){let t=s[e];if((t.route.HydrateFallback||t.route.hydrateFallbackElement)&&(p=e),t.route.id){let{loaderData:e,errors:n}=r,a=t.route.loader&&void 0===e[t.route.id]&&(!n||void 0===n[t.route.id]);if(t.route.lazy||a){c=!0,s=p>=0?s.slice(0,p+1):[s[0]];break}}}return s.reduceRight((e,n,o)=>{let i,l=!1,h=null,d=null;var f;r&&(i=u&&n.route.id?u[n.route.id]:void 0,h=n.route.errorElement||x,c&&(p<0&&0===o?(P[f="route-fallback"]||(P[f]=!0),l=!0,d=null):p===o&&(l=!0,d=n.route.hydrateFallbackElement||null)));let v=t.concat(s.slice(0,o+1)),m=()=>{let t;return t=i?h:l?d:n.route.Component?a.createElement(n.route.Component,null):n.route.element?n.route.element:e,a.createElement(S,{match:n,routeContext:{outlet:e,matches:v,isDataRoute:null!=r},children:t})};return r&&(n.route.ErrorBoundary||n.route.errorElement||0===o)?a.createElement(w,{location:r.location,revalidation:r.revalidation,component:h,error:i,children:m(),routeContext:{outlet:null,matches:v,isDataRoute:!0}}):m()},null)}(O&&O.map(e=>Object.assign({},e,{params:Object.assign({},m,e.params),pathname:(0,o.HS)([g,l.encodeLocation?l.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?g:(0,o.HS)([g,l.encodeLocation?l.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),h,r,n);return t&&U?a.createElement(c.Provider,{value:{location:i({pathname:"/",search:"",hash:"",state:null,key:"default"},y),navigationType:o.rc.Pop}},U):U}function b(){let e=function(){var e;let t=a.useContext(h),r=function(){let e=a.useContext(s);return e||(0,o.Oi)(!1),e}(C.UseRouteError),n=R(C.UseRouteError);return void 0!==t?t:null==(e=r.errors)?void 0:e[n]}(),t=(0,o.pX)(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,n={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return a.createElement(a.Fragment,null,a.createElement("h2",null,"Unexpected Application Error!"),a.createElement("h3",{style:{fontStyle:"italic"}},t),r?a.createElement("pre",{style:n},r):null,null)}const x=a.createElement(b,null);class w extends a.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?a.createElement(p.Provider,{value:this.props.routeContext},a.createElement(h.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function S(e){let{routeContext:t,match:r,children:n}=e,o=a.useContext(l);return o&&o.static&&o.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=r.route.id),a.createElement(p.Provider,{value:t},n)}var E=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(E||{}),C=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(C||{});function R(e){let t=function(){let e=a.useContext(p);return e||(0,o.Oi)(!1),e}(),r=t.matches[t.matches.length-1];return r.route.id||(0,o.Oi)(!1),r.route.id}const P={},O=(e,t,r)=>{};function U(e,t){void 0===(null==e?void 0:e.v7_startTransition)&&O("v7_startTransition","React Router will begin wrapping state updates in `React.startTransition` in v7","https://reactrouter.com/v6/upgrading/future#v7_starttransition"),void 0!==(null==e?void 0:e.v7_relativeSplatPath)||t&&t.v7_relativeSplatPath||O("v7_relativeSplatPath","Relative route resolution within Splat routes is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath"),t&&(void 0===t.v7_fetcherPersist&&O("v7_fetcherPersist","The persistence behavior of fetchers is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_fetcherpersist"),void 0===t.v7_normalizeFormMethod&&O("v7_normalizeFormMethod","Casing of `formMethod` fields is being normalized to uppercase in v7","https://reactrouter.com/v6/upgrading/future#v7_normalizeformmethod"),void 0===t.v7_partialHydration&&O("v7_partialHydration","`RouterProvider` hydration behavior is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_partialhydration"),void 0===t.v7_skipActionErrorRevalidation&&O("v7_skipActionErrorRevalidation","The revalidation behavior after 4xx/5xx `action` responses is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_skipactionerrorrevalidation"))}function _(e){let{to:t,replace:r,state:n,relative:i}=e;d()||(0,o.Oi)(!1);let{future:l,static:s}=a.useContext(u),{matches:c}=a.useContext(p),{pathname:h}=f(),v=m(),g=(0,o.Gh)(t,(0,o.yD)(c,l.v7_relativeSplatPath),h,"path"===i),y=JSON.stringify(g);return a.useEffect(()=>v(JSON.parse(y),{replace:r,state:n,relative:i}),[v,y,i,r,n]),null}function k(e){(0,o.Oi)(!1)}function T(e){let{basename:t="/",children:r=null,location:n,navigationType:l=o.rc.Pop,navigator:s,static:p=!1,future:h}=e;d()&&(0,o.Oi)(!1);let f=t.replace(/^\/*/,"/"),v=a.useMemo(()=>({basename:f,navigator:s,static:p,future:i({v7_relativeSplatPath:!1},h)}),[f,h,s,p]);"string"==typeof n&&(n=(0,o.Rr)(n));let{pathname:m="/",search:g="",hash:y="",state:b=null,key:x="default"}=n,w=a.useMemo(()=>{let e=(0,o.pb)(m,f);return null==e?null:{location:{pathname:e,search:g,hash:y,state:b,key:x},navigationType:l}},[f,m,g,y,b,x,l]);return null==w?null:a.createElement(u.Provider,{value:v},a.createElement(c.Provider,{children:r,value:w}))}function L(e){let{children:t,location:r}=e;return y(B(t),r)}function B(e,t){void 0===t&&(t=[]);let r=[];return a.Children.forEach(e,(e,n)=>{if(!a.isValidElement(e))return;let i=[...t,n];if(e.type===a.Fragment)return void r.push.apply(r,B(e.props.children,i));e.type!==k&&(0,o.Oi)(!1),e.props.index&&e.props.children&&(0,o.Oi)(!1);let l={id:e.props.id||i.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(l.children=B(e.props.children,i)),r.push(l)}),r}(n||(n=r.t(a,2))).startTransition,new Promise(()=>{}),a.Component},3972:e=>{var t=[];function r(e){for(var r=-1,n=0;n<t.length;n++)if(t[n].identifier===e){r=n;break}return r}function n(e,n){for(var o={},i=[],l=0;l<e.length;l++){var s=e[l],u=n.base?s[0]+n.base:s[0],c=o[u]||0,p="".concat(u," ").concat(c);o[u]=c+1;var h=r(p),d={css:s[1],media:s[2],sourceMap:s[3],supports:s[4],layer:s[5]};if(-1!==h)t[h].references++,t[h].updater(d);else{var f=a(d,n);n.byIndex=l,t.splice(l,0,{identifier:p,updater:f,references:1})}i.push(p)}return i}function a(e,t){var r=t.domAPI(t);return r.update(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;r.update(e=t)}else r.remove()}}e.exports=function(e,a){var o=n(e=e||[],a=a||{});return function(e){e=e||[];for(var i=0;i<o.length;i++){var l=r(o[i]);t[l].references--}for(var s=n(e,a),u=0;u<o.length;u++){var c=r(o[u]);0===t[c].references&&(t[c].updater(),t.splice(c,1))}o=s}}},6511:e=>{var t={};e.exports=function(e,r){var n=function(e){if(void 0===t[e]){var r=document.querySelector(e);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(e){r=null}t[e]=r}return t[e]}(e);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(r)}},7800:e=>{e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},8300:(e,t,r)=>{e.exports=function(e){var t=r.nc;t&&e.setAttribute("nonce",t)}},5005:e=>{e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=e.insertStyleElement(e);return{update:function(r){!function(e,t,r){var n="";r.supports&&(n+="@supports (".concat(r.supports,") {")),r.media&&(n+="@media ".concat(r.media," {"));var a=void 0!==r.layer;a&&(n+="@layer".concat(r.layer.length>0?" ".concat(r.layer):""," {")),n+=r.css,a&&(n+="}"),r.media&&(n+="}"),r.supports&&(n+="}");var o=r.sourceMap;o&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(o))))," */")),t.styleTagTransform(n,e,t.options)}(t,e,r)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},8053:e=>{e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}}}]);
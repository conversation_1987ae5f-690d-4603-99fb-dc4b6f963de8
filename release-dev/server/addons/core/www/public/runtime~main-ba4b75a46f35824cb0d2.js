(()=>{"use strict";var e,t,r,o,n,a={},i={};function l(e){var t=i[e];if(void 0!==t)return t.exports;var r=i[e]={id:e,loaded:!1,exports:{}};return a[e].call(r.exports,r,r.exports,l),r.loaded=!0,r.exports}l.m=a,e=[],l.O=(t,r,o,n)=>{if(!r){var a=1/0;for(c=0;c<e.length;c++){for(var[r,o,n]=e[c],i=!0,u=0;u<r.length;u++)(!1&n||a>=n)&&Object.keys(l.O).every(e=>l.O[e](r[u]))?r.splice(u--,1):(i=!1,n<a&&(a=n));if(i){e.splice(c--,1);var d=o();void 0!==d&&(t=d)}}return t}n=n||0;for(var c=e.length;c>0&&e[c-1][2]>n;c--)e[c]=e[c-1];e[c]=[r,o,n]},l.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return l.d(t,{a:t}),t},r=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,l.t=function(e,o){if(1&o&&(e=this(e)),8&o)return e;if("object"==typeof e&&e){if(4&o&&e.__esModule)return e;if(16&o&&"function"==typeof e.then)return e}var n=Object.create(null);l.r(n);var a={};t=t||[null,r({}),r([]),r(r)];for(var i=2&o&&e;"object"==typeof i&&!~t.indexOf(i);i=r(i))Object.getOwnPropertyNames(i).forEach(t=>a[t]=()=>e[t]);return a.default=()=>e,l.d(n,a),n},l.d=(e,t)=>{for(var r in t)l.o(t,r)&&!l.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},l.f={},l.e=e=>Promise.all(Object.keys(l.f).reduce((t,r)=>(l.f[r](e,t),t),[])),l.u=e=>e+"-"+{509:"74717775229133cba321",531:"18f78f67edcb4b56cbcf",725:"64850f3876d039ea9082"}[e]+".js",l.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),l.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o={},n="datatp_ui_host:",l.l=(e,t,r,a)=>{if(o[e])o[e].push(t);else{var i,u;if(void 0!==r)for(var d=document.getElementsByTagName("script"),c=0;c<d.length;c++){var f=d[c];if(f.getAttribute("src")==e||f.getAttribute("data-webpack")==n+r){i=f;break}}i||(u=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,l.nc&&i.setAttribute("nonce",l.nc),i.setAttribute("data-webpack",n+r),i.src=e),o[e]=[t];var s=(t,r)=>{i.onerror=i.onload=null,clearTimeout(p);var n=o[e];if(delete o[e],i.parentNode&&i.parentNode.removeChild(i),n&&n.forEach(e=>e(r)),t)return t(r)},p=setTimeout(s.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=s.bind(null,i.onerror),i.onload=s.bind(null,i.onload),u&&document.head.appendChild(i)}},l.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},l.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),l.p="/",(()=>{var e={354:0};l.f.j=(t,r)=>{var o=l.o(e,t)?e[t]:void 0;if(0!==o)if(o)r.push(o[2]);else if(354!=t){var n=new Promise((r,n)=>o=e[t]=[r,n]);r.push(o[2]=n);var a=l.p+l.u(t),i=new Error;l.l(a,r=>{if(l.o(e,t)&&(0!==(o=e[t])&&(e[t]=void 0),o)){var n=r&&("load"===r.type?"missing":r.type),a=r&&r.target&&r.target.src;i.message="Loading chunk "+t+" failed.\n("+n+": "+a+")",i.name="ChunkLoadError",i.type=n,i.request=a,o[1](i)}},"chunk-"+t,t)}else e[t]=0},l.O.j=t=>0===e[t];var t=(t,r)=>{var o,n,[a,i,u]=r,d=0;if(a.some(t=>0!==e[t])){for(o in i)l.o(i,o)&&(l.m[o]=i[o]);if(u)var c=u(l)}for(t&&t(r);d<a.length;d++)n=a[d],l.o(e,n)&&e[n]&&e[n][0](),e[n]=0;return l.O(c)},r=self.webpackChunkdatatp_ui_host=self.webpackChunkdatatp_ui_host||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),l.nc=void 0})();
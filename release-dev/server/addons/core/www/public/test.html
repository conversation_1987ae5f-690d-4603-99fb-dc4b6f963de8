<!DOCTYPE html>
<html lang="en" data-navigation-type="default" data-navbar-horizontal-shape="slim">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=0">
  <meta name="theme-color" content="#ffffff">

  <!-- ===============================================-->
  <!--    Document Title-->
  <!-- ===============================================-->
  <title id="page-title">WSS</title>
</head>

<body>
  Hello

  <script>
    var wsUrl = "wss://datatp-1-proxy.dev.datatp.net/websocket/channel"
    var websocket = new WebSocket(wsUrl);
    websocket.addEventListener("open", (evt) => {
      console.log(`WebSocket: open`);
      console.log(evt)
    });

    websocket.addEventListener("close", (evt) => {
      console.log(`WebSocket: close`);
      console.log(evt)
    });

    websocket.addEventListener("error", (evt) => {
      console.log('Error:');
      console.log(evt);
    });
  </script>
</body>

</html>
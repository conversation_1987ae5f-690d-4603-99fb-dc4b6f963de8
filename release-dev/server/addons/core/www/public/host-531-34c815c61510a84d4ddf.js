"use strict";(self.webpackChunkdatatp_ui_host=self.webpackChunkdatatp_ui_host||[]).push([[531],{7069:(t,e,o)=>{var n=o(4532);e.H=n.createRoot,n.hydrateRoot},6531:(t,e,o)=>{o.r(e),o.d(e,{configureEmbeddedRoute:()=>h});var n=o(2815),r=o.n(n),s=o(7069),a=o(2198);const p=a.app.host.DATATP_SESSION;class d extends a.app.HostAppContext{broadcast(t){this.event=t}}class i extends r().Component{componentDidMount(){if(!p.isAuthenticated()){let{token:t}=this.props,e=t=>{this.forceUpdate()};console.log("Signin with odoo token"),p.signin("odoo",t,e)}}componentWillUnmount(){}getHostAppContext(){throw new Error("Method not implemented.")}handle(t){}render(){if(!p.isAuthenticated())return r().createElement("div",null," Odoo Embedded App");let{appId:t}=this.props;const e=a.app.host.CONFIG;let o=(new a.app.host.UserAppFeatureManager).get(t,!1),n=e.createServerContext(),s=new d(this,n);return r().createElement("div",{className:"light-theme flex-vbox"},o.createUI(s))}}class c{root;renderApp(t,e){const o=document.getElementById("datatp-ui");if(!o)throw new Error("Cannot find block with id datatp-ui");{const n=(0,s.H)(o);n.render(r().createElement(i,{appId:t,token:e})),this.root=n}}destroyApp(){this.root&&(this.root.unmount(),this.root=null)}}function h(){window.DATATP_UI.factory||(window.DATATP_UI.factory=new c)}}}]);
/**
 * TinyMCE version 6.1.2 (2022-07-29)
 */
!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.ModelManager");const t=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&(o=n=e,(r=String).prototype.isPrototypeOf(o)||(null===(s=n.constructor)||void 0===s?void 0:s.name)===r.name)?"string":t;var o,n,r,s})(t)===e,o=e=>t=>typeof t===e,n=t("string"),r=t("object"),s=t("array"),l=(null,e=>null===e);const a=o("boolean"),c=e=>!(e=>null==e)(e),i=o("function"),m=o("number"),d=()=>{},u=e=>()=>e,f=e=>e,g=(e,t)=>e===t;function h(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const p=e=>t=>!e(t),w=e=>e(),b=u(!1),v=u(!0);class y{constructor(e,t){this.tag=e,this.value=t}static some(e){return new y(!0,e)}static none(){return y.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?y.some(e(this.value)):y.none()}bind(e){return this.tag?e(this.value):y.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:y.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return c(e)?y.some(e):y.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}y.singletonNone=new y(!1);const x=Array.prototype.slice,C=Array.prototype.indexOf,S=Array.prototype.push,T=(e,t)=>((e,t)=>C.call(e,t))(e,t)>-1,R=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},D=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},O=(e,t)=>{const o=e.length,n=new Array(o);for(let r=0;r<o;r++){const o=e[r];n[r]=t(o,r)}return n},k=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},E=(e,t)=>{const o=[],n=[];for(let r=0,s=e.length;r<s;r++){const s=e[r];(t(s,r)?o:n).push(s)}return{pass:o,fail:n}},N=(e,t)=>{const o=[];for(let n=0,r=e.length;n<r;n++){const r=e[n];t(r,n)&&o.push(r)}return o},B=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),z=(e,t,o)=>(k(e,((e,n)=>{o=t(o,e,n)})),o),A=(e,t)=>((e,t,o)=>{for(let n=0,r=e.length;n<r;n++){const r=e[n];if(t(r,n))return y.some(r);if(o(r,n))break}return y.none()})(e,t,b),W=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return y.some(o);return y.none()},L=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!s(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);S.apply(t,e[o])}return t},_=(e,t)=>L(O(e,t)),M=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},j=(e,t)=>{const o={};for(let n=0,r=e.length;n<r;n++){const r=e[n];o[String(r)]=t(r,n)}return o},I=(e,t)=>t>=0&&t<e.length?y.some(e[t]):y.none(),P=e=>I(e,0),F=e=>I(e,e.length-1),H=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return y.none()},q=Object.keys,V=Object.hasOwnProperty,$=(e,t)=>{const o=q(e);for(let n=0,r=o.length;n<r;n++){const r=o[n];t(e[r],r)}},U=(e,t)=>G(e,((e,o)=>({k:o,v:t(e,o)}))),G=(e,t)=>{const o={};return $(e,((e,n)=>{const r=t(e,n);o[r.k]=r.v})),o},K=(e,t)=>{const o=[];return $(e,((e,n)=>{o.push(t(e,n))})),o},Y=e=>K(e,f),J=(e,t)=>V.call(e,t);"undefined"!=typeof window?window:Function("return this;")();const Q=e=>e.dom.nodeName.toLowerCase(),X=e=>e.dom.nodeType,Z=e=>t=>X(t)===e,ee=e=>8===X(e)||"#comment"===Q(e),te=Z(1),oe=Z(3),ne=Z(9),re=Z(11),se=e=>t=>te(t)&&Q(t)===e,le=(e,t,o)=>{if(!(n(o)||a(o)||m(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},ae=(e,t,o)=>{le(e.dom,t,o)},ce=(e,t)=>{const o=e.dom;$(t,((e,t)=>{le(o,t,e)}))},ie=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},me=(e,t)=>y.from(ie(e,t)),de=(e,t)=>{e.dom.removeAttribute(t)},ue=e=>z(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}),fe=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},ge={fromHtml:(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return fe(o.childNodes[0])},fromTag:(e,t)=>{const o=(t||document).createElement(e);return fe(o)},fromText:(e,t)=>{const o=(t||document).createTextNode(e);return fe(o)},fromDom:fe,fromPoint:(e,t,o)=>y.from(e.dom.elementFromPoint(t,o)).map(fe)},he=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},pe=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,we=(e,t)=>{const o=void 0===t?document:t.dom;return pe(o)?y.none():y.from(o.querySelector(e)).map(ge.fromDom)},be=(e,t)=>e.dom===t.dom,ve=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},ye=he,xe=e=>ge.fromDom(e.dom.ownerDocument),Ce=e=>ne(e)?e:xe(e),Se=e=>y.from(e.dom.parentNode).map(ge.fromDom),Te=(e,t)=>{const o=i(t)?t:b;let n=e.dom;const r=[];for(;null!==n.parentNode&&void 0!==n.parentNode;){const e=n.parentNode,t=ge.fromDom(e);if(r.push(t),!0===o(t))break;n=e}return r},Re=e=>y.from(e.dom.previousSibling).map(ge.fromDom),De=e=>y.from(e.dom.nextSibling).map(ge.fromDom),Oe=e=>O(e.dom.childNodes,ge.fromDom),ke=(e,t)=>{const o=e.dom.childNodes;return y.from(o[t]).map(ge.fromDom)},Ee=(e,t)=>{Se(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},Ne=(e,t)=>{De(e).fold((()=>{Se(e).each((e=>{ze(e,t)}))}),(e=>{Ee(e,t)}))},Be=(e,t)=>{const o=(e=>ke(e,0))(e);o.fold((()=>{ze(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},ze=(e,t)=>{e.dom.appendChild(t.dom)},Ae=(e,t)=>{Ee(e,t),ze(t,e)},We=(e,t)=>{k(t,((o,n)=>{const r=0===n?e:t[n-1];Ne(r,o)}))},Le=(e,t)=>{k(t,(t=>{ze(e,t)}))},_e=e=>{e.dom.textContent="",k(Oe(e),(e=>{Me(e)}))},Me=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},je=e=>{const t=Oe(e);t.length>0&&We(e,t),Me(e)},Ie=(e,t)=>ge.fromDom(e.dom.cloneNode(t)),Pe=e=>Ie(e,!1),Fe=e=>Ie(e,!0),He=(e,t)=>{const o=ge.fromTag(t),n=ue(e);return ce(o,n),o},qe=["tfoot","thead","tbody","colgroup"],Ve=(e,t,o)=>({element:e,rowspan:t,colspan:o}),$e=(e,t,o)=>({element:e,cells:t,section:o}),Ue=(e,t,o)=>({element:e,isNew:t,isLocked:o}),Ge=(e,t,o,n)=>({element:e,cells:t,section:o,isNew:n}),Ke=i(Element.prototype.attachShadow)&&i(Node.prototype.getRootNode),Ye=u(Ke),Je=Ke?e=>ge.fromDom(e.dom.getRootNode()):Ce,Qe=e=>ge.fromDom(e.dom.host),Xe=e=>{const t=oe(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return(e=>{const t=Je(e);return re(o=t)&&c(o.dom.host)?y.some(t):y.none();var o})(ge.fromDom(t)).fold((()=>o.body.contains(t)),(n=Xe,r=Qe,e=>n(r(e))));var n,r},Ze=e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return ge.fromDom(t)},et=(e,t)=>{let o=[];return k(Oe(e),(e=>{t(e)&&(o=o.concat([e])),o=o.concat(et(e,t))})),o},tt=(e,t,o)=>((e,o,n)=>N(Te(e,n),(e=>he(e,t))))(e,0,o),ot=(e,t)=>((e,o)=>N(Oe(e),(e=>he(e,t))))(e),nt=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return pe(o)?[]:O(o.querySelectorAll(e),ge.fromDom)})(t,e);var rt=(e,t,o,n,r)=>e(o,n)?y.some(o):i(r)&&r(o)?y.none():t(o,n,r);const st=(e,t,o)=>{let n=e.dom;const r=i(o)?o:b;for(;n.parentNode;){n=n.parentNode;const e=ge.fromDom(n);if(t(e))return y.some(e);if(r(e))break}return y.none()},lt=(e,t,o)=>st(e,(e=>he(e,t)),o),at=(e,t)=>((e,o)=>A(e.dom.childNodes,(e=>{return o=ge.fromDom(e),he(o,t);var o})).map(ge.fromDom))(e),ct=(e,t)=>we(t,e),it=(e,t,o)=>rt(((e,t)=>he(e,t)),lt,e,t,o),mt=(e,t,o=g)=>e.exists((e=>o(e,t))),dt=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},ut=(e,t)=>e?y.some(t):y.none(),ft=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,gt=(e,t)=>-1!==e.indexOf(t),ht=(e,t)=>ft(e,t,0),pt=(e,t)=>ft(e,t,e.length-t.length),wt=(e=>t=>t.replace(e,""))(/^\s+|\s+$/g),bt=e=>e.length>0,vt=e=>void 0!==e.style&&i(e.style.getPropertyValue),yt=(e,t,o)=>{if(!n(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);vt(e)&&e.style.setProperty(t,o)},xt=(e,t,o)=>{const n=e.dom;yt(n,t,o)},Ct=(e,t)=>{const o=e.dom;$(t,((e,t)=>{yt(o,t,e)}))},St=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||Xe(e)?n:Tt(o,t)},Tt=(e,t)=>vt(e)?e.style.getPropertyValue(t):"",Rt=(e,t)=>{const o=e.dom,n=Tt(o,t);return y.from(n).filter((e=>e.length>0))},Dt=(e,t)=>{((e,t)=>{vt(e)&&e.style.removeProperty(t)})(e.dom,t),mt(me(e,"style").map(wt),"")&&de(e,"style")},Ot=(e,t,o=0)=>me(e,t).map((e=>parseInt(e,10))).getOr(o),kt=(e,t)=>Ot(e,t,1),Et=e=>se("col")(e)?Ot(e,"span",1)>1:kt(e,"colspan")>1,Nt=e=>kt(e,"rowspan")>1,Bt=(e,t)=>parseInt(St(e,t),10),zt=u(10),At=u(10),Wt=(e,t)=>Lt(e,t,v),Lt=(e,t,o)=>_(Oe(e),(e=>he(e,t)?o(e)?[e]:[]:Lt(e,t,o))),_t=(e,t)=>((e,t,o=b)=>o(t)?y.none():T(e,Q(t))?y.some(t):lt(t,e.join(","),(e=>he(e,"table")||o(e))))(["td","th"],e,t),Mt=e=>Wt(e,"th,td"),jt=e=>he(e,"colgroup")?ot(e,"col"):_(Ft(e),(e=>ot(e,"col"))),It=(e,t)=>it(e,"table",t),Pt=e=>Wt(e,"tr"),Ft=e=>It(e).fold(u([]),(e=>ot(e,"colgroup"))),Ht=(e,t)=>O(e,(e=>{if("colgroup"===Q(e)){const t=O(jt(e),(e=>{const t=Ot(e,"span",1);return Ve(e,1,t)}));return $e(e,t,"colgroup")}{const o=O(Mt(e),(e=>{const t=Ot(e,"rowspan",1),o=Ot(e,"colspan",1);return Ve(e,t,o)}));return $e(e,o,t(e))}})),qt=e=>Se(e).map((e=>{const t=Q(e);return(e=>T(qe,e))(t)?t:"tbody"})).getOr("tbody"),Vt=e=>{const t=Pt(e),o=[...Ft(e),...t];return Ht(o,qt)},$t=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},Ut=()=>Gt(0,0),Gt=(e,t)=>({major:e,minor:t}),Kt={nu:Gt,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?Ut():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return Gt(n(1),n(2))})(e,o)},unknown:Ut},Yt=(e,t)=>{const o=String(t).toLowerCase();return A(e,(e=>e.search(o)))},Jt=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Qt=e=>t=>gt(t,e),Xt=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>gt(e,"edge/")&&gt(e,"chrome")&&gt(e,"safari")&&gt(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Jt],search:e=>gt(e,"chrome")&&!gt(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>gt(e,"msie")||gt(e,"trident")},{name:"Opera",versionRegexes:[Jt,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Qt("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Qt("firefox")},{name:"Safari",versionRegexes:[Jt,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(gt(e,"safari")||gt(e,"mobile/"))&&gt(e,"applewebkit")}],Zt=[{name:"Windows",search:Qt("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>gt(e,"iphone")||gt(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Qt("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:Qt("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Qt("linux"),versionRegexes:[]},{name:"Solaris",search:Qt("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Qt("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Qt("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],eo={browsers:u(Xt),oses:u(Zt)},to="Edge",oo="Chromium",no="Opera",ro="Firefox",so="Safari",lo=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(to),isChromium:n(oo),isIE:n("IE"),isOpera:n(no),isFirefox:n(ro),isSafari:n(so)}},ao=()=>lo({current:void 0,version:Kt.unknown()}),co=lo,io=(u(to),u(oo),u("IE"),u(no),u(ro),u(so),"Windows"),mo="Android",uo="Linux",fo="macOS",go="Solaris",ho="FreeBSD",po="ChromeOS",wo=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(io),isiOS:n("iOS"),isAndroid:n(mo),isMacOS:n(fo),isLinux:n(uo),isSolaris:n(go),isFreeBSD:n(ho),isChromeOS:n(po)}},bo=()=>wo({current:void 0,version:Kt.unknown()}),vo=wo,yo=(u(io),u("iOS"),u(mo),u(uo),u(fo),u(go),u(ho),u(po),e=>window.matchMedia(e).matches);let xo=$t((()=>((e,t,o)=>{const n=eo.browsers(),r=eo.oses(),s=t.bind((e=>((e,t)=>H(t.brands,(t=>{const o=t.brand.toLowerCase();return A(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:Kt.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>Yt(e,t).map((e=>{const o=Kt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(ao,co),l=((e,t)=>Yt(e,t).map((e=>{const o=Kt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(r,e).fold(bo,vo),a=((e,t,o,n)=>{const r=e.isiOS()&&!0===/ipad/i.test(o),s=e.isiOS()&&!r,l=e.isiOS()||e.isAndroid(),a=l||n("(pointer:coarse)"),c=r||!s&&l&&n("(min-device-width:768px)"),i=s||l&&!c,m=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),d=!i&&!c&&!m;return{isiPad:u(r),isiPhone:u(s),isTablet:u(c),isPhone:u(i),isTouch:u(a),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:u(m),isDesktop:u(d)}})(l,s,e,o);return{browser:s,os:l,deviceType:a}})(navigator.userAgent,y.from(navigator.userAgentData),yo)));const Co=()=>xo(),So=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=St(o,e);return parseFloat(t)||0}return n},n=(e,t)=>z(t,((t,o)=>{const n=St(e,o),r=void 0===n?0:parseInt(n,10);return isNaN(r)?t:t+r}),0);return{set:(t,o)=>{if(!m(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;vt(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const r=n(e,o);return t>r?t-r:0}}},To=(e,t,o)=>((e,t)=>(e=>{const t=parseFloat(e);return isNaN(t)?y.none():y.some(t)})(e).getOr(t))(St(e,t),o),Ro=So("width",(e=>e.dom.offsetWidth)),Do=e=>Ro.get(e),Oo=e=>Ro.getOuter(e),ko=e=>((e,t)=>{const o=e.dom,n=o.getBoundingClientRect().width||o.offsetWidth;return"border-box"===t?n:((e,t,o,n)=>t-To(e,"padding-left",0)-To(e,"padding-right",0)-To(e,"border-left-width",0)-To(e,"border-right-width",0))(e,n)})(e,"content-box"),Eo=(e,t,o)=>{const n=e.cells,r=n.slice(0,t),s=n.slice(t),l=r.concat(o).concat(s);return zo(e,l)},No=(e,t,o)=>Eo(e,t,[o]),Bo=(e,t,o)=>{e.cells[t]=o},zo=(e,t)=>Ge(e.element,t,e.section,e.isNew),Ao=(e,t)=>e.cells[t],Wo=(e,t)=>Ao(e,t).element,Lo=e=>e.cells.length,_o=e=>{const t=E(e,(e=>"colgroup"===e.section));return{rows:t.fail,cols:t.pass}},Mo=(e,t,o)=>{const n=O(e.cells,o);return Ge(t(e.element),n,e.section,!0)},jo="data-snooker-locked-cols",Io=e=>me(e,jo).bind((e=>y.from(e.match(/\d+/g)))).map((e=>j(e,v))),Po=e=>{const t=z(_o(e).rows,((e,t)=>(k(t.cells,((t,o)=>{t.isLocked&&(e[o]=!0)})),e)),{}),o=K(t,((e,t)=>parseInt(t,10)));return((e,t)=>{const o=x.call(e,0);return o.sort(void 0),o})(o)},Fo=(e,t)=>e+","+t,Ho=(e,t)=>{const o=_(e.all,(e=>e.cells));return N(o,t)},qo=e=>{const t={},o=[],n=P(e).map((e=>e.element)).bind(It).bind(Io).getOr({});let r=0,s=0,l=0;const{pass:a,fail:c}=E(e,(e=>"colgroup"===e.section));k(c,(e=>{const a=[];k(e.cells,(e=>{let o=0;for(;void 0!==t[Fo(l,o)];)o++;const r=((e,t)=>J(e,t)&&void 0!==e[t]&&null!==e[t])(n,o.toString()),c=((e,t,o,n,r,s)=>({element:e,rowspan:t,colspan:o,row:n,column:r,isLocked:s}))(e.element,e.rowspan,e.colspan,l,o,r);for(let n=0;n<e.colspan;n++)for(let r=0;r<e.rowspan;r++){const e=o+n,a=Fo(l+r,e);t[a]=c,s=Math.max(s,e+1)}a.push(c)})),r++,o.push($e(e.element,a,e.section)),l++}));const{columns:i,colgroups:m}=F(a).map((e=>{const t=(e=>{const t={};let o=0;return k(e.cells,(e=>{const n=e.colspan;D(n,(r=>{const s=o+r;t[s]=((e,t,o)=>({element:e,colspan:t,column:o}))(e.element,n,s)})),o+=n})),t})(e),o=((e,t)=>({element:e,columns:t}))(e.element,Y(t));return{colgroups:[o],columns:t}})).getOrThunk((()=>({colgroups:[],columns:{}}))),d=((e,t)=>({rows:e,columns:t}))(r,s);return{grid:d,access:t,all:o,columns:i,colgroups:m}},Vo=e=>{const t=Vt(e);return qo(t)},$o=qo,Uo=(e,t,o)=>y.from(e.access[Fo(t,o)]),Go=(e,t,o)=>{const n=Ho(e,(e=>o(t,e.element)));return n.length>0?y.some(n[0]):y.none()},Ko=Ho,Yo=e=>_(e.all,(e=>e.cells)),Jo=e=>Y(e.columns),Qo=e=>q(e.columns).length>0,Xo=(e,t)=>y.from(e.columns[t]),Zo=(e,t=v)=>{const o=e.grid,n=D(o.columns,f),r=D(o.rows,f);return O(n,(o=>en((()=>_(r,(t=>Uo(e,t,o).filter((e=>e.column===o)).toArray()))),(e=>1===e.colspan&&t(e.element)),(()=>Uo(e,0,o)))))},en=(e,t,o)=>{const n=e();return A(n,t).orThunk((()=>y.from(n[0]).orThunk(o))).map((e=>e.element))},tn=e=>{const t=e.grid,o=D(t.rows,f),n=D(t.columns,f);return O(o,(t=>en((()=>_(n,(o=>Uo(e,t,o).filter((e=>e.row===t)).fold(u([]),(e=>[e]))))),(e=>1===e.rowspan),(()=>Uo(e,t,0)))))},on=(e,t)=>o=>"rtl"===nn(o)?t:e,nn=e=>"rtl"===St(e,"direction")?"rtl":"ltr",rn=So("height",(e=>{const t=e.dom;return Xe(e)?t.getBoundingClientRect().height:t.offsetHeight})),sn=e=>rn.get(e),ln=e=>rn.getOuter(e),an=(e,t)=>({left:e,top:t,translate:(o,n)=>an(e+o,t+n)}),cn=an,mn=(e,t)=>void 0!==e?e:void 0!==t?t:0,dn=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,r=t.documentElement;if(o===e.dom)return cn(o.offsetLeft,o.offsetTop);const s=mn(null==n?void 0:n.pageYOffset,r.scrollTop),l=mn(null==n?void 0:n.pageXOffset,r.scrollLeft),a=mn(r.clientTop,o.clientTop),c=mn(r.clientLeft,o.clientLeft);return un(e).translate(l-c,s-a)},un=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?cn(o.offsetLeft,o.offsetTop):Xe(e)?(e=>{const t=e.getBoundingClientRect();return cn(t.left,t.top)})(t):cn(0,0)},fn=(e,t)=>({row:e,y:t}),gn=(e,t)=>({col:e,x:t}),hn=e=>dn(e).left+Oo(e),pn=e=>dn(e).left,wn=(e,t)=>gn(e,pn(t)),bn=(e,t)=>gn(e,hn(t)),vn=e=>dn(e).top,yn=(e,t)=>fn(e,vn(t)),xn=(e,t)=>fn(e,vn(t)+ln(t)),Cn=(e,t,o)=>{if(0===o.length)return[];const n=O(o.slice(1),((t,o)=>t.map((t=>e(o,t))))),r=o[o.length-1].map((e=>t(o.length-1,e)));return n.concat([r])},Sn={delta:f,positions:e=>Cn(yn,xn,e),edge:vn},Tn=on({delta:f,edge:pn,positions:e=>Cn(wn,bn,e)},{delta:e=>-e,edge:hn,positions:e=>Cn(bn,wn,e)}),Rn={delta:(e,t)=>Tn(t).delta(e,t),positions:(e,t)=>Tn(t).positions(e,t),edge:e=>Tn(e).edge(e)},Dn={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},On=(()=>{const e="[0-9]+",t="[eE][+-]?[0-9]+",o=e=>`(?:${e})?`,n=["Infinity","[0-9]+\\."+o(e)+o(t),"\\.[0-9]+"+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),kn=/(\d+(\.\d+)?)%/,En=/(\d+(\.\d+)?)px|em/,Nn=se("col"),Bn=(e,t,o)=>{const n=(r=e,y.from(r.dom.parentElement).map(ge.fromDom)).getOrThunk((()=>Ze(xe(e))));var r;return t(e)/o(n)*100},zn=(e,t)=>{xt(e,"width",t+"px")},An=(e,t)=>{xt(e,"width",t+"%")},Wn=(e,t)=>{xt(e,"height",t+"px")},Ln=e=>{const t=(e=>{return To(t=e,"height",t.dom.offsetHeight)+"px";var t})(e);return t?((e,t,o,n)=>{const r=parseFloat(e);return pt(e,"%")&&"table"!==Q(t)?((e,t,o,n)=>{const r=It(e).map((e=>{const n=o(e);return Math.floor(t/100*n)})).getOr(t);return n(e,r),r})(t,r,o,n):r})(t,e,sn,Wn):sn(e)},_n=(e,t)=>Rt(e,t).orThunk((()=>me(e,t).map((e=>e+"px")))),Mn=e=>_n(e,"width"),jn=e=>Bn(e,Do,ko),In=e=>{return Nn(e)?Do(e):To(t=e,"width",t.dom.offsetWidth);var t},Pn=e=>((e,t,o)=>o(e)/kt(e,"rowspan"))(e,0,Ln),Fn=(e,t,o)=>{xt(e,"width",t+o)},Hn=e=>Bn(e,Do,ko)+"%",qn=u(kn),Vn=se("col"),$n=e=>Mn(e).getOrThunk((()=>In(e)+"px")),Un=e=>{return(t=e,_n(t,"height")).getOrThunk((()=>Pn(e)+"px"));var t},Gn=(e,t,o,n,r,s)=>e.filter(n).fold((()=>s(((e,t)=>{if(t<0||t>=e.length-1)return y.none();const o=e[t].fold((()=>{const o=(e=>{const t=x.call(e,0);return t.reverse(),t})(e.slice(0,t));return H(o,((e,t)=>e.map((e=>({value:e,delta:t+1})))))}),(e=>y.some({value:e,delta:0}))),n=e[t+1].fold((()=>{const o=e.slice(t+1);return H(o,((e,t)=>e.map((e=>({value:e,delta:t+1})))))}),(e=>y.some({value:e,delta:1})));return o.bind((e=>n.map((t=>{const o=t.delta+e.delta;return Math.abs(t.value-e.value)/o}))))})(o,t))),(e=>r(e))),Kn=(e,t,o,n)=>{const r=Zo(e),s=Qo(e)?(e=>O(Jo(e),(e=>y.from(e.element))))(e):r,l=[y.some(Rn.edge(t))].concat(O(Rn.positions(r,t),(e=>e.map((e=>e.x))))),a=p(Et);return O(s,((e,t)=>Gn(e,t,l,a,(e=>{if((e=>{const t=Co().browser,o=t.isChromium()||t.isFirefox();return!Vn(e)||o})(e))return o(e);{const e=null!=(s=r[t])?f(s):y.none();return Gn(e,t,l,a,(e=>n(y.some(Do(e)))),n)}var s}),n)))},Yn=e=>e.map((e=>e+"px")).getOr(""),Jn=(e,t,o)=>Kn(e,t,In,(e=>e.getOrThunk(o.minCellWidth))),Qn=(e,t,o,n,r)=>{const s=tn(e),l=[y.some(o.edge(t))].concat(O(o.positions(s,t),(e=>e.map((e=>e.y)))));return O(s,((e,t)=>Gn(e,t,l,p(Nt),n,r)))},Xn=(e,t)=>()=>Xe(e)?t(e):parseFloat(Rt(e,"width").getOr("0")),Zn=e=>{const t=Xn(e,(e=>parseFloat(Hn(e)))),o=Xn(e,Do);return{width:t,pixelWidth:o,getWidths:(t,o)=>((e,t,o)=>Kn(e,t,jn,(e=>e.fold((()=>o.minCellWidth()),(e=>e/o.pixelWidth()*100)))))(t,e,o),getCellDelta:e=>e/o()*100,singleColumnWidth:(e,t)=>[100-e],minCellWidth:()=>zt()/o()*100,setElementWidth:An,adjustTableWidth:o=>{const n=t();An(e,n+o/100*n)},isRelative:!0,label:"percent"}},er=e=>{const t=Xn(e,Do);return{width:t,pixelWidth:t,getWidths:(t,o)=>Jn(t,e,o),getCellDelta:f,singleColumnWidth:(e,t)=>[Math.max(zt(),e+t)-e],minCellWidth:zt,setElementWidth:zn,adjustTableWidth:o=>{const n=t()+o;zn(e,n)},isRelative:!1,label:"pixel"}},tr=e=>Mn(e).fold((()=>(e=>{const t=Xn(e,Do),o=u(0);return{width:t,pixelWidth:t,getWidths:(t,o)=>Jn(t,e,o),getCellDelta:o,singleColumnWidth:u([0]),minCellWidth:o,setElementWidth:d,adjustTableWidth:d,isRelative:!0,label:"none"}})(e)),(t=>((e,t)=>null!==qn().exec(t)?Zn(e):er(e))(e,t))),or=er,nr=Zn,rr=(e,t,o)=>{const n=e[o].element,r=ge.fromTag("td");ze(r,ge.fromTag("br")),(t?ze:Be)(n,r)},sr=((e,t)=>{const o=t=>e(t)?y.from(t.dom.nodeValue):y.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return o(t).getOr("")},getOption:o,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(oe),lr=e=>sr.get(e),ar=e=>sr.getOption(e),cr=(e,t)=>sr.set(e,t),ir=e=>"img"===Q(e)?1:ar(e).fold((()=>Oe(e).length),(e=>e.length)),mr=["img","br"],dr=e=>ar(e).filter((e=>0!==e.trim().length||e.indexOf("\xa0")>-1)).isSome()||T(mr,Q(e)),ur=e=>((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const r=ge.fromDom(e.childNodes[n]);if(t(r))return y.some(r);const s=o(e.childNodes[n]);if(s.isSome())return s}return y.none()};return o(e.dom)})(e,dr),fr=e=>gr(e,dr),gr=(e,t)=>{const o=e=>{const n=Oe(e);for(let e=n.length-1;e>=0;e--){const r=n[e];if(t(r))return y.some(r);const s=o(r);if(s.isSome())return s}return y.none()};return o(e)},hr={scope:["row","col"]},pr=e=>()=>{const t=ge.fromTag("td",e.dom);return ze(t,ge.fromTag("br",e.dom)),t},wr=e=>()=>ge.fromTag("col",e.dom),br=e=>()=>ge.fromTag("colgroup",e.dom),vr=e=>()=>ge.fromTag("tr",e.dom),yr=(e,t,o)=>{const n=((e,t)=>{const o=He(e,t),n=Oe(Fe(e));return Le(o,n),o})(e,t);return $(o,((e,t)=>{null===e?de(n,t):ae(n,t,e)})),n},xr=e=>e,Cr=(e,t,o)=>{const n=(e,t)=>{((e,t)=>{const o=e.dom,n=t.dom;vt(o)&&vt(n)&&(n.style.cssText=o.style.cssText)})(e.element,t),Dt(t,"height"),1!==e.colspan&&Dt(t,"width")};return{col:o=>{const r=ge.fromTag(Q(o.element),t.dom);return n(o,r),e(o.element,r),r},colgroup:br(t),row:vr(t),cell:r=>{const s=ge.fromTag(Q(r.element),t.dom),l=o.getOr(["strong","em","b","i","span","font","h1","h2","h3","h4","h5","h6","p","div"]),a=l.length>0?((e,t,o)=>ur(e).map((n=>{const r=o.join(","),s=tt(n,r,(t=>be(t,e)));return B(s,((e,t)=>{const o=Pe(t);return de(o,"contenteditable"),ze(e,o),o}),t)})).getOr(t))(r.element,s,l):s;return ze(a,ge.fromTag("br")),n(r,s),((e,t)=>{$(hr,((o,n)=>me(e,n).filter((e=>T(o,e))).each((e=>ae(t,n,e)))))})(r.element,s),e(r.element,s),s},replace:yr,colGap:wr(t),gap:pr(t)}},Sr=e=>({col:wr(e),colgroup:br(e),row:vr(e),cell:pr(e),replace:xr,colGap:wr(e),gap:pr(e)}),Tr=e=>ge.fromDom(e.getBody()),Rr=e=>t=>be(t,Tr(e)),Dr=e=>{de(e,"data-mce-style");const t=e=>de(e,"data-mce-style");k(Mt(e),t),k(jt(e),t),k(Pt(e),t)},Or=e=>ge.fromDom(e.selection.getStart()),kr=e=>e.getBoundingClientRect().width,Er=e=>e.getBoundingClientRect().height,Nr=(e,t)=>{const o=t.column,n=t.column+t.colspan-1,r=t.row,s=t.row+t.rowspan-1;return o<=e.finishCol&&n>=e.startCol&&r<=e.finishRow&&s>=e.startRow},Br=(e,t)=>t.column>=e.startCol&&t.column+t.colspan-1<=e.finishCol&&t.row>=e.startRow&&t.row+t.rowspan-1<=e.finishRow,zr=(e,t,o)=>{const n=Go(e,t,be),r=Go(e,o,be);return n.bind((e=>r.map((t=>{return o=e,n=t,{startRow:Math.min(o.row,n.row),startCol:Math.min(o.column,n.column),finishRow:Math.max(o.row+o.rowspan-1,n.row+n.rowspan-1),finishCol:Math.max(o.column+o.colspan-1,n.column+n.colspan-1)};var o,n}))))},Ar=(e,t,o)=>zr(e,t,o).map((t=>{const o=Ko(e,h(Nr,t));return O(o,(e=>e.element))})),Wr=(e,t)=>Go(e,t,((e,t)=>ve(t,e))).map((e=>e.element)),Lr=(e,t,o)=>{const n=Mr(e);return Ar(n,t,o)},_r=(e,t,o,n,r)=>{const s=Mr(e),l=be(e,o)?y.some(t):Wr(s,t),a=be(e,r)?y.some(n):Wr(s,n);return l.bind((e=>a.bind((t=>Ar(s,e,t)))))},Mr=Vo;var jr=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],Ir=()=>({up:u({selector:lt,closest:it,predicate:st,all:Te}),down:u({selector:nt,predicate:et}),styles:u({get:St,getRaw:Rt,set:xt,remove:Dt}),attrs:u({get:ie,set:ae,remove:de,copyTo:(e,t)=>{const o=ue(e);ce(t,o)}}),insert:u({before:Ee,after:Ne,afterAll:We,append:ze,appendAll:Le,prepend:Be,wrap:Ae}),remove:u({unwrap:je,remove:Me}),create:u({nu:ge.fromTag,clone:e=>ge.fromDom(e.dom.cloneNode(!1)),text:ge.fromText}),query:u({comparePosition:(e,t)=>e.dom.compareDocumentPosition(t.dom),prevSibling:Re,nextSibling:De}),property:u({children:Oe,name:Q,parent:Se,document:e=>Ce(e).dom,isText:oe,isComment:ee,isElement:te,isSpecial:e=>{const t=Q(e);return T(["script","noscript","iframe","noframes","noembed","title","style","textarea","xmp"],t)},getLanguage:e=>te(e)?me(e,"lang"):y.none(),getText:lr,setText:cr,isBoundary:e=>!!te(e)&&("body"===Q(e)||T(jr,Q(e))),isEmptyTag:e=>!!te(e)&&T(["br","img","hr","input"],Q(e)),isNonEditable:e=>te(e)&&"false"===ie(e,"contenteditable")}),eq:be,is:ye});const Pr=(e,t,o,n)=>{const r=t(e,o);return B(n,((o,n)=>{const r=t(e,n);return Fr(e,o,r)}),r)},Fr=(e,t,o)=>t.bind((t=>o.filter(h(e.eq,t)))),Hr=Ir(),qr=(e,t)=>((e,t,o)=>o.length>0?((e,t,o,n)=>n(e,t,o[0],o.slice(1)))(e,t,o,Pr):y.none())(Hr,((t,o)=>e(o)),t),Vr=e=>lt(e,"table"),$r=(e,t,o)=>{const n=e=>t=>void 0!==o&&o(t)||be(t,e);return be(e,t)?y.some({boxes:y.some([e]),start:e,finish:t}):Vr(e).bind((r=>Vr(t).bind((s=>{if(be(r,s))return y.some({boxes:Lr(r,e,t),start:e,finish:t});if(ve(r,s)){const o=tt(t,"td,th",n(r)),l=o.length>0?o[o.length-1]:t;return y.some({boxes:_r(r,e,r,t,s),start:e,finish:l})}if(ve(s,r)){const o=tt(e,"td,th",n(s)),l=o.length>0?o[o.length-1]:e;return y.some({boxes:_r(s,e,r,t,s),start:e,finish:l})}return((e,t,o)=>((e,t,o,n=b)=>{const r=[t].concat(e.up().all(t)),s=[o].concat(e.up().all(o)),l=e=>W(e,n).fold((()=>e),(t=>e.slice(0,t+1))),a=l(r),c=l(s),i=A(a,(t=>R(c,((e,t)=>h(e.eq,t))(e,t))));return{firstpath:a,secondpath:c,shared:i}})(Hr,e,t,void 0))(e,t).shared.bind((l=>it(l,"table",o).bind((o=>{const l=tt(t,"td,th",n(o)),a=l.length>0?l[l.length-1]:t,c=tt(e,"td,th",n(o)),i=c.length>0?c[c.length-1]:e;return y.some({boxes:_r(o,e,r,t,s),start:i,finish:a})}))))}))))},Ur=(e,t)=>{const o=nt(e,t);return o.length>0?y.some(o):y.none()},Gr=(e,t,o)=>ct(e,t).bind((t=>ct(e,o).bind((e=>qr(Vr,[t,e]).map((o=>({first:t,last:e,table:o}))))))),Kr=(e,t,o,n,r)=>((e,t)=>A(e,(e=>he(e,t))))(e,r).bind((e=>((e,t,o)=>It(e).bind((n=>((e,t,o,n)=>Go(e,t,be).bind((t=>{const r=o>0?t.row+t.rowspan-1:t.row,s=n>0?t.column+t.colspan-1:t.column;return Uo(e,r+o,s+n).map((e=>e.element))})))(Mr(n),e,t,o))))(e,t,o).bind((e=>((e,t)=>lt(e,"table").bind((o=>ct(o,t).bind((t=>$r(t,e).bind((e=>e.boxes.map((t=>({boxes:t,start:e.start,finish:e.finish}))))))))))(e,n))))),Yr=(e,t)=>Ur(e,t),Jr=(e,t,o)=>Gr(e,t,o).bind((t=>{const o=t=>be(e,t),n="thead,tfoot,tbody,table",r=lt(t.first,n,o),s=lt(t.last,n,o);return r.bind((e=>s.bind((o=>be(e,o)?((e,t,o)=>((e,t,o)=>zr(e,t,o).bind((t=>((e,t)=>{let o=!0;const n=h(Br,t);for(let r=t.startRow;r<=t.finishRow;r++)for(let s=t.startCol;s<=t.finishCol;s++)o=o&&Uo(e,r,s).exists(n);return o?y.some(t):y.none()})(e,t))))(Mr(e),t,o))(t.table,t.first,t.last):y.none()))))})),Qr=f,Xr=e=>{const t=(e,t)=>me(e,t).exists((e=>parseInt(e,10)>1));return e.length>0&&M(e,(e=>t(e,"rowspan")||t(e,"colspan")))?y.some(e):y.none()},Zr=(e,t,o)=>t.length<=1?y.none():Jr(e,o.firstSelectedSelector,o.lastSelectedSelector).map((e=>({bounds:e,cells:t}))),es={selected:"data-mce-selected",selectedSelector:"td[data-mce-selected],th[data-mce-selected]",firstSelected:"data-mce-first-selected",firstSelectedSelector:"td[data-mce-first-selected],th[data-mce-first-selected]",lastSelected:"data-mce-last-selected",lastSelectedSelector:"td[data-mce-last-selected],th[data-mce-last-selected]"},ts=(e,t,o)=>({element:o,mergable:Zr(t,e,es),unmergable:Xr(e),selection:Qr(e)}),os=e=>(t,o)=>{const n=Q(t),r="col"===n||"colgroup"===n?It(s=t).bind((e=>Yr(e,es.firstSelectedSelector))).fold(u(s),(e=>e[0])):t;var s;return it(r,e,o)},ns=os("th,td,caption"),rs=os("th,td"),ss=e=>{return t=e.model.table.getSelectedCells(),O(t,ge.fromDom);var t},ls=(e,t)=>{e.on("BeforeGetContent",(t=>{const o=o=>{t.preventDefault(),(e=>It(e[0]).map((e=>{const t=((e,t)=>{const o=e=>he(e.element,t),n=Fe(e),r=Vt(n),s=tr(e),l=$o(r),a=((e,t)=>{const o=e.grid.columns;let n=e.grid.rows,r=o,s=0,l=0;const a=[],c=[];return $(e.access,(e=>{if(a.push(e),t(e)){c.push(e);const t=e.row,o=t+e.rowspan-1,a=e.column,i=a+e.colspan-1;t<n?n=t:o>s&&(s=o),a<r?r=a:i>l&&(l=i)}})),((e,t,o,n,r,s)=>({minRow:e,minCol:t,maxRow:o,maxCol:n,allCells:r,selectedCells:s}))(n,r,s,l,a,c)})(l,o),c="th:not("+t+"),td:not("+t+")",i=Lt(n,"th,td",(e=>he(e,c)));k(i,Me),((e,t,o,n)=>{const r=N(e,(e=>"colgroup"!==e.section)),s=t.grid.columns,l=t.grid.rows;for(let e=0;e<l;e++){let l=!1;for(let a=0;a<s;a++)e<o.minRow||e>o.maxRow||a<o.minCol||a>o.maxCol||(Uo(t,e,a).filter(n).isNone()?rr(r,l,e):l=!0)}})(r,l,a,o);const m=((e,t,o,n)=>{if(0===n.minCol&&t.grid.columns===n.maxCol+1)return 0;const r=Jn(t,e,o),s=z(r,((e,t)=>e+t),0),l=z(r.slice(n.minCol,n.maxCol+1),((e,t)=>e+t),0),a=l/s*o.pixelWidth()-o.pixelWidth();return o.getCellDelta(a)})(e,Vo(e),s,a);return((e,t,o,n)=>{$(o.columns,(e=>{(e.column<t.minCol||e.column>t.maxCol)&&Me(e.element)}));const r=N(Wt(e,"tr"),(e=>0===e.dom.childElementCount));k(r,Me),t.minCol!==t.maxCol&&t.minRow!==t.maxRow||k(Wt(e,"th,td"),(e=>{de(e,"rowspan"),de(e,"colspan")})),de(e,jo),de(e,"data-snooker-col-series"),tr(e).adjustTableWidth(n)})(n,a,l,m),n})(e,"[data-mce-selected]");return Dr(t),[t]})))(o).each((o=>{t.content="text"===t.format?(e=>O(e,(e=>e.dom.innerText)).join(""))(o):((e,t)=>O(t,(t=>e.selection.serializer.serialize(t.dom,{}))).join(""))(e,o)}))};if(!0===t.selection){const t=(e=>N(ss(e),(e=>he(e,es.selectedSelector))))(e);t.length>=1&&o(t)}})),e.on("BeforeSetContent",(o=>{if(!0===o.selection&&!0===o.paste){const n=ss(e);P(n).each((n=>{It(n).each((r=>{const s=N(((e,t)=>{const o=document.createElement("div");return o.innerHTML=e,Oe(ge.fromDom(o))})(o.content),(e=>"meta"!==Q(e))),l=se("table");if(1===s.length&&l(s[0])){o.preventDefault();const l=ge.fromDom(e.getDoc()),a=Sr(l),c=((e,t,o)=>({element:e,clipboard:t,generators:o}))(n,s[0],a);t.pasteCells(r,c).each((()=>{e.focus()}))}}))}))}}))},as=(e,t)=>({element:e,offset:t}),cs=(e,t,o)=>e.property().isText(t)&&0===e.property().getText(t).trim().length||e.property().isComment(t)?o(t).bind((t=>cs(e,t,o).orThunk((()=>y.some(t))))):y.none(),is=(e,t)=>e.property().isText(t)?e.property().getText(t).length:e.property().children(t).length,ms=(e,t)=>{const o=cs(e,t,e.query().prevSibling).getOr(t);if(e.property().isText(o))return as(o,is(e,o));const n=e.property().children(o);return n.length>0?ms(e,n[n.length-1]):as(o,is(e,o))},ds=ms,us=Ir(),fs=(e,t)=>{if(!Et(e)){const o=(e=>Mn(e).bind((e=>{return t=e,o=["fixed","relative","empty"],y.from(On.exec(t)).bind((e=>{const t=Number(e[1]),n=e[2];return((e,t)=>R(t,(t=>R(Dn[t],(t=>e===t)))))(n,o)?y.some({value:t,unit:n}):y.none()}));var t,o})))(e);o.each((o=>{const n=o.value/2;Fn(e,n,o.unit),Fn(t,n,o.unit)}))}},gs=e=>O(e,u(0)),hs=(e,t,o,n,r)=>r(e.slice(0,t)).concat(n).concat(r(e.slice(o))),ps=e=>(t,o,n,r)=>{if(e(n)){const e=Math.max(r,t[o]-Math.abs(n)),s=Math.abs(e-t[o]);return n>=0?s:-s}return n},ws=ps((e=>e<0)),bs=ps(v),vs=()=>{const e=(e,t,o,n)=>{const r=(100+o)/100,s=Math.max(n,(e[t]+o)/r);return O(e,((e,o)=>(o===t?s:e/r)-e))},t=(t,o,n,r,s,l)=>l?e(t,o,r,s):((e,t,o,n,r)=>{const s=ws(e,t,n,r);return hs(e,t,o+1,[s,0],gs)})(t,o,n,r,s);return{resizeTable:(e,t)=>e(t),clampTableDelta:ws,calcLeftEdgeDeltas:t,calcMiddleDeltas:(e,o,n,r,s,l,a)=>t(e,n,r,s,l,a),calcRightEdgeDeltas:(t,o,n,r,s,l)=>{if(l)return e(t,n,r,s);{const e=ws(t,n,r,s);return gs(t.slice(0,n)).concat([e])}},calcRedestributedWidths:(e,t,o,n)=>{if(n){const n=(t+o)/t,r=O(e,(e=>e/n));return{delta:100*n-100,newSizes:r}}return{delta:o,newSizes:e}}}},ys=()=>{const e=(e,t,o,n,r)=>{const s=bs(e,n>=0?o:t,n,r);return hs(e,t,o+1,[s,-s],gs)};return{resizeTable:(e,t,o)=>{o&&e(t)},clampTableDelta:(e,t,o,n,r)=>{if(r){if(o>=0)return o;{const t=z(e,((e,t)=>e+t-n),0);return Math.max(-t,o)}}return ws(e,t,o,n)},calcLeftEdgeDeltas:e,calcMiddleDeltas:(t,o,n,r,s,l)=>e(t,n,r,s,l),calcRightEdgeDeltas:(e,t,o,n,r,s)=>{if(s)return gs(e);{const t=n/e.length;return O(e,u(t))}},calcRedestributedWidths:(e,t,o,n)=>({delta:0,newSizes:e})}},xs=e=>Vo(e).grid,Cs=se("th"),Ss=e=>M(e,(e=>Cs(e.element))),Ts=(e,t)=>e&&t?"sectionCells":e?"section":"cells",Rs=e=>{const t="thead"===e.section,o=mt(Ds(e.cells),"th");return"tfoot"===e.section?{type:"footer"}:t||o?{type:"header",subType:Ts(t,o)}:{type:"body"}},Ds=e=>{const t=N(e,(e=>Cs(e.element)));return 0===t.length?y.some("td"):t.length===e.length?y.some("th"):y.none()},Os=(e,t,o)=>Ue(o(e.element,t),!0,e.isLocked),ks=(e,t)=>e.section!==t?Ge(e.element,e.cells,t,e.isNew):e,Es=()=>({transformRow:ks,transformCell:(e,t,o)=>{const n=o(e.element,t),r="td"!==Q(n)?((e,t)=>{const o=He(e,"td");Ne(e,o);const n=Oe(e);return Le(o,n),Me(e),o})(n):n;return Ue(r,e.isNew,e.isLocked)}}),Ns=()=>({transformRow:ks,transformCell:Os}),Bs=()=>({transformRow:(e,t)=>ks(e,"thead"===t?"tbody":t),transformCell:Os}),zs=Es,As=Ns,Ws=Bs,Ls=()=>({transformRow:f,transformCell:Os}),_s=e=>it(e,"[contenteditable]"),Ms=(e,t=!1)=>Xe(e)?e.dom.isContentEditable:_s(e).fold(u(t),(e=>"true"===js(e))),js=e=>e.dom.contentEditable,Is=(e,t,o,n)=>{o===n?de(e,t):ae(e,t,o)},Ps=(e,t,o)=>{F(ot(e,t)).fold((()=>Be(e,o)),(e=>Ne(e,o)))},Fs=(e,t)=>{const o=[],n=[],r=e=>O(e,(e=>{e.isNew&&o.push(e.element);const t=e.element;return _e(t),k(e.cells,(e=>{e.isNew&&n.push(e.element),Is(e.element,"colspan",e.colspan,1),Is(e.element,"rowspan",e.rowspan,1),ze(t,e.element)})),t})),s=e=>_(e,(e=>O(e.cells,(e=>(Is(e.element,"span",e.colspan,1),e.element))))),l=(t,o)=>{const n=((e,t)=>{const o=at(e,t).getOrThunk((()=>{const o=ge.fromTag(t,xe(e).dom);return"thead"===t?Ps(e,"caption,colgroup",o):"colgroup"===t?Ps(e,"caption",o):ze(e,o),o}));return _e(o),o})(e,o),l=("colgroup"===o?s:r)(t);Le(n,l)},a=(t,o)=>{t.length>0?l(t,o):(t=>{at(e,t).each(Me)})(o)},c=[],i=[],m=[],d=[];return k(t,(e=>{switch(e.section){case"thead":c.push(e);break;case"tbody":i.push(e);break;case"tfoot":m.push(e);break;case"colgroup":d.push(e)}})),a(d,"colgroup"),a(c,"thead"),a(i,"tbody"),a(m,"tfoot"),{newRows:o,newCells:n}},Hs=(e,t)=>{if(0===e.length)return 0;const o=e[0];return W(e,(e=>!t(o.element,e.element))).getOr(e.length)},qs=(e,t)=>{const o=O(e,(e=>O(e.cells,b)));return O(e,((n,r)=>{const s=_(n.cells,((n,s)=>{if(!1===o[r][s]){const m=((e,t,o,n)=>{const r=((e,t)=>e[t])(e,t),s="colgroup"===r.section,l=Hs(r.cells.slice(o),n),a=s?1:Hs(((e,t)=>O(e,(e=>Ao(e,t))))(e.slice(t),o),n);return{colspan:l,rowspan:a}})(e,r,s,t);return((e,t,n,r)=>{for(let s=e;s<e+n;s++)for(let e=t;e<t+r;e++)o[s][e]=!0})(r,s,m.rowspan,m.colspan),[(l=n.element,a=m.rowspan,c=m.colspan,i=n.isNew,{element:l,rowspan:a,colspan:c,isNew:i})]}return[];var l,a,c,i}));return((e,t,o,n)=>({element:e,cells:t,section:o,isNew:n}))(n.element,s,n.section,n.isNew)}))},Vs=(e,t,o)=>{const n=[];k(e.colgroups,(r=>{const s=[];for(let n=0;n<e.grid.columns;n++){const r=Xo(e,n).map((e=>Ue(e.element,o,!1))).getOrThunk((()=>Ue(t.colGap(),!0,!1)));s.push(r)}n.push(Ge(r.element,s,"colgroup",o))}));for(let r=0;r<e.grid.rows;r++){const s=[];for(let n=0;n<e.grid.columns;n++){const l=Uo(e,r,n).map((e=>Ue(e.element,o,e.isLocked))).getOrThunk((()=>Ue(t.gap(),!0,!1)));s.push(l)}const l=e.all[r],a=Ge(l.element,s,l.section,o);n.push(a)}return n},$s=e=>qs(e,be),Us=(e,t)=>H(e.all,(e=>A(e.cells,(e=>be(t,e.element))))),Gs=(e,t,o)=>{const n=O(t.selection,(t=>_t(t).bind((t=>Us(e,t))).filter(o))),r=dt(n);return ut(r.length>0,r)},Ks=(e,t,o,n,r)=>(s,l,a,c)=>{const i=Vo(s),m=y.from(null==c?void 0:c.section).getOrThunk(Ls);return t(i,l).map((t=>{const o=((e,t)=>Vs(e,t,!1))(i,a),n=e(o,t,be,r(a),m),s=Po(n.grid);return{info:t,grid:$s(n.grid),cursor:n.cursor,lockedColumns:s}})).bind((e=>{const t=Fs(s,e.grid),r=y.from(null==c?void 0:c.sizing).getOrThunk((()=>tr(s))),l=y.from(null==c?void 0:c.resize).getOrThunk(ys);return o(s,e.grid,e.info,{sizing:r,resize:l,section:m}),n(s),de(s,jo),e.lockedColumns.length>0&&ae(s,jo,e.lockedColumns.join(",")),y.some({cursor:e.cursor,newRows:t.newRows,newCells:t.newCells})}))},Ys=(e,t)=>Gs(e,t,v).map((e=>({cells:e,generators:t.generators,clipboard:t.clipboard}))),Js=(e,t)=>Gs(e,t,v),Qs=(e,t)=>Gs(e,t,(e=>!e.isLocked)),Xs=(e,t)=>M(t,(t=>((e,t)=>Us(e,t).exists((e=>!e.isLocked)))(e,t))),Zs=(e,t,o,n)=>{const r=_o(e).rows;let s=!0;for(let e=0;e<r.length;e++)for(let l=0;l<Lo(r[0]);l++){const a=r[e],c=Ao(a,l),i=o(c.element,t);i&&!s?Bo(a,l,Ue(n(),!0,c.isLocked)):i&&(s=!1)}return e},el=e=>{const t=t=>t(e),o=u(e),n=()=>r,r={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:v,isError:b,map:t=>ol.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>y.some(e)};return r},tl=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:b,isError:v,map:t,mapError:t=>ol.error(t(e)),bind:t,exists:b,forall:v,getOr:f,or:f,getOrThunk:w,orThunk:w,getOrDie:(n=String(e),()=>{throw new Error(n)}),each:d,toOptional:y.none};var n;return o},ol={value:el,error:tl,fromOption:(e,t)=>e.fold((()=>tl(t)),el)},nl=(e,t)=>({rowDelta:0,colDelta:Lo(e[0])-Lo(t[0])}),rl=(e,t)=>({rowDelta:e.length-t.length,colDelta:0}),sl=(e,t,o,n)=>{const r="colgroup"===t.section?o.col:o.cell;return D(e,(e=>Ue(r(),!0,n(e))))},ll=(e,t,o,n)=>{const r=e[e.length-1];return e.concat(D(t,(()=>{const e="colgroup"===r.section?o.colgroup:o.row,t=Mo(r,e,f),s=sl(t.cells.length,t,o,(e=>J(n,e.toString())));return zo(t,s)})))},al=(e,t,o,n)=>O(e,(e=>{const r=sl(t,e,o,b);return Eo(e,n,r)})),cl=(e,t,o)=>{const n=t.colDelta<0?al:f,r=t.rowDelta<0?ll:f,s=Po(e),l=Lo(e[0]),a=R(s,(e=>e===l-1)),c=n(e,Math.abs(t.colDelta),o,a?l-1:l),i=Po(c);return r(c,Math.abs(t.rowDelta),o,j(i,v))},il=(e,t,o,n)=>{const r=h(n,Ao(e[t],o).element),s=e[t];return e.length>1&&Lo(s)>1&&(o>0&&r(Wo(s,o-1))||o<s.cells.length-1&&r(Wo(s,o+1))||t>0&&r(Wo(e[t-1],o))||t<e.length-1&&r(Wo(e[t+1],o)))},ml=(e,t,o)=>N(o,(o=>o>=e.column&&o<=Lo(t[0])+e.column)),dl=(e,t,o,n,r)=>{((e,t,o,n)=>{t>0&&t<e[0].cells.length&&k(e,(e=>{const r=e.cells[t-1];let s=0;const l=n();for(;e.cells.length>t+s&&o(r.element,e.cells[t+s].element);)Bo(e,t+s,Ue(l,!0,e.cells[t+s].isLocked)),s++}))})(t,e,r,n.cell);const s=rl(o,t),l=cl(o,s,n),a=rl(t,l),c=cl(t,a,n);return O(c,((t,o)=>Eo(t,e,l[o].cells)))},ul=(e,t,o,n,r)=>{((e,t,o,n)=>{const r=_o(e).rows;if(t>0&&t<r.length){const e=((e,t)=>z(e,((e,o)=>R(e,(e=>t(e.element,o.element)))?e:e.concat([o])),[]))(r[t-1].cells,o);k(e,(e=>{let s=y.none();for(let l=t;l<r.length;l++)for(let t=0;t<Lo(r[0]);t++){const a=r[l],c=Ao(a,t);o(c.element,e.element)&&(s.isNone()&&(s=y.some(n())),s.each((e=>{Bo(a,t,Ue(e,!0,c.isLocked))})))}}))}})(t,e,r,n.cell);const s=Po(t),l=nl(t,o),a={...l,colDelta:l.colDelta-s.length},c=cl(t,a,n),{cols:i,rows:m}=_o(c),d=Po(c),u=nl(o,t),f={...u,colDelta:u.colDelta+d.length},g=(p=n,w=d,O(o,(e=>z(w,((t,o)=>{const n=sl(1,e,p,v)[0];return No(t,o,n)}),e)))),h=cl(g,f,n);var p,w;return[...i,...m.slice(0,e),...h,...m.slice(e,m.length)]},fl=(e,t,o,n,r)=>{const{rows:s,cols:l}=_o(e),a=s.slice(0,t),c=s.slice(t);return[...l,...a,((e,t,o,n)=>Mo(e,(e=>n(e,o)),t))(s[o],((e,o)=>t>0&&t<s.length&&n(Wo(s[t-1],o),Wo(s[t],o))?Ao(s[t],o):Ue(r(e.element,n),!0,e.isLocked)),n,r),...c]},gl=(e,t,o,n,r)=>O(e,(e=>{const s=t>0&&t<Lo(e)&&n(Wo(e,t-1),Wo(e,t)),l=((e,t,o,n,r,s,l)=>{if("colgroup"!==o&&n)return Ao(e,t);{const t=Ao(e,r);return Ue(l(t.element,s),!0,!1)}})(e,t,e.section,s,o,n,r);return No(e,t,l)})),hl=(e,t,o,n)=>((e,t,o,n)=>void 0!==Wo(e[t],o)&&t>0&&n(Wo(e[t-1],o),Wo(e[t],o)))(e,t,o,n)||((e,t,o)=>t>0&&o(Wo(e,t-1),Wo(e,t)))(e[t],o,n),pl=(e,t,o,n)=>{const r=e=>(e=>"row"===e?Nt(t):Et(t))(e)?`${e}group`:e;return e?Cs(t)?r(o):null:n&&Cs(t)?r("row"===o?"col":"row"):null},wl=(e,t,o)=>Ue(o(e.element,t),!0,e.isLocked),bl=(e,t,o,n,r,s,l)=>O(e,((e,a)=>((e,c)=>{const i=e.cells,m=O(i,((e,c)=>{if((e=>R(t,(t=>o(e.element,t.element))))(e)){const t=l(e,a,c)?r(e,o,n):e;return s(t,a,c).each((e=>{var o,n;o=t.element,n={scope:y.from(e)},$(n,((e,t)=>{e.fold((()=>{de(o,t)}),(e=>{le(o.dom,t,e)}))}))})),t}return e}));return Ge(e.element,m,e.section,e.isNew)})(e))),vl=(e,t,o)=>_(e,((n,r)=>hl(e,r,t,o)?[]:[Ao(n,t)])),yl=(e,t,o,n,r)=>{const s=_o(e).rows,l=_(t,(e=>vl(s,e,n))),a=O(s,(e=>Ss(e.cells))),c=((e,t)=>M(t,f)&&Ss(e)?v:(e,o,n)=>!("th"===Q(e.element)&&t[o]))(l,a),i=((e,t)=>(o,n)=>y.some(pl(e,o.element,"row",t[n])))(o,a);return bl(e,l,n,r,wl,i,c)},xl=(e,t,o,n)=>{const r=_o(e).rows,s=O(t,(e=>Ao(r[e.row],e.column)));return bl(e,s,o,n,wl,y.none,v)},Cl=e=>{if(!s(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return k(e,((n,r)=>{const l=q(n);if(1!==l.length)throw new Error("one and only one name per case");const a=l[0],c=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!s(c))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==c.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+c.length+" ("+c+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[r].apply(null,o)},match:e=>{const n=q(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!M(t,(e=>T(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o},Sl={...Cl([{none:[]},{only:["index"]},{left:["index","next"]},{middle:["prev","index","next"]},{right:["prev","index"]}])},Tl=(e,t,o)=>{let n=0;for(let r=e;r<t;r++)n+=void 0!==o[r]?o[r]:0;return n},Rl=(e,t)=>{const o=Yo(e);return O(o,(e=>{const o=Tl(e.row,e.row+e.rowspan,t);return{element:e.element,height:o,rowspan:e.rowspan}}))},Dl=(e,t,o)=>{const n=((e,t)=>Qo(e)?((e,t)=>{const o=Jo(e);return O(o,((e,o)=>({element:e.element,width:t[o],colspan:e.colspan})))})(e,t):((e,t)=>{const o=Yo(e);return O(o,(e=>{const o=Tl(e.column,e.column+e.colspan,t);return{element:e.element,width:o,colspan:e.colspan}}))})(e,t))(e,t);k(n,(e=>{o.setElementWidth(e.element,e.width)}))},Ol=(e,t,o,n,r)=>{const s=Vo(e),l=r.getCellDelta(t),a=r.getWidths(s,r),c=o===s.grid.columns-1,i=n.clampTableDelta(a,o,l,r.minCellWidth(),c),m=((e,t,o,n,r)=>{const s=e.slice(0),l=((e,t)=>0===e.length?Sl.none():1===e.length?Sl.only(0):0===t?Sl.left(0,1):t===e.length-1?Sl.right(t-1,t):t>0&&t<e.length-1?Sl.middle(t-1,t,t+1):Sl.none())(e,t),a=u(O(s,u(0)));return l.fold(a,(e=>n.singleColumnWidth(s[e],o)),((e,t)=>r.calcLeftEdgeDeltas(s,e,t,o,n.minCellWidth(),n.isRelative)),((e,t,l)=>r.calcMiddleDeltas(s,e,t,l,o,n.minCellWidth(),n.isRelative)),((e,t)=>r.calcRightEdgeDeltas(s,e,t,o,n.minCellWidth(),n.isRelative)))})(a,o,i,r,n),d=O(m,((e,t)=>e+a[t]));Dl(s,d,r),n.resizeTable(r.adjustTableWidth,i,c)},kl=e=>z(e,((e,t)=>R(e,(e=>e.column===t.column))?e:e.concat([t])),[]).sort(((e,t)=>e.column-t.column)),El=se("col"),Nl=se("colgroup"),Bl=e=>"tr"===Q(e)||Nl(e),zl=e=>({element:e,colspan:Ot(e,"colspan",1),rowspan:Ot(e,"rowspan",1)}),Al=e=>me(e,"scope").map((e=>e.substr(0,3))),Wl=(e,t=zl)=>{const o=o=>{if(Bl(o))return Nl((r={element:o}).element)?e.colgroup(r):e.row(r);{const r=o,s=(t=>El(t.element)?e.col(t):e.cell(t))(t(r));return n=y.some({item:r,replacement:s}),s}var r};let n=y.none();return{getOrInit:(e,t)=>n.fold((()=>o(e)),(n=>t(e,n.item)?n.replacement:o(e)))}},Ll=e=>t=>{const o=[],n=n=>{const r="td"===e?{scope:null}:{},s=t.replace(n,e,r);return o.push({item:n,sub:s}),s};return{replaceOrInit:(e,t)=>{if(Bl(e)||El(e))return e;{const r=e;return((e,t)=>A(o,(o=>t(o.item,e))))(r,t).fold((()=>n(r)),(o=>t(e,o.item)?o.sub:n(r)))}}}},_l=e=>({unmerge:t=>{const o=Al(t);return o.each((e=>ae(t,"scope",e))),()=>{const n=e.cell({element:t,colspan:1,rowspan:1});return Dt(n,"width"),Dt(t,"width"),o.each((e=>ae(n,"scope",e))),n}},merge:e=>(Dt(e[0],"width"),(()=>{const t=dt(O(e,Al));if(0===t.length)return y.none();{const e=t[0],o=["row","col"];return R(t,(t=>t!==e&&T(o,t)))?y.none():y.from(e)}})().fold((()=>de(e[0],"scope")),(t=>ae(e[0],"scope",t+"group"))),u(e[0]))}),Ml=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","table","thead","tfoot","tbody","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],jl=Ir(),Il=e=>((e,t)=>{const o=e.property().name(t);return T(Ml,o)})(jl,e),Pl=e=>((e,t)=>{const o=e.property().name(t);return T(["ol","ul"],o)})(jl,e),Fl=e=>{const t=se("br"),o=e=>fr(e).bind((o=>{const n=De(o).map((e=>!!Il(e)||!!((e,t)=>T(["br","img","hr","input"],e.property().name(t)))(jl,e)&&"img"!==Q(e))).getOr(!1);return Se(o).map((r=>{return!0===n||("li"===Q(s=r)||st(s,Pl).isSome())||t(o)||Il(r)&&!be(e,r)?[]:[ge.fromTag("br")];var s}))})).getOr([]),n=(()=>{const n=_(e,(e=>{const n=Oe(e);return(e=>M(e,(e=>t(e)||oe(e)&&0===lr(e).trim().length)))(n)?[]:n.concat(o(e))}));return 0===n.length?[ge.fromTag("br")]:n})();_e(e[0]),Le(e[0],n)},Hl=e=>Ms(e,!0),ql=e=>{0===Mt(e).length&&Me(e)},Vl=(e,t)=>({grid:e,cursor:t}),$l=(e,t,o)=>{const n=((e,t,o)=>{var n,r;const s=_o(e).rows;return y.from(null===(r=null===(n=s[t])||void 0===n?void 0:n.cells[o])||void 0===r?void 0:r.element).filter(Hl).orThunk((()=>(e=>H(e,(e=>H(e.cells,(e=>{const t=e.element;return ut(Hl(t),t)})))))(s)))})(e,t,o);return Vl(e,n)},Ul=e=>z(e,((e,t)=>R(e,(e=>e.row===t.row))?e:e.concat([t])),[]).sort(((e,t)=>e.row-t.row)),Gl=(e,t)=>(o,n,r,s,l)=>{const a=Ul(n),c=O(a,(e=>e.row)),i=((e,t,o,n,r,s,l)=>{const{cols:a,rows:c}=_o(e),i=c[t[0]],m=_(t,(e=>((e,t,o)=>{const n=e[t];return _(n.cells,((n,r)=>hl(e,t,r,o)?[]:[n]))})(c,e,r))),d=O(i.cells,((e,t)=>Ss(vl(c,t,r)))),u=[...c];k(t,(e=>{u[e]=l.transformRow(c[e],o)}));const g=[...a,...u],h=((e,t)=>M(t,f)&&Ss(e.cells)?v:(e,o,n)=>!("th"===Q(e.element)&&t[n]))(i,d),p=((e,t)=>(o,n,r)=>y.some(pl(e,o.element,"col",t[r])))(n,d);return bl(g,m,r,s,l.transformCell,p,h)})(o,c,e,t,r,s.replaceOrInit,l);return $l(i,n[0].row,n[0].column)},Kl=Gl("thead",!0),Yl=Gl("tbody",!1),Jl=Gl("tfoot",!1),Ql=(e,t,o)=>{const n=((e,t)=>Ht(e,(()=>t)))(e,o.section),r=$o(n);return Vs(r,t,!0)},Xl=(e,t,o,n)=>((e,t,o,n)=>{const r=$o(t),s=n.getWidths(r,n);Dl(r,s,n)})(0,t,0,n.sizing),Zl=(e,t,o,n)=>((e,t,o,n,r)=>{const s=$o(t),l=n.getWidths(s,n),a=n.pixelWidth(),{newSizes:c,delta:i}=r.calcRedestributedWidths(l,a,o.pixelDelta,n.isRelative);Dl(s,c,n),n.adjustTableWidth(i)})(0,t,o,n.sizing,n.resize),ea=(e,t)=>R(t,(e=>0===e.column&&e.isLocked)),ta=(e,t)=>R(t,(t=>t.column+t.colspan>=e.grid.columns&&t.isLocked)),oa=(e,t)=>{const o=Zo(e),n=kl(t);return z(n,((e,t)=>e+o[t.column].map(Oo).getOr(0)),0)},na=e=>(t,o)=>Js(t,o).filter((o=>!(e?ea:ta)(t,o))).map((e=>({details:e,pixelDelta:oa(t,e)}))),ra=e=>(t,o)=>Ys(t,o).filter((o=>!(e?ea:ta)(t,o.cells))),sa=Ll("th"),la=Ll("td"),aa=Ks(((e,t,o,n)=>{const r=t[0].row,s=Ul(t),l=B(s,((e,t)=>({grid:fl(e.grid,r,t.row+e.delta,o,n.getOrInit),delta:e.delta+1})),{grid:e,delta:0}).grid;return $l(l,r,t[0].column)}),Js,d,d,Wl),ca=Ks(((e,t,o,n)=>{const r=Ul(t),s=r[r.length-1],l=s.row+s.rowspan,a=B(r,((e,t)=>fl(e,l,t.row,o,n.getOrInit)),e);return $l(a,l,t[0].column)}),Js,d,d,Wl),ia=Ks(((e,t,o,n)=>{const r=t.details,s=kl(r),l=s[0].column,a=B(s,((e,t)=>({grid:gl(e.grid,l,t.column+e.delta,o,n.getOrInit),delta:e.delta+1})),{grid:e,delta:0}).grid;return $l(a,r[0].row,l)}),na(!0),Zl,d,Wl),ma=Ks(((e,t,o,n)=>{const r=t.details,s=r[r.length-1],l=s.column+s.colspan,a=kl(r),c=B(a,((e,t)=>gl(e,l,t.column,o,n.getOrInit)),e);return $l(c,r[0].row,l)}),na(!1),Zl,d,Wl),da=Ks(((e,t,o,n)=>{const r=kl(t.details),s=((e,t)=>_(e,(e=>{const o=e.cells,n=B(t,((e,t)=>t>=0&&t<e.length?e.slice(0,t).concat(e.slice(t+1)):e),o);return n.length>0?[Ge(e.element,n,e.section,e.isNew)]:[]})))(e,O(r,(e=>e.column))),l=s.length>0?s[0].cells.length-1:0;return $l(s,r[0].row,Math.min(r[0].column,l))}),((e,t)=>Qs(e,t).map((t=>({details:t,pixelDelta:-oa(e,t)})))),Zl,ql,Wl),ua=Ks(((e,t,o,n)=>{const r=Ul(t),s=((e,t,o)=>{const{rows:n,cols:r}=_o(e);return[...r,...n.slice(0,t),...n.slice(o+1)]})(e,r[0].row,r[r.length-1].row),l=s.length>0?s.length-1:0;return $l(s,Math.min(t[0].row,l),t[0].column)}),Js,d,ql,Wl),fa=Ks(((e,t,o,n)=>{const r=kl(t),s=O(r,(e=>e.column)),l=yl(e,s,!0,o,n.replaceOrInit);return $l(l,t[0].row,t[0].column)}),Qs,d,d,sa),ga=Ks(((e,t,o,n)=>{const r=kl(t),s=O(r,(e=>e.column)),l=yl(e,s,!1,o,n.replaceOrInit);return $l(l,t[0].row,t[0].column)}),Qs,d,d,la),ha=Ks(Kl,Qs,d,d,sa),pa=Ks(Yl,Qs,d,d,la),wa=Ks(Jl,Qs,d,d,la),ba=Ks(((e,t,o,n)=>{const r=xl(e,t,o,n.replaceOrInit);return $l(r,t[0].row,t[0].column)}),Qs,d,d,sa),va=Ks(((e,t,o,n)=>{const r=xl(e,t,o,n.replaceOrInit);return $l(r,t[0].row,t[0].column)}),Qs,d,d,la),ya=Ks(((e,t,o,n)=>{const r=t.cells;Fl(r);const s=((e,t,o,n)=>{const r=_o(e).rows;if(0===r.length)return e;for(let e=t.startRow;e<=t.finishRow;e++)for(let o=t.startCol;o<=t.finishCol;o++){const t=r[e],s=Ao(t,o).isLocked;Bo(t,o,Ue(n(),!1,s))}return e})(e,t.bounds,0,n.merge(r));return Vl(s,y.from(r[0]))}),((e,t)=>((e,t)=>t.mergable)(0,t).filter((t=>Xs(e,t.cells)))),Xl,d,_l),xa=Ks(((e,t,o,n)=>{const r=B(t,((e,t)=>Zs(e,t,o,n.unmerge(t))),e);return Vl(r,y.from(t[0]))}),((e,t)=>((e,t)=>t.unmergable)(0,t).filter((t=>Xs(e,t)))),Xl,d,_l),Ca=Ks(((e,t,o,n)=>{const r=((e,t)=>{const o=Vo(e);return Vs(o,t,!0)})(t.clipboard,t.generators);var s,l;return((e,t,o,n,r)=>{const s=Po(t),l=((e,t,o)=>{const n=Lo(t[0]),r=_o(t).cols.length+e.row,s=D(n-e.column,(t=>t+e.column));return{row:r,column:A(s,(e=>M(o,(t=>t!==e)))).getOr(n-1)}})(e,t,s),a=_o(o).rows,c=ml(l,a,s),i=((e,t,o)=>{if(e.row>=t.length||e.column>Lo(t[0]))return ol.error("invalid start address out of table bounds, row: "+e.row+", column: "+e.column);const n=t.slice(e.row),r=n[0].cells.slice(e.column),s=Lo(o[0]),l=o.length;return ol.value({rowDelta:n.length-l,colDelta:r.length-s})})(l,t,a);return i.map((e=>{const o={...e,colDelta:e.colDelta-c.length},s=cl(t,o,n),i=Po(s),m=ml(l,a,i);return((e,t,o,n,r,s)=>{const l=e.row,a=e.column,c=l+o.length,i=a+Lo(o[0])+s.length,m=j(s,v);for(let e=l;e<c;e++){let s=0;for(let c=a;c<i;c++){if(m[c]){s++;continue}il(t,e,c,r)&&Zs(t,Wo(t[e],c),r,n.cell);const i=c-a-s,d=Ao(o[e-l],i),u=d.element,f=n.replace(u);Bo(t[e],c,Ue(f,!0,d.isLocked))}}return t})(l,s,a,n,r,m)}))})((s=t.row,l=t.column,{row:s,column:l}),e,r,t.generators,o).fold((()=>Vl(e,y.some(t.element))),(e=>$l(e,t.row,t.column)))}),((e,t)=>_t(t.element).bind((o=>Us(e,o).map((e=>({...e,generators:t.generators,clipboard:t.clipboard})))))),Xl,d,Wl),Sa=Ks(((e,t,o,n)=>{const r=_o(e).rows,s=t.cells[0].column,l=r[t.cells[0].row],a=Ql(t.clipboard,t.generators,l),c=dl(s,e,a,t.generators,o);return $l(c,t.cells[0].row,t.cells[0].column)}),ra(!0),d,d,Wl),Ta=Ks(((e,t,o,n)=>{const r=_o(e).rows,s=t.cells[t.cells.length-1].column+t.cells[t.cells.length-1].colspan,l=r[t.cells[0].row],a=Ql(t.clipboard,t.generators,l),c=dl(s,e,a,t.generators,o);return $l(c,t.cells[0].row,t.cells[0].column)}),ra(!1),d,d,Wl),Ra=Ks(((e,t,o,n)=>{const r=_o(e).rows,s=t.cells[0].row,l=r[s],a=Ql(t.clipboard,t.generators,l),c=ul(s,e,a,t.generators,o);return $l(c,t.cells[0].row,t.cells[0].column)}),Ys,d,d,Wl),Da=Ks(((e,t,o,n)=>{const r=_o(e).rows,s=t.cells[t.cells.length-1].row+t.cells[t.cells.length-1].rowspan,l=r[t.cells[0].row],a=Ql(t.clipboard,t.generators,l),c=ul(s,e,a,t.generators,o);return $l(c,t.cells[0].row,t.cells[0].column)}),Ys,d,d,Wl),Oa=(e,t)=>{const o=Vo(e);return Js(o,t).bind((e=>{const t=e[e.length-1],n=e[0].column,r=t.column+t.colspan,s=L(O(o.all,(e=>N(e.cells,(e=>e.column>=n&&e.column<r)))));return Ds(s)})).getOr("")},ka=(e,t)=>{const o=Vo(e);return Js(o,t).bind(Ds).getOr("")},Ea=(e,t)=>{const o=Vo(e);return Js(o,t).bind((e=>{const t=e[e.length-1],n=e[0].row,r=t.row+t.rowspan;return(e=>{const t=O(e,(e=>Rs(e).type)),o=T(t,"header"),n=T(t,"footer");if(o||n){const e=T(t,"body");return!o||e||n?o||e||!n?y.none():y.some("footer"):y.some("header")}return y.some("body")})(o.all.slice(n,r))})).getOr("")},Na=(e,t)=>e.dispatch("NewRow",{node:t}),Ba=(e,t)=>e.dispatch("NewCell",{node:t}),za=(e,t,o)=>{e.dispatch("TableModified",{...o,table:t})},Aa={structure:!1,style:!0},Wa={structure:!0,style:!1},La={structure:!0,style:!0},_a=e=>t=>t.options.get(e),Ma="100%",ja=e=>{var t;const o=e.dom,n=null!==(t=o.getParent(e.selection.getStart(),o.isBlock))&&void 0!==t?t:e.getBody();return ko(ge.fromDom(n))+"px"},Ia=e=>y.from(e.options.get("table_clone_elements")),Pa=_a("table_header_type"),Fa=_a("table_column_resizing"),Ha=e=>"preservetable"===Fa(e),qa=e=>"resizetable"===Fa(e),Va=_a("table_sizing_mode"),$a=e=>"relative"===Va(e),Ua=e=>"fixed"===Va(e),Ga=e=>"responsive"===Va(e),Ka=_a("table_resize_bars"),Ya=_a("table_style_by_css"),Ja=e=>{const t=e.options,o=t.get("table_default_attributes");return t.isSet("table_default_attributes")?o:((e,t)=>Ga(e)||Ya(e)?t:Ua(e)?{...t,width:ja(e)}:{...t,width:Ma})(e,o)},Qa=_a("table_use_colgroups"),Xa=(e,t)=>$a(e)?nr(t):Ua(e)?or(t):tr(t),Za=(e,t,o)=>{const n=e=>"table"===Q(Tr(e)),r=Ia(e),s=qa(e)?d:fs,l=t=>{switch(Pa(e)){case"section":return zs();case"sectionCells":return As();case"cells":return Ws();default:return((e,t)=>{var o;switch((o=Vo(e),H(o.all,(e=>{const t=Rs(e);return"header"===t.type?y.from(t.subType):y.none()}))).getOr(t)){case"section":return Es();case"sectionCells":return Ns();case"cells":return Bs()}})(t,"section")}},a=(n,s,a,c)=>(i,m,d=!1)=>{Dr(i);const u=ge.fromDom(e.getDoc()),f=Cr(a,u,r),g={sizing:Xa(e,i),resize:qa(e)?vs():ys(),section:l(i)};return s(i)?n(i,m,f,g).bind((n=>{t.refresh(i.dom),k(n.newRows,(t=>{Na(e,t.dom)})),k(n.newCells,(t=>{Ba(e,t.dom)}));const r=((t,n)=>n.cursor.fold((()=>{const n=Mt(t);return P(n).filter(Xe).map((n=>{o.clearSelectedCells(t.dom);const r=e.dom.createRng();return r.selectNode(n.dom),e.selection.setRng(r),ae(n,"data-mce-selected","1"),r}))}),(n=>{const r=ds(us,n),s=e.dom.createRng();return s.setStart(r.element.dom,r.offset),s.setEnd(r.element.dom,r.offset),e.selection.setRng(s),o.clearSelectedCells(t.dom),y.some(s)})))(i,n);return Xe(i)&&(Dr(i),d||za(e,i.dom,c)),r.map((e=>({rng:e,effect:c})))})):y.none()},c=a(ua,(t=>!1===n(e)||xs(t).rows>1),d,Wa),i=a(da,(t=>!1===n(e)||xs(t).columns>1),d,Wa);return{deleteRow:c,deleteColumn:i,insertRowsBefore:a(aa,v,d,Wa),insertRowsAfter:a(ca,v,d,Wa),insertColumnsBefore:a(ia,v,s,Wa),insertColumnsAfter:a(ma,v,s,Wa),mergeCells:a(ya,v,d,Wa),unmergeCells:a(xa,v,d,Wa),pasteColsBefore:a(Sa,v,d,Wa),pasteColsAfter:a(Ta,v,d,Wa),pasteRowsBefore:a(Ra,v,d,Wa),pasteRowsAfter:a(Da,v,d,Wa),pasteCells:a(Ca,v,d,La),makeCellsHeader:a(ba,v,d,Wa),unmakeCellsHeader:a(va,v,d,Wa),makeColumnsHeader:a(fa,v,d,Wa),unmakeColumnsHeader:a(ga,v,d,Wa),makeRowsHeader:a(ha,v,d,Wa),makeRowsBody:a(pa,v,d,Wa),makeRowsFooter:a(wa,v,d,Wa),getTableRowType:Ea,getTableCellType:ka,getTableColType:Oa}},ec=(e,t,o)=>{const n=Ot(e,t,1);1===o||n<=1?de(e,t):ae(e,t,Math.min(o,n))},tc=(e,t)=>o=>{const n=o.column+o.colspan-1,r=o.column;return n>=e&&r<t},oc=Cl([{invalid:["raw"]},{pixels:["value"]},{percent:["value"]}]),nc=(e,t,o)=>{const n=o.substring(0,o.length-e.length),r=parseFloat(n);return n===r.toString()?t(r):oc.invalid(o)},rc={...oc,from:e=>pt(e,"%")?nc("%",oc.percent,e):pt(e,"px")?nc("px",oc.pixels,e):oc.invalid(e)},sc=(e,t,o)=>{const n=rc.from(o),r=M(e,(e=>"0px"===e))?((e,t)=>{const o=e.fold((()=>u("")),(e=>u(e/t+"px")),(()=>u(100/t+"%")));return D(t,o)})(n,e.length):((e,t,o)=>e.fold((()=>t),(e=>((e,t,o)=>{const n=o/t;return O(e,(e=>rc.from(e).fold((()=>e),(e=>e*n+"px"),(e=>e/100*o+"px"))))})(t,o,e)),(e=>((e,t)=>O(e,(e=>rc.from(e).fold((()=>e),(e=>e/t*100+"%"),(e=>e+"%")))))(t,o))))(n,e,t);return cc(r)},lc=(e,t)=>0===e.length?t:B(e,((e,t)=>rc.from(t).fold(u(0),f,f)+e),0),ac=(e,t)=>rc.from(e).fold(u(e),(e=>e+t+"px"),(e=>e+t+"%")),cc=e=>{if(0===e.length)return e;const t=B(e,((e,t)=>{const o=rc.from(t).fold((()=>({value:t,remainder:0})),(e=>((e,t)=>{const o=Math.floor(e);return{value:o+"px",remainder:e-o}})(e)),(e=>({value:e+"%",remainder:0})));return{output:[o.value].concat(e.output),remainder:e.remainder+o.remainder}}),{output:[],remainder:0}),o=t.output;return o.slice(0,o.length-1).concat([ac(o[o.length-1],Math.round(t.remainder))])},ic=rc.from,mc=e=>ic(e).fold(u("px"),u("px"),u("%")),dc=(e,t,o)=>{const n=Vo(e),r=n.all,s=Yo(n),l=Jo(n);t.each((t=>{const o=mc(t),r=Do(e),a=((e,t)=>Kn(e,t,$n,Yn))(n,e),c=sc(a,r,t);Qo(n)?((e,t,o)=>{k(t,((t,n)=>{const r=lc([e[n]],zt());xt(t.element,"width",r+o)}))})(c,l,o):((e,t,o)=>{k(t,(t=>{const n=e.slice(t.column,t.colspan+t.column),r=lc(n,zt());xt(t.element,"width",r+o)}))})(c,s,o),xt(e,"width",t)})),o.each((t=>{const o=mc(t),l=sn(e),a=((e,t,o)=>Qn(e,t,o,Un,Yn))(n,e,Sn);((e,t,o,n)=>{k(o,(t=>{const o=e.slice(t.row,t.rowspan+t.row),r=lc(o,At());xt(t.element,"height",r+n)})),k(t,((t,o)=>{xt(t.element,"height",e[o])}))})(sc(a,l,t),r,s,o),xt(e,"height",t)}))},uc=e=>Mn(e).exists((e=>kn.test(e))),fc=e=>Mn(e).exists((e=>En.test(e))),gc=e=>Mn(e).isNone(),hc=e=>{de(e,"width")},pc=e=>{const t=Hn(e);dc(e,y.some(t),y.none()),hc(e)},wc=e=>{const t=(e=>Do(e)+"px")(e);dc(e,y.some(t),y.none()),hc(e)},bc=e=>{Dt(e,"width");const t=jt(e),o=t.length>0?t:Mt(e);k(o,(e=>{Dt(e,"width"),hc(e)})),hc(e)},vc={styles:{"border-collapse":"collapse",width:"100%"},attributes:{border:"1"},colGroups:!1},yc=(e,t,o,n)=>D(e,(e=>((e,t,o,n)=>{const r=ge.fromTag("tr");for(let s=0;s<e;s++){const e=ge.fromTag(n<t||s<o?"th":"td");s<o&&ae(e,"scope","row"),n<t&&ae(e,"scope","col"),ze(e,ge.fromTag("br")),ze(r,e)}return r})(t,o,n,e))),xc=(e,t)=>{e.selection.select(t.dom,!0),e.selection.collapse(!0)},Cc=(e,t,o,r,s)=>{const l=(e=>{const t=e.options,o=t.get("table_default_styles");return t.isSet("table_default_styles")?o:((e,t)=>Ga(e)||!Ya(e)?t:Ua(e)?{...t,width:ja(e)}:{...t,width:Ma})(e,o)})(e),a={styles:l,attributes:Ja(e),colGroups:Qa(e)};return e.undoManager.ignore((()=>{const n=((e,t,o,n,r,s=vc)=>{const l=ge.fromTag("table"),a="cells"!==r;Ct(l,s.styles),ce(l,s.attributes),s.colGroups&&ze(l,(e=>{const t=ge.fromTag("colgroup");return D(e,(()=>ze(t,ge.fromTag("col")))),t})(t));const c=Math.min(e,o);if(a&&o>0){const e=ge.fromTag("thead");ze(l,e);const s=yc(o,t,"sectionCells"===r?c:0,n);Le(e,s)}const i=ge.fromTag("tbody");ze(l,i);const m=yc(a?e-c:e,t,a?0:o,n);return Le(i,m),l})(o,t,s,r,Pa(e),a);ae(n,"data-mce-id","__mce");const l=(e=>{const t=ge.fromTag("div"),o=ge.fromDom(e.dom.cloneNode(!0));return ze(t,o),(e=>e.dom.innerHTML)(t)})(n);e.insertContent(l),e.addVisual()})),ct(Tr(e),'table[data-mce-id="__mce"]').map((t=>(Ua(e)?wc(t):Ga(e)?bc(t):($a(e)||(e=>n(e)&&-1!==e.indexOf("%"))(l.width))&&pc(t),Dr(t),de(t,"data-mce-id"),((e,t)=>{k(nt(t,"tr"),(t=>{Na(e,t.dom),k(nt(t,"th,td"),(t=>{Ba(e,t.dom)}))}))})(e,t),((e,t)=>{ct(t,"td,th").each(h(xc,e))})(e,t),t.dom))).getOr(null)};var Sc=tinymce.util.Tools.resolve("tinymce.FakeClipboard");const Tc="x-tinymce/dom-table-",Rc=Tc+"rows",Dc=Tc+"columns",Oc=e=>{const t=Sc.FakeClipboardItem(e);Sc.write([t])},kc=e=>{var t;const o=null!==(t=Sc.read())&&void 0!==t?t:[];return H(o,(t=>y.from(t.getType(e))))},Ec=e=>{kc(e).isSome()&&Sc.clear()},Nc=e=>{e.fold(zc,(e=>Oc({[Rc]:e})))},Bc=()=>kc(Rc),zc=()=>Ec(Rc),Ac=e=>{e.fold(Lc,(e=>Oc({[Dc]:e})))},Wc=()=>kc(Dc),Lc=()=>Ec(Dc),_c=e=>ns(Or(e),Rr(e)),Mc=(e,t)=>{const o=Rr(e),s=e=>It(e,o),l=t=>(e=>rs(Or(e),Rr(e)))(e).bind((e=>s(e).map((o=>t(o,e))))),a=t=>{e.focus()},c=(t,o=!1)=>l(((n,r)=>{const s=ts(ss(e),n,r);t(n,s,o).each(a)})),i=()=>l(((t,o)=>((e,t,o)=>{const n=Vo(e);return Js(n,t).bind((e=>{const t=Vs(n,o,!1),r=_o(t).rows.slice(e[0].row,e[e.length-1].row+e[e.length-1].rowspan),s=_(r,(e=>{const t=N(e.cells,(e=>!e.isLocked));return t.length>0?[{...e,cells:t}]:[]})),l=$s(s);return ut(l.length>0,l)})).map((e=>O(e,(e=>{const t=Pe(e.element);return k(e.cells,(e=>{const o=Fe(e.element);Is(o,"colspan",e.colspan,1),Is(o,"rowspan",e.rowspan,1),ze(t,o)})),t}))))})(t,ts(ss(e),t,o),Cr(d,ge.fromDom(e.getDoc()),y.none())))),u=()=>l(((t,o)=>((e,t)=>{const o=Vo(e);return Qs(o,t).map((e=>{const t=e[e.length-1],n=e[0].column,r=t.column+t.colspan,s=((e,t,o)=>{if(Qo(e)){const n=N(Jo(e),tc(t,o)),r=O(n,(e=>{const n=Fe(e.element);return ec(n,"span",o-t),n})),s=ge.fromTag("colgroup");return Le(s,r),[s]}return[]})(o,n,r),l=((e,t,o)=>O(e.all,(e=>{const n=N(e.cells,tc(t,o)),r=O(n,(e=>{const n=Fe(e.element);return ec(n,"colspan",o-t),n})),s=ge.fromTag("tr");return Le(s,r),s})))(o,n,r);return[...s,...l]}))})(t,ts(ss(e),t,o)))),f=(t,o)=>o().each((o=>{const n=O(o,(e=>Fe(e)));l(((o,r)=>{const s=Sr(ge.fromDom(e.getDoc())),l=((e,t,o,n)=>({selection:Qr(e),clipboard:o,generators:n}))(ss(e),0,n,s);t(o,l).each(a)}))})),g=e=>(t,o)=>((e,t)=>J(e,t)?y.from(e.type):y.none())(o,"type").each((t=>{c(e(t),o.no_events)}));$({mceTableSplitCells:()=>c(t.unmergeCells),mceTableMergeCells:()=>c(t.mergeCells),mceTableInsertRowBefore:()=>c(t.insertRowsBefore),mceTableInsertRowAfter:()=>c(t.insertRowsAfter),mceTableInsertColBefore:()=>c(t.insertColumnsBefore),mceTableInsertColAfter:()=>c(t.insertColumnsAfter),mceTableDeleteCol:()=>c(t.deleteColumn),mceTableDeleteRow:()=>c(t.deleteRow),mceTableCutCol:()=>u().each((e=>{Ac(e),c(t.deleteColumn)})),mceTableCutRow:()=>i().each((e=>{Nc(e),c(t.deleteRow)})),mceTableCopyCol:()=>u().each((e=>Ac(e))),mceTableCopyRow:()=>i().each((e=>Nc(e))),mceTablePasteColBefore:()=>f(t.pasteColsBefore,Wc),mceTablePasteColAfter:()=>f(t.pasteColsAfter,Wc),mceTablePasteRowBefore:()=>f(t.pasteRowsBefore,Bc),mceTablePasteRowAfter:()=>f(t.pasteRowsAfter,Bc),mceTableDelete:()=>_c(e).each((t=>{It(t,o).filter(p(o)).each((t=>{const o=ge.fromText("");if(Ne(t,o),Me(t),e.dom.isEmpty(e.getBody()))e.setContent(""),e.selection.setCursorLocation();else{const t=e.dom.createRng();t.setStart(o.dom,0),t.setEnd(o.dom,0),e.selection.setRng(t),e.nodeChanged()}}))})),mceTableCellToggleClass:(t,o)=>{l((t=>{const n=ss(e),r=M(n,(t=>e.formatter.match("tablecellclass",{value:o},t.dom))),s=r?e.formatter.remove:e.formatter.apply;k(n,(e=>s("tablecellclass",{value:o},e.dom))),za(e,t.dom,Aa)}))},mceTableToggleClass:(t,o)=>{l((t=>{e.formatter.toggle("tableclass",{value:o},t.dom),za(e,t.dom,Aa)}))},mceTableToggleCaption:()=>{_c(e).each((t=>{It(t,o).each((o=>{at(o,"caption").fold((()=>{const t=ge.fromTag("caption");ze(t,ge.fromText("Caption")),((e,t,o)=>{ke(e,0).fold((()=>{ze(e,t)}),(e=>{Ee(e,t)}))})(o,t),e.selection.setCursorLocation(t.dom,0)}),(n=>{se("caption")(t)&&we("td",o).each((t=>e.selection.setCursorLocation(t.dom,0))),Me(n)})),za(e,o.dom,Wa)}))}))},mceTableSizingMode:(t,n)=>(t=>_c(e).each((n=>{Ga(e)||Ua(e)||$a(e)||It(n,o).each((o=>{"relative"!==t||uc(o)?"fixed"!==t||fc(o)?"responsive"!==t||gc(o)||bc(o):wc(o):pc(o),Dr(o),za(e,o.dom,Wa)}))})))(n),mceTableCellType:g((e=>"th"===e?t.makeCellsHeader:t.unmakeCellsHeader)),mceTableColType:g((e=>"th"===e?t.makeColumnsHeader:t.unmakeColumnsHeader)),mceTableRowType:g((e=>{switch(e){case"header":return t.makeRowsHeader;case"footer":return t.makeRowsFooter;default:return t.makeRowsBody}}))},((t,o)=>e.addCommand(o,t))),e.addCommand("mceInsertTable",((t,o)=>{((e,t,o,n={})=>{const r=e=>m(e)&&e>0;if(r(t)&&r(o)){const r=n.headerRows||0,s=n.headerColumns||0;return Cc(e,o,t,s,r)}console.error("Invalid values for mceInsertTable - rows and columns values are required to insert a table.")})(e,o.rows,o.columns,o.options)})),e.addCommand("mceTableApplyCellStyle",((t,o)=>{const l=e=>"tablecell"+e.toLowerCase().replace("-","");if(!r(o))return;const a=ss(e);if(0===a.length)return;const c=((e,t)=>{const o={};return((e,t,o,n)=>{$(e,((e,r)=>{(t(e,r)?o:n)(e,r)}))})(e,t,(e=>(t,o)=>{e[o]=t})(o),d),o})(o,((t,o)=>e.formatter.has(l(o))&&n(t)));(e=>{for(const t in e)if(V.call(e,t))return!1;return!0})(c)||($(c,((t,o)=>{const n=l(o);k(a,(o=>{""===t?e.formatter.remove(n,{value:null},o.dom,!0):e.formatter.apply(n,{value:t},o.dom)}))})),s(a[0]).each((t=>za(e,t.dom,Aa))))}))},jc=Cl([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Ic={before:jc.before,on:jc.on,after:jc.after,cata:(e,t,o,n)=>e.fold(t,o,n),getStart:e=>e.fold(f,f,f)},Pc=(e,t)=>({selection:e,kill:t}),Fc=(e,t)=>{const o=e.document.createRange();return o.selectNode(t.dom),o},Hc=(e,t)=>{const o=e.document.createRange();return qc(o,t),o},qc=(e,t)=>e.selectNodeContents(t.dom),Vc=(e,t,o)=>{const n=e.document.createRange();var r;return r=n,t.fold((e=>{r.setStartBefore(e.dom)}),((e,t)=>{r.setStart(e.dom,t)}),(e=>{r.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},$c=(e,t,o,n,r)=>{const s=e.document.createRange();return s.setStart(t.dom,o),s.setEnd(n.dom,r),s},Uc=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),Gc=Cl([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Kc=(e,t,o)=>t(ge.fromDom(o.startContainer),o.startOffset,ge.fromDom(o.endContainer),o.endOffset),Yc=(e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:u(e),rtl:y.none}),relative:(t,o)=>({ltr:$t((()=>Vc(e,t,o))),rtl:$t((()=>y.some(Vc(e,o,t))))}),exact:(t,o,n,r)=>({ltr:$t((()=>$c(e,t,o,n,r))),rtl:$t((()=>y.some($c(e,n,r,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>Gc.rtl(ge.fromDom(e.endContainer),e.endOffset,ge.fromDom(e.startContainer),e.startOffset))).getOrThunk((()=>Kc(0,Gc.ltr,o))):Kc(0,Gc.ltr,o)})(0,o)},Jc=(e,t)=>Yc(e,t).match({ltr:(t,o,n,r)=>{const s=e.document.createRange();return s.setStart(t.dom,o),s.setEnd(n.dom,r),s},rtl:(t,o,n,r)=>{const s=e.document.createRange();return s.setStart(n.dom,r),s.setEnd(t.dom,o),s}});Gc.ltr,Gc.rtl;const Qc=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),Xc=(e,t,o,n)=>({start:Ic.on(e,t),finish:Ic.on(o,n)}),Zc=(e,t)=>{const o=Jc(e,t);return Qc(ge.fromDom(o.startContainer),o.startOffset,ge.fromDom(o.endContainer),o.endOffset)},ei=Xc,ti=(e,t,o,n,r)=>be(o,n)?y.none():$r(o,n,t).bind((t=>{const n=t.boxes.getOr([]);return n.length>1?(r(e,n,t.start,t.finish),y.some(Pc(y.some(ei(o,0,o,ir(o))),!0))):y.none()})),oi=(e,t)=>({item:e,mode:t}),ni=(e,t,o,n=ri)=>e.property().parent(t).map((e=>oi(e,n))),ri=(e,t,o,n=si)=>o.sibling(e,t).map((e=>oi(e,n))),si=(e,t,o,n=si)=>{const r=e.property().children(t);return o.first(r).map((e=>oi(e,n)))},li=[{current:ni,next:ri,fallback:y.none()},{current:ri,next:si,fallback:y.some(ni)},{current:si,next:si,fallback:y.some(ri)}],ai=(e,t,o,n,r=li)=>A(r,(e=>e.current===o)).bind((o=>o.current(e,t,n,o.next).orThunk((()=>o.fallback.bind((o=>ai(e,t,o,n))))))),ci=(e,t,o,n,r,s)=>ai(e,t,n,r).bind((t=>s(t.item)?y.none():o(t.item)?y.some(t.item):ci(e,t.item,o,t.mode,r,s))),ii=e=>t=>0===e.property().children(t).length,mi=(e,t,o,n)=>ci(e,t,o,ri,{sibling:(e,t)=>e.query().prevSibling(t),first:e=>e.length>0?y.some(e[e.length-1]):y.none()},n),di=(e,t,o,n)=>ci(e,t,o,ri,{sibling:(e,t)=>e.query().nextSibling(t),first:e=>e.length>0?y.some(e[0]):y.none()},n),ui=Ir(),fi=(e,t)=>((e,t,o)=>mi(e,t,ii(e),o))(ui,e,t),gi=(e,t)=>((e,t,o)=>di(e,t,ii(e),o))(ui,e,t),hi=Cl([{none:["message"]},{success:[]},{failedUp:["cell"]},{failedDown:["cell"]}]),pi=e=>it(e,"tr"),wi={...hi,verify:(e,t,o,n,r,s,l)=>it(n,"td,th",l).bind((o=>it(t,"td,th",l).map((t=>be(o,t)?be(n,o)&&ir(o)===r?s(t):hi.none("in same cell"):qr(pi,[o,t]).fold((()=>((e,t,o)=>{const n=e.getRect(t),r=e.getRect(o);return r.right>n.left&&r.left<n.right})(e,t,o)?hi.success():s(t)),(e=>s(t))))))).getOr(hi.none("default")),cata:(e,t,o,n,r)=>e.fold(t,o,n,r)},bi=se("br"),vi=(e,t,o)=>t(e,o).bind((e=>oe(e)&&0===lr(e).trim().length?vi(e,t,o):y.some(e))),yi=(e,t,o,n)=>((e,t)=>ke(e,t).filter(bi).orThunk((()=>ke(e,t-1).filter(bi))))(t,o).bind((t=>n.traverse(t).fold((()=>vi(t,n.gather,e).map(n.relative)),(e=>(e=>Se(e).bind((t=>{const o=Oe(t);return((e,t)=>W(e,h(be,t)))(o,e).map((n=>((e,t,o,n)=>({parent:e,children:t,element:o,index:n}))(t,o,e,n)))})))(e).map((e=>Ic.on(e.parent,e.index))))))),xi=(e,t)=>({left:e.left,top:e.top+t,right:e.right,bottom:e.bottom+t}),Ci=(e,t)=>({left:e.left,top:e.top-t,right:e.right,bottom:e.bottom-t}),Si=(e,t,o)=>({left:e.left+t,top:e.top+o,right:e.right+t,bottom:e.bottom+o}),Ti=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom}),Ri=(e,t)=>y.some(e.getRect(t)),Di=(e,t,o)=>te(t)?Ri(e,t).map(Ti):oe(t)?((e,t,o)=>o>=0&&o<ir(t)?e.getRangedRect(t,o,t,o+1):o>0?e.getRangedRect(t,o-1,t,o):y.none())(e,t,o).map(Ti):y.none(),Oi=(e,t)=>te(t)?Ri(e,t).map(Ti):oe(t)?e.getRangedRect(t,0,t,ir(t)).map(Ti):y.none(),ki=Cl([{none:[]},{retry:["caret"]}]),Ei=(e,t,o)=>{return(n=t,r=Il,rt(((e,t)=>t(e)),st,n,r,undefined)).fold(b,(t=>Oi(e,t).exists((e=>((e,t)=>e.left<t.left||Math.abs(t.right-e.left)<1||e.left>t.right)(o,e)))));var n,r},Ni={point:e=>e.bottom,adjuster:(e,t,o,n,r)=>{const s=xi(r,5);return Math.abs(o.bottom-n.bottom)<1||o.top>r.bottom?ki.retry(s):o.top===r.bottom?ki.retry(xi(r,1)):Ei(e,t,r)?ki.retry(Si(s,5,0)):ki.none()},move:xi,gather:gi},Bi=(e,t,o,n,r)=>0===r?y.some(n):((e,t,o)=>e.elementFromPoint(t,o).filter((e=>"table"===Q(e))).isSome())(e,n.left,t.point(n))?((e,t,o,n,r)=>Bi(e,t,o,t.move(n,5),r))(e,t,o,n,r-1):e.situsFromPoint(n.left,t.point(n)).bind((s=>s.start.fold(y.none,(s=>Oi(e,s).bind((l=>t.adjuster(e,s,l,o,n).fold(y.none,(n=>Bi(e,t,o,n,r-1))))).orThunk((()=>y.some(n)))),y.none))),zi=(e,t,o)=>{const n=e.move(o,5),r=Bi(t,e,o,n,100).getOr(n);return((e,t,o)=>e.point(t)>o.getInnerHeight()?y.some(e.point(t)-o.getInnerHeight()):e.point(t)<0?y.some(-e.point(t)):y.none())(e,r,t).fold((()=>t.situsFromPoint(r.left,e.point(r))),(o=>(t.scrollBy(0,o),t.situsFromPoint(r.left,e.point(r)-o))))},Ai={tryUp:h(zi,{point:e=>e.top,adjuster:(e,t,o,n,r)=>{const s=Ci(r,5);return Math.abs(o.top-n.top)<1||o.bottom<r.top?ki.retry(s):o.bottom===r.top?ki.retry(Ci(r,1)):Ei(e,t,r)?ki.retry(Si(s,5,0)):ki.none()},move:Ci,gather:fi}),tryDown:h(zi,Ni),getJumpSize:u(5)},Wi=(e,t,o)=>e.getSelection().bind((n=>((e,t,o,n)=>{const r=bi(t)?((e,t,o)=>o.traverse(t).orThunk((()=>vi(t,o.gather,e))).map(o.relative))(e,t,n):yi(e,t,o,n);return r.map((e=>({start:e,finish:e})))})(t,n.finish,n.foffset,o).fold((()=>y.some(as(n.finish,n.foffset))),(r=>{const s=e.fromSitus(r);return l=wi.verify(e,n.finish,n.foffset,s.finish,s.foffset,o.failure,t),wi.cata(l,(e=>y.none()),(()=>y.none()),(e=>y.some(as(e,0))),(e=>y.some(as(e,ir(e)))));var l})))),Li=(e,t,o,n,r,s)=>0===s?y.none():ji(e,t,o,n,r).bind((l=>{const a=e.fromSitus(l),c=wi.verify(e,o,n,a.finish,a.foffset,r.failure,t);return wi.cata(c,(()=>y.none()),(()=>y.some(l)),(l=>be(o,l)&&0===n?_i(e,o,n,Ci,r):Li(e,t,l,0,r,s-1)),(l=>be(o,l)&&n===ir(l)?_i(e,o,n,xi,r):Li(e,t,l,ir(l),r,s-1)))})),_i=(e,t,o,n,r)=>Di(e,t,o).bind((t=>Mi(e,r,n(t,Ai.getJumpSize())))),Mi=(e,t,o)=>{const n=Co().browser;return n.isChromium()||n.isSafari()||n.isFirefox()?t.retry(e,o):y.none()},ji=(e,t,o,n,r)=>Di(e,o,n).bind((t=>Mi(e,r,t))),Ii=(e,t,o,n,r)=>it(n,"td,th",t).bind((n=>it(n,"table",t).bind((s=>((e,t)=>st(e,(e=>Se(e).exists((e=>be(e,t)))),void 0).isSome())(r,s)?((e,t,o)=>Wi(e,t,o).bind((n=>Li(e,t,n.element,n.offset,o,20).map(e.fromSitus))))(e,t,o).bind((e=>it(e.finish,"td,th",t).map((t=>({start:n,finish:t,range:e}))))):y.none())))),Pi=(e,t,o,n,r,s)=>s(n,t).orThunk((()=>Ii(e,t,o,n,r).map((e=>{const t=e.range;return Pc(y.some(ei(t.start,t.soffset,t.finish,t.foffset)),!0)})))),Fi=(e,t)=>it(e,"tr",t).bind((e=>it(e,"table",t).bind((o=>{const n=nt(o,"tr");return be(e,n[0])?((e,t,o)=>mi(ui,e,(e=>fr(e).isSome()),o))(o,0,t).map((e=>{const t=ir(e);return Pc(y.some(ei(e,t,e,t)),!0)})):y.none()})))),Hi=(e,t)=>it(e,"tr",t).bind((e=>it(e,"table",t).bind((o=>{const n=nt(o,"tr");return be(e,n[n.length-1])?((e,t,o)=>di(ui,e,(e=>ur(e).isSome()),o))(o,0,t).map((e=>Pc(y.some(ei(e,0,e,0)),!0))):y.none()})))),qi=(e,t,o,n,r,s,l)=>Ii(e,o,n,r,s).bind((e=>ti(t,o,e.start,e.finish,l))),Vi=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},$i=()=>{const e=(e=>{const t=Vi(y.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(y.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(y.some(e))}}})(d);return{...e,on:t=>e.get().each(t)}},Ui=(e,t)=>it(e,"td,th",t),Gi={traverse:De,gather:gi,relative:Ic.before,retry:Ai.tryDown,failure:wi.failedDown},Ki={traverse:Re,gather:fi,relative:Ic.before,retry:Ai.tryUp,failure:wi.failedUp},Yi=e=>t=>t===e,Ji=Yi(38),Qi=Yi(40),Xi=e=>e>=37&&e<=40,Zi={isBackward:Yi(37),isForward:Yi(39)},em={isBackward:Yi(39),isForward:Yi(37)},tm=Cl([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),om={domRange:tm.domRange,relative:tm.relative,exact:tm.exact,exactFromRange:e=>tm.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>ge.fromDom(e.startContainer),relative:(e,t)=>Ic.getStart(e),exact:(e,t,o,n)=>e}))(e);return ge.fromDom(Ce(t).dom.defaultView)},range:Qc},nm=document.caretPositionFromPoint?(e,t,o)=>{var n,r;return y.from(null===(r=(n=e.dom).caretPositionFromPoint)||void 0===r?void 0:r.call(n,t,o)).bind((t=>{if(null===t.offsetNode)return y.none();const o=e.dom.createRange();return o.setStart(t.offsetNode,t.offset),o.collapse(),y.some(o)}))}:document.caretRangeFromPoint?(e,t,o)=>{var n,r;return y.from(null===(r=(n=e.dom).caretRangeFromPoint)||void 0===r?void 0:r.call(n,t,o))}:y.none,rm=(e,t)=>{const o=Q(e);return"input"===o?Ic.after(e):T(["br","img"],o)?0===t?Ic.before(e):Ic.after(e):Ic.on(e,t)},sm=e=>y.from(e.getSelection()),lm=(e,t)=>{sm(e).each((e=>{e.removeAllRanges(),e.addRange(t)}))},am=(e,t,o,n,r)=>{const s=$c(e,t,o,n,r);lm(e,s)},cm=(e,t)=>Yc(e,t).match({ltr:(t,o,n,r)=>{am(e,t,o,n,r)},rtl:(t,o,n,r)=>{sm(e).each((s=>{if(s.setBaseAndExtent)s.setBaseAndExtent(t.dom,o,n.dom,r);else if(s.extend)try{((e,t,o,n,r,s)=>{t.collapse(o.dom,n),t.extend(r.dom,s)})(0,s,t,o,n,r)}catch(s){am(e,n,r,t,o)}else am(e,n,r,t,o)}))}}),im=(e,t,o,n,r)=>{const s=((e,t,o,n)=>{const r=rm(e,t),s=rm(o,n);return om.relative(r,s)})(t,o,n,r);cm(e,s)},mm=(e,t,o)=>{const n=((e,t)=>{const o=e.fold(Ic.before,rm,Ic.after),n=t.fold(Ic.before,rm,Ic.after);return om.relative(o,n)})(t,o);cm(e,n)},dm=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return y.some(Qc(ge.fromDom(t.startContainer),t.startOffset,ge.fromDom(o.endContainer),o.endOffset))}return y.none()},um=e=>{if(null===e.anchorNode||null===e.focusNode)return dm(e);{const t=ge.fromDom(e.anchorNode),o=ge.fromDom(e.focusNode);return((e,t,o,n)=>{const r=((e,t,o,n)=>{const r=xe(e).dom.createRange();return r.setStart(e.dom,t),r.setEnd(o.dom,n),r})(e,t,o,n),s=be(e,o)&&t===n;return r.collapsed&&!s})(t,e.anchorOffset,o,e.focusOffset)?y.some(Qc(t,e.anchorOffset,o,e.focusOffset)):dm(e)}},fm=(e,t,o=!0)=>{const n=(o?Hc:Fc)(e,t);lm(e,n)},gm=e=>(e=>sm(e).filter((e=>e.rangeCount>0)).bind(um))(e).map((e=>om.exact(e.start,e.soffset,e.finish,e.foffset))),hm=e=>({elementFromPoint:(t,o)=>ge.fromPoint(ge.fromDom(e.document),t,o),getRect:e=>e.dom.getBoundingClientRect(),getRangedRect:(t,o,n,r)=>{const s=om.exact(t,o,n,r);return((e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?y.some(o).map(Uc):y.none()})(Jc(e,t)))(e,s)},getSelection:()=>gm(e).map((t=>Zc(e,t))),fromSitus:t=>{const o=om.relative(t.start,t.finish);return Zc(e,o)},situsFromPoint:(t,o)=>((e,t,o)=>((e,t,o)=>{const n=ge.fromDom(e.document);return nm(n,t,o).map((e=>Qc(ge.fromDom(e.startContainer),e.startOffset,ge.fromDom(e.endContainer),e.endOffset)))})(e,t,o))(e,t,o).map((e=>Xc(e.start,e.soffset,e.finish,e.foffset))),clearSelection:()=>{(e=>{sm(e).each((e=>e.removeAllRanges()))})(e)},collapseSelection:(t=!1)=>{gm(e).each((o=>o.fold((e=>e.collapse(t)),((o,n)=>{const r=t?o:n;mm(e,r,r)}),((o,n,r,s)=>{const l=t?o:r,a=t?n:s;im(e,l,a,l,a)}))))},setSelection:t=>{im(e,t.start,t.soffset,t.finish,t.foffset)},setRelativeSelection:(t,o)=>{mm(e,t,o)},selectNode:t=>{fm(e,t,!1)},selectContents:t=>{fm(e,t)},getInnerHeight:()=>e.innerHeight,getScrollY:()=>(e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return cn(o,n)})(ge.fromDom(e.document)).top,scrollBy:(t,o)=>{((e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollBy(e,t)})(t,o,ge.fromDom(e.document))}}),pm=(e,t)=>({rows:e,cols:t}),wm=e=>void 0!==e.dom.classList,bm=(e,t)=>((e,t,o)=>{const n=((e,t)=>{const o=ie(e,t);return void 0===o||""===o?[]:o.split(" ")})(e,t).concat([o]);return ae(e,t,n.join(" ")),!0})(e,"class",t),vm=(e,t)=>{wm(e)?e.dom.classList.add(t):bm(e,t)},ym=(e,t)=>wm(e)&&e.dom.classList.contains(t),xm=()=>({tag:"none"}),Cm=e=>({tag:"multiple",elements:e}),Sm=e=>({tag:"single",element:e}),Tm=e=>{const t=ge.fromDom((e=>{if(Ye()&&c(e.target)){const t=ge.fromDom(e.target);if(te(t)&&c(t.dom.shadowRoot)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return P(t)}}return y.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),r=(s=n,l=o,(...e)=>s(l.apply(null,e)));var s,l;return((e,t,o,n,r,s,l)=>({target:e,x:t,y:o,stop:n,prevent:r,kill:s,raw:l}))(t,e.clientX,e.clientY,o,n,r,e)},Rm=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},Dm=v,Om=(e,t,o)=>((e,t,o,n)=>((e,t,o,n,r)=>{const s=((e,t)=>o=>{e(o)&&t(Tm(o))})(o,n);return e.dom.addEventListener(t,s,r),{unbind:h(Rm,e,t,s,r)}})(e,t,o,n,!1))(e,t,Dm,o),km=Tm,Em=e=>!1===ym(ge.fromDom(e.target),"ephox-snooker-resizer-bar"),Nm=(e,t)=>{const o=(r=es.selectedSelector,{get:()=>Yr(ge.fromDom(e.getBody()),r).fold((()=>rs(Or(e),Rr(e)).fold(xm,Sm)),Cm)}),n=((e,t,o)=>{const n=t=>{de(t,e.selected),de(t,e.firstSelected),de(t,e.lastSelected)},r=t=>{ae(t,e.selected,"1")},s=e=>{l(e),o()},l=t=>{const o=nt(t,`${e.selectedSelector},${e.firstSelectedSelector},${e.lastSelectedSelector}`);k(o,n)};return{clearBeforeUpdate:l,clear:s,selectRange:(o,n,l,a)=>{s(o),k(n,r),ae(l,e.firstSelected,"1"),ae(a,e.lastSelected,"1"),t(n,l,a)},selectedSelector:e.selectedSelector,firstSelectedSelector:e.firstSelectedSelector,lastSelectedSelector:e.lastSelectedSelector}})(es,((t,o,n)=>{It(o).each((r=>{const s=Ia(e),l=Cr(d,ge.fromDom(e.getDoc()),s),a=((e,t,o)=>{const n=Vo(e);return Js(n,t).map((e=>{const t=Vs(n,o,!1),{rows:r}=_o(t),s=((e,t)=>{const o=e.slice(0,t[t.length-1].row+1),n=$s(o);return _(n,(e=>{const o=e.cells.slice(0,t[t.length-1].column+1);return O(o,(e=>e.element))}))})(r,e),l=((e,t)=>{const o=e.slice(t[0].row+t[0].rowspan-1,e.length),n=$s(o);return _(n,(e=>{const o=e.cells.slice(t[0].column+t[0].colspan-1,e.cells.length);return O(o,(e=>e.element))}))})(r,e);return{upOrLeftCells:s,downOrRightCells:l}}))})(r,{selection:ss(e)},l);((e,t,o,n,r)=>{e.dispatch("TableSelectionChange",{cells:t,start:o,finish:n,otherCells:r})})(e,t,o,n,a)}))}),(()=>(e=>{e.dispatch("TableSelectionClear")})(e)));var r;return e.on("init",(o=>{const r=e.getWin(),s=Tr(e),l=Rr(e),a=((e,t,o,n)=>{const r=((e,t,o,n)=>{const r=$i(),s=r.clear,l=s=>{r.on((r=>{n.clearBeforeUpdate(t),Ui(s.target,o).each((l=>{$r(r,l,o).each((o=>{const r=o.boxes.getOr([]);if(1===r.length){const o=r[0],l="false"===js(o),a=mt(_s(s.target),o,be);l&&a&&(n.selectRange(t,r,o,o),e.selectContents(o))}else r.length>1&&(n.selectRange(t,r,o.start,o.finish),e.selectContents(l))}))}))}))};return{clearstate:s,mousedown:e=>{n.clear(t),Ui(e.target,o).each(r.set)},mouseover:e=>{l(e)},mouseup:e=>{l(e),s()}}})(hm(e),t,o,n);return{clearstate:r.clearstate,mousedown:r.mousedown,mouseover:r.mouseover,mouseup:r.mouseup}})(r,s,l,n),c=((e,t,o,n)=>{const r=hm(e),s=()=>(n.clear(t),y.none());return{keydown:(e,l,a,c,i,m)=>{const d=e.raw,u=d.which,f=!0===d.shiftKey,g=Ur(t,n.selectedSelector).fold((()=>(Xi(u)&&!f&&n.clearBeforeUpdate(t),Qi(u)&&f?h(qi,r,t,o,Gi,c,l,n.selectRange):Ji(u)&&f?h(qi,r,t,o,Ki,c,l,n.selectRange):Qi(u)?h(Pi,r,o,Gi,c,l,Hi):Ji(u)?h(Pi,r,o,Ki,c,l,Fi):y.none)),(e=>{const o=o=>()=>{const s=H(o,(o=>((e,t,o,n,r)=>Kr(n,e,t,r.firstSelectedSelector,r.lastSelectedSelector).map((e=>(r.clearBeforeUpdate(o),r.selectRange(o,e.boxes,e.start,e.finish),e.boxes))))(o.rows,o.cols,t,e,n)));return s.fold((()=>Gr(t,n.firstSelectedSelector,n.lastSelectedSelector).map((e=>{const o=Qi(u)||m.isForward(u)?Ic.after:Ic.before;return r.setRelativeSelection(Ic.on(e.first,0),o(e.table)),n.clear(t),Pc(y.none(),!0)}))),(e=>y.some(Pc(y.none(),!0))))};return Qi(u)&&f?o([pm(1,0)]):Ji(u)&&f?o([pm(-1,0)]):m.isBackward(u)&&f?o([pm(0,-1),pm(-1,0)]):m.isForward(u)&&f?o([pm(0,1),pm(1,0)]):Xi(u)&&!f?s:y.none}));return g()},keyup:(e,r,s,l,a)=>Ur(t,n.selectedSelector).fold((()=>{const c=e.raw,i=c.which;return!0===c.shiftKey&&Xi(i)?((e,t,o,n,r,s,l)=>be(o,r)&&n===s?y.none():it(o,"td,th",t).bind((o=>it(r,"td,th",t).bind((n=>ti(e,t,o,n,l))))))(t,o,r,s,l,a,n.selectRange):y.none()}),y.none)}})(r,s,l,n),i=((e,t,o,n)=>{const r=hm(e);return(e,s)=>{n.clearBeforeUpdate(t),$r(e,s,o).each((e=>{const o=e.boxes.getOr([]);n.selectRange(t,o,e.start,e.finish),r.selectContents(s),r.collapseSelection()}))}})(r,s,l,n);e.on("TableSelectorChange",(e=>i(e.start,e.finish)));const m=(t,o)=>{(e=>!0===e.raw.shiftKey)(t)&&(o.kill&&t.kill(),o.selection.each((t=>{const o=om.relative(t.start,t.finish),n=Jc(r,o);e.selection.setRng(n)})))},u=e=>0===e.button,f=(()=>{const e=Vi(ge.fromDom(s)),t=Vi(0);return{touchEnd:o=>{const n=ge.fromDom(o.target);if(se("td")(n)||se("th")(n)){const r=e.get(),s=t.get();be(r,n)&&o.timeStamp-s<300&&(o.preventDefault(),i(n,n))}e.set(n),t.set(o.timeStamp)}}})();e.on("dragstart",(e=>{a.clearstate()})),e.on("mousedown",(e=>{u(e)&&Em(e)&&a.mousedown(km(e))})),e.on("mouseover",(e=>{var t;void 0!==(t=e).buttons&&0==(1&t.buttons)||!Em(e)||a.mouseover(km(e))})),e.on("mouseup",(e=>{u(e)&&Em(e)&&a.mouseup(km(e))})),e.on("touchend",f.touchEnd),e.on("keyup",(t=>{const o=km(t);if(o.raw.shiftKey&&Xi(o.raw.which)){const t=e.selection.getRng(),n=ge.fromDom(t.startContainer),r=ge.fromDom(t.endContainer);c.keyup(o,n,t.startOffset,r,t.endOffset).each((e=>{m(o,e)}))}})),e.on("keydown",(o=>{const n=km(o);t.hide();const r=e.selection.getRng(),s=ge.fromDom(r.startContainer),l=ge.fromDom(r.endContainer),a=on(Zi,em)(ge.fromDom(e.selection.getStart()));c.keydown(n,s,r.startOffset,l,r.endOffset,a).each((e=>{m(n,e)})),t.show()})),e.on("NodeChange",(()=>{const t=e.selection,o=ge.fromDom(t.getStart()),r=ge.fromDom(t.getEnd());qr(It,[o,r]).fold((()=>n.clear(s)),d)}))})),e.on("PreInit",(()=>{e.serializer.addTempAttr(es.firstSelected),e.serializer.addTempAttr(es.lastSelected)})),{getSelectedCells:()=>((e,t,o,n)=>{switch(e.tag){case"none":return t();case"single":return(e=>[e.dom])(e.element);case"multiple":return(e=>O(e,(e=>e.dom)))(e.elements)}})(o.get(),u([])),clearSelectedCells:e=>n.clear(ge.fromDom(e))}},Bm=e=>{let t=[];return{bind:e=>{if(void 0===e)throw new Error("Event bind error: undefined handler");t.push(e)},unbind:e=>{t=N(t,(t=>t!==e))},trigger:(...o)=>{const n={};k(e,((e,t)=>{n[e]=o[t]})),k(t,(e=>{e(n)}))}}},zm=e=>({registry:U(e,(e=>({bind:e.bind,unbind:e.unbind}))),trigger:U(e,(e=>e.trigger))}),Am=e=>e.slice(0).sort(),Wm=(e,t)=>{const o=N(t,(t=>!T(e,t)));o.length>0&&(e=>{throw new Error("Unsupported keys for object: "+Am(e).join(", "))})(o)},Lm=e=>((e,t)=>((e,t,o)=>{if(0===t.length)throw new Error("You must specify at least one required field.");return((e,t)=>{if(!s(t))throw new Error("The required fields must be an array. Was: "+t+".");k(t,(t=>{if(!n(t))throw new Error("The value "+t+" in the "+e+" fields was not a string.")}))})("required",t),(e=>{const t=Am(e);A(t,((e,o)=>o<t.length-1&&e===t[o+1])).each((e=>{throw new Error("The field: "+e+" occurs more than once in the combined fields: ["+t.join(", ")+"].")}))})(t),n=>{const r=q(n);M(t,(e=>T(r,e)))||((e,t)=>{throw new Error("All required keys ("+Am(e).join(", ")+") were not specified. Specified keys were: "+Am(t).join(", ")+".")})(t,r),e(t,r);const s=N(t,(e=>!o.validate(n[e],e)));return s.length>0&&((e,t)=>{throw new Error("All values need to be of type: "+t+". Keys ("+Am(e).join(", ")+") were not.")})(s,o.label),n}})(e,t,{validate:i,label:"function"}))(Wm,e),_m=Lm(["compare","extract","mutate","sink"]),Mm=Lm(["element","start","stop","destroy"]),jm=Lm(["forceDrop","drop","move","delayDrop"]),Im=()=>{const e=(()=>{const e=zm({move:Bm(["info"])});return{onEvent:d,reset:d,events:e.registry}})(),t=(()=>{let e=y.none();const t=zm({move:Bm(["info"])});return{onEvent:(o,n)=>{n.extract(o).each((o=>{const r=((t,o)=>{const n=e.map((e=>t.compare(e,o)));return e=y.some(o),n})(n,o);r.each((e=>{t.trigger.move(e)}))}))},reset:()=>{e=y.none()},events:t.registry}})();let o=e;return{on:()=>{o.reset(),o=t},off:()=>{o.reset(),o=e},isOn:()=>o===t,onEvent:(e,t)=>{o.onEvent(e,t)},events:t.events}},Pm=e=>{const t=e.replace(/\./g,"-");return{resolve:e=>t+"-"+e}},Fm=Pm("ephox-dragster").resolve;var Hm=_m({compare:(e,t)=>cn(t.left-e.left,t.top-e.top),extract:e=>y.some(cn(e.x,e.y)),sink:(e,t)=>{const o=(e=>{const t={layerClass:Fm("blocker"),...e},o=ge.fromTag("div");return ae(o,"role","presentation"),Ct(o,{position:"fixed",left:"0px",top:"0px",width:"100%",height:"100%"}),vm(o,Fm("blocker")),vm(o,t.layerClass),{element:u(o),destroy:()=>{Me(o)}}})(t),n=Om(o.element(),"mousedown",e.forceDrop),r=Om(o.element(),"mouseup",e.drop),s=Om(o.element(),"mousemove",e.move),l=Om(o.element(),"mouseout",e.delayDrop);return Mm({element:o.element,start:e=>{ze(e,o.element())},stop:()=>{Me(o.element())},destroy:()=>{o.destroy(),r.unbind(),s.unbind(),l.unbind(),n.unbind()}})},mutate:(e,t)=>{e.mutate(t.left,t.top)}});const qm=Pm("ephox-snooker").resolve,Vm=qm("resizer-bar"),$m=qm("resizer-rows"),Um=qm("resizer-cols"),Gm=e=>{const t=nt(e.parent(),"."+Vm);k(t,Me)},Km=(e,t,o)=>{const n=e.origin();k(t,(t=>{t.each((t=>{const r=o(n,t);vm(r,Vm),ze(e.parent(),r)}))}))},Ym=(e,t,o,n,r)=>{const s=dn(o),l=t.isResizable,a=n.length>0?Sn.positions(n,o):[],c=a.length>0?((e,t)=>_(e.all,((e,o)=>t(e.element)?[o]:[])))(e,l):[];((e,t,o,n)=>{Km(e,t,((e,t)=>{const r=((e,t,o,n,r)=>{const s=ge.fromTag("div");return Ct(s,{position:"absolute",left:t+"px",top:o-3.5+"px",height:"7px",width:n+"px"}),ce(s,{"data-row":e,role:"presentation"}),s})(t.row,o.left-e.left,t.y-e.top,n);return vm(r,$m),r}))})(t,N(a,((e,t)=>R(c,(e=>t===e)))),s,Oo(o));const i=r.length>0?Rn.positions(r,o):[],m=i.length>0?((e,t)=>{const o=[];return D(e.grid.columns,(n=>{Xo(e,n).map((e=>e.element)).forall(t)&&o.push(n)})),N(o,(o=>{const n=Ko(e,(e=>e.column===o));return M(n,(e=>t(e.element)))}))})(e,l):[];((e,t,o,n)=>{Km(e,t,((e,t)=>{const r=((e,t,o,n,r)=>{const s=ge.fromTag("div");return Ct(s,{position:"absolute",left:t-3.5+"px",top:o+"px",height:r+"px",width:"7px"}),ce(s,{"data-column":e,role:"presentation"}),s})(t.col,t.x-e.left,o.top-e.top,0,n);return vm(r,Um),r}))})(t,N(i,((e,t)=>R(m,(e=>t===e)))),s,ln(o))},Jm=(e,t)=>{if(Gm(e),e.isResizable(t)){const o=Vo(t),n=tn(o),r=Zo(o);Ym(o,e,t,n,r)}},Qm=(e,t)=>{const o=nt(e.parent(),"."+Vm);k(o,t)},Xm=e=>{Qm(e,(e=>{xt(e,"display","none")}))},Zm=e=>{Qm(e,(e=>{xt(e,"display","block")}))},ed=qm("resizer-bar-dragging"),td=e=>{const t=(()=>{const e=zm({drag:Bm(["xDelta","yDelta","target"])});let t=y.none();const o=(()=>{const e=zm({drag:Bm(["xDelta","yDelta"])});return{mutate:(t,o)=>{e.trigger.drag(t,o)},events:e.registry}})();return o.events.drag.bind((o=>{t.each((t=>{e.trigger.drag(o.xDelta,o.yDelta,t)}))})),{assign:e=>{t=y.some(e)},get:()=>t,mutate:o.mutate,events:e.registry}})(),o=((e,t={})=>{var o;return((e,t,o)=>{let n=!1;const r=zm({start:Bm([]),stop:Bm([])}),s=Im(),a=()=>{m.stop(),s.isOn()&&(s.off(),r.trigger.stop())},c=((e,t)=>{let o=null;const n=()=>{l(o)||(clearTimeout(o),o=null)};return{cancel:n,throttle:(...t)=>{n(),o=setTimeout((()=>{o=null,e.apply(null,t)}),200)}}})(a);s.events.move.bind((o=>{t.mutate(e,o.info)}));const i=e=>(...t)=>{n&&e.apply(null,t)},m=t.sink(jm({forceDrop:a,drop:i(a),move:i((e=>{c.cancel(),s.onEvent(e,t)})),delayDrop:i(c.throttle)}),o);return{element:m.element,go:e=>{m.start(e),s.on(),r.trigger.start()},on:()=>{n=!0},off:()=>{n=!1},destroy:()=>{m.destroy()},events:r.registry}})(e,null!==(o=t.mode)&&void 0!==o?o:Hm,t)})(t,{});let n=y.none();const r=(e,t)=>y.from(ie(e,t));t.events.drag.bind((e=>{r(e.target,"data-row").each((t=>{const o=Bt(e.target,"top");xt(e.target,"top",o+e.yDelta+"px")})),r(e.target,"data-column").each((t=>{const o=Bt(e.target,"left");xt(e.target,"left",o+e.xDelta+"px")}))}));const s=(e,t)=>Bt(e,t)-Ot(e,"data-initial-"+t,0);o.events.stop.bind((()=>{t.get().each((t=>{n.each((o=>{r(t,"data-row").each((e=>{const n=s(t,"top");de(t,"data-initial-top"),d.trigger.adjustHeight(o,n,parseInt(e,10))})),r(t,"data-column").each((e=>{const n=s(t,"left");de(t,"data-initial-left"),d.trigger.adjustWidth(o,n,parseInt(e,10))})),Jm(e,o)}))}))}));const a=(n,r)=>{d.trigger.startAdjust(),t.assign(n),ae(n,"data-initial-"+r,Bt(n,r)),vm(n,ed),xt(n,"opacity","0.2"),o.go(e.parent())},c=Om(e.parent(),"mousedown",(e=>{var t;t=e.target,ym(t,$m)&&a(e.target,"top"),(e=>ym(e,Um))(e.target)&&a(e.target,"left")})),i=t=>be(t,e.view()),m=Om(e.view(),"mouseover",(t=>{var o;(o=t.target,it(o,"table",i).filter(Ms)).fold((()=>{Xe(t.target)&&Gm(e)}),(t=>{n=y.some(t),Jm(e,t)}))})),d=zm({adjustHeight:Bm(["table","delta","row"]),adjustWidth:Bm(["table","delta","column"]),startAdjust:Bm([])});return{destroy:()=>{c.unbind(),m.unbind(),o.destroy(),Gm(e)},refresh:t=>{Jm(e,t)},on:o.on,off:o.off,hideBars:h(Xm,e),showBars:h(Zm,e),events:d.registry}},od=(e,t,o)=>{const n=Sn,r=Rn,s=td(e),l=zm({beforeResize:Bm(["table","type"]),afterResize:Bm(["table","type"]),startDrag:Bm([])});return s.events.adjustHeight.bind((e=>{const t=e.table;l.trigger.beforeResize(t,"row");((e,t,o,n)=>{const r=Vo(e),s=((e,t,o)=>Qn(e,t,o,Pn,(e=>e.getOrThunk(At))))(r,e,n),l=O(s,((e,n)=>o===n?Math.max(t+e,At()):e)),a=Rl(r,l),c=((e,t)=>O(e.all,((e,o)=>({element:e.element,height:t[o]}))))(r,l);k(c,(e=>{Wn(e.element,e.height)})),k(a,(e=>{Wn(e.element,e.height)}));const i=B(l,((e,t)=>e+t),0);Wn(e,i)})(t,n.delta(e.delta,t),e.row,n),l.trigger.afterResize(t,"row")})),s.events.startAdjust.bind((e=>{l.trigger.startDrag()})),s.events.adjustWidth.bind((e=>{const n=e.table;l.trigger.beforeResize(n,"col");const s=r.delta(e.delta,n),a=o(n);Ol(n,s,e.column,t,a),l.trigger.afterResize(n,"col")})),{on:s.on,off:s.off,refreshBars:s.refresh,hideBars:s.hideBars,showBars:s.showBars,destroy:s.destroy,events:l.registry}},nd=e=>c(e)&&"TABLE"===e.tagName,rd="bar-",sd=e=>"false"!==ie(e,"data-mce-resize"),ld=e=>{const t=$i(),o=$i(),n=$i();let r,s;const l=t=>Xa(e,t),a=()=>Ha(e)?ys():vs();return e.on("init",(()=>{const r=((e,t)=>e.inline?((e,t,o)=>({parent:u(t),view:u(e),origin:u(cn(0,0)),isResizable:o}))(ge.fromDom(e.getBody()),(()=>{const e=ge.fromTag("div");return Ct(e,{position:"static",height:"0",width:"0",padding:"0",margin:"0",border:"0"}),ze(Ze(ge.fromDom(document)),e),e})(),t):((e,t)=>{const o=ne(e)?(e=>ge.fromDom(Ce(e).dom.documentElement))(e):e;return{parent:u(o),view:u(e),origin:u(cn(0,0)),isResizable:t}})(ge.fromDom(e.getDoc()),t))(e,sd);if(n.set(r),(e=>{const t=e.options.get("object_resizing");return T(t.split(","),"table")})(e)&&Ka(e)){const n=a(),s=od(r,n,l);s.on(),s.events.startDrag.bind((o=>{t.set(e.selection.getRng())})),s.events.beforeResize.bind((t=>{const o=t.table.dom;((e,t,o,n,r)=>{e.dispatch("ObjectResizeStart",{target:t,width:o,height:n,origin:r})})(e,o,kr(o),Er(o),rd+t.type)})),s.events.afterResize.bind((o=>{const n=o.table,r=n.dom;Dr(n),t.on((t=>{e.selection.setRng(t),e.focus()})),((e,t,o,n,r)=>{e.dispatch("ObjectResized",{target:t,width:o,height:n,origin:r})})(e,r,kr(r),Er(r),rd+o.type),e.undoManager.add()})),o.set(s)}})),e.on("ObjectResizeStart",(t=>{const o=t.target;if(nd(o)){const n=ge.fromDom(o);k(e.dom.select(".mce-clonedresizable"),(t=>{e.dom.addClass(t,"mce-"+Fa(e)+"-columns")})),!fc(n)&&Ua(e)?wc(n):!uc(n)&&$a(e)&&pc(n),gc(n)&&ht(t.origin,rd)&&pc(n),r=t.width,s=Ga(e)?"":((e,t)=>{const o=e.dom.getStyle(t,"width")||e.dom.getAttrib(t,"width");return y.from(o).filter(bt)})(e,o).getOr("")}})),e.on("ObjectResized",(t=>{const o=t.target;if(nd(o)){const n=ge.fromDom(o),c=t.origin;ht(c,"corner-")&&((t,o,n)=>{const c=pt(o,"e");if(""===s&&pc(t),n!==r&&""!==s){xt(t,"width",s);const o=a(),i=l(t),m=Ha(e)||c?(e=>xs(e).columns)(t)-1:0;Ol(t,n-r,m,o,i)}else if((e=>/^(\d+(\.\d+)?)%$/.test(e))(s)){const e=parseFloat(s.replace("%",""));xt(t,"width",n*e/r+"%")}(e=>/^(\d+(\.\d+)?)px$/.test(e))(s)&&(e=>{const t=Vo(e);Qo(t)||k(Mt(e),(e=>{const t=St(e,"width");xt(e,"width",t),de(e,"width")}))})(t)})(n,c,t.width),Dr(n),za(e,n.dom,Aa)}})),e.on("SwitchMode",(()=>{o.on((t=>{e.mode.isReadOnly()?t.hideBars():t.showBars()}))})),e.on("remove",(()=>{o.on((e=>{e.destroy()})),n.on((t=>{((e,t)=>{e.inline&&Me(t.parent())})(e,t)}))})),{refresh:e=>{o.on((t=>t.refreshBars(ge.fromDom(e))))},hide:()=>{o.on((e=>e.hideBars()))},show:()=>{o.on((e=>e.showBars()))}}},ad=e=>{(e=>{const t=e.options.register;t("table_clone_elements",{processor:"string[]"}),t("table_use_colgroups",{processor:"boolean",default:!0}),t("table_header_type",{processor:e=>{const t=T(["section","cells","sectionCells","auto"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be one of: section, cells, sectionCells or auto."}},default:"section"}),t("table_sizing_mode",{processor:"string",default:"auto"}),t("table_default_attributes",{processor:"object",default:{border:"1"}}),t("table_default_styles",{processor:"object",default:{"border-collapse":"collapse"}}),t("table_column_resizing",{processor:e=>{const t=T(["preservetable","resizetable"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be preservetable, or resizetable."}},default:"preservetable"}),t("table_resize_bars",{processor:"boolean",default:!0}),t("table_style_by_css",{processor:"boolean",default:!0})})(e);const t=ld(e),o=Nm(e,t),n=Za(e,t,o);return Mc(e,n),((e,t)=>{const o=Rr(e),n=t=>rs(Or(e)).bind((n=>It(n,o).map((o=>{const r=ts(ss(e),o,n);return t(o,r)})))).getOr("");$({mceTableRowType:()=>n(t.getTableRowType),mceTableCellType:()=>n(t.getTableCellType),mceTableColType:()=>n(t.getTableColType)},((t,o)=>e.addQueryValueHandler(o,t)))})(e,n),ls(e,n),{getSelectedCells:o.getSelectedCells,clearSelectedCells:o.clearSelectedCells}};e.add("dom",(e=>({table:ad(e)})))}();
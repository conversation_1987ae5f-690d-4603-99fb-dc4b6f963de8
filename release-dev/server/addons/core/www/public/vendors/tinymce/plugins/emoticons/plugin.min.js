/**
 * TinyMCE version 6.1.2 (2022-07-29)
 */
!function(){"use strict";var t=tinymce.util.Tools.resolve("tinymce.PluginManager");const e=(null,t=>null===t);const o=()=>{},n=()=>!1;class s{constructor(t,e){this.tag=t,this.value=e}static some(t){return new s(!0,t)}static none(){return s.singletonNone}fold(t,e){return this.tag?e(this.value):t()}isSome(){return this.tag}isNone(){return!this.tag}map(t){return this.tag?s.some(t(this.value)):s.none()}bind(t){return this.tag?t(this.value):s.none()}exists(t){return this.tag&&t(this.value)}forall(t){return!this.tag||t(this.value)}filter(t){return!this.tag||t(this.value)?this:s.none()}getOr(t){return this.tag?this.value:t}or(t){return this.tag?this:t}getOrThunk(t){return this.tag?this.value:t()}orThunk(t){return this.tag?this:t()}getOrDie(t){if(this.tag)return this.value;throw new Error(null!=t?t:"Called getOrDie on None")}static from(t){return null==t?s.none():s.some(t)}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(t){this.tag&&t(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}s.singletonNone=new s(!1);const r=(t,e)=>{const o=t.length,n=new Array(o);for(let s=0;s<o;s++){const o=t[s];n[s]=e(o,s)}return n},a=t=>{let e=t;return{get:()=>e,set:t=>{e=t}}},i=Object.keys,l=Object.hasOwnProperty,c=(t,e)=>{const o=i(t);for(let n=0,s=o.length;n<s;n++){const s=o[n];e(t[s],s)}},u=(t,e)=>l.call(t,e),g=(m=(t,e)=>e,(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const e={};for(let o=0;o<t.length;o++){const n=t[o];for(const t in n)u(n,t)&&(e[t]=m(e[t],n[t]))}return e});var m;const h=()=>{const t=(t=>{const e=a(s.none()),o=()=>e.get().each(t);return{clear:()=>{o(),e.set(s.none())},isSet:()=>e.get().isSome(),get:()=>e.get(),set:t=>{o(),e.set(s.some(t))}}})(o);return{...t,on:e=>t.get().each(e)}},d=(t,e)=>-1!==t.indexOf(e);var p=tinymce.util.Tools.resolve("tinymce.Resource");const y=t=>e=>e.options.get(t),v=y("emoticons_database"),f=y("emoticons_database_url"),b=y("emoticons_database_id"),w=y("emoticons_append"),_=y("emoticons_images_url"),j="All",C={symbols:"Symbols",people:"People",animals_and_nature:"Animals and Nature",food_and_drink:"Food and Drink",activity:"Activity",travel_and_places:"Travel and Places",objects:"Objects",flags:"Flags",user:"User Defined"},k=(t,e)=>u(t,e)?t[e]:e,A=t=>{const e=w(t);return o=t=>({keywords:[],category:"user",...t}),((t,e)=>{const o={};return c(t,((t,n)=>{const s=e(t,n);o[s.k]=s.v})),o})(e,((t,e)=>({k:e,v:o(t)})));var o},O=(t,e)=>d(t.title.toLowerCase(),e)||((t,o)=>{for(let o=0,s=t.length;o<s;o++)if(n=t[o],d(n.toLowerCase(),e))return!0;var n;return!1})(t.keywords),x=(t,e,o)=>{const s=[],r=e.toLowerCase(),a=o.fold((()=>n),(t=>e=>e>=t));for(let o=0;o<t.length&&(0!==e.length&&!O(t[o],r)||(s.push({value:t[o].char,text:t[o].title,icon:t[o].char}),!a(s.length)));o++);return s},L="pattern",T=(t,o)=>{const n={pattern:"",results:x(o.listAll(),"",s.some(300))},i=a(j),l=((t,o)=>{let n=null;const s=()=>{e(n)||(clearTimeout(n),n=null)};return{cancel:s,throttle:(...e)=>{s(),n=setTimeout((()=>{n=null,t.apply(null,e)}),200)}}})((t=>{(t=>{const e=t.getData(),n=i.get(),r=o.listCategory(n),a=x(r,e.pattern,n===j?s.some(300):s.none());t.setData({results:a})})(t)})),c={label:"Search",type:"input",name:L},u={type:"collection",name:"results"},g=()=>({title:"Emojis",size:"normal",body:{type:"tabpanel",tabs:r(o.listCategories(),(t=>({title:t,name:t,items:[c,u]})))},initialData:n,onTabChange:(t,e)=>{i.set(e.newTabName),l.throttle(t)},onChange:l.throttle,onAction:(e,o)=>{"results"===o.name&&(((t,e)=>{t.insertContent(e)})(t,o.value),e.close())},buttons:[{type:"cancel",text:"Close",primary:!0}]}),m=t.windowManager.open(g());m.focus(L),o.hasLoaded()||(m.block("Loading emojis..."),o.waitForLoad().then((()=>{m.redial(g()),l.throttle(m),m.focus(L),m.unblock()})).catch((t=>{m.redial({title:"Emojis",body:{type:"panel",items:[{type:"alertbanner",level:"error",icon:"warning",text:"Could not load emojis"}]},buttons:[{type:"cancel",text:"Close",primary:!0}],initialData:{pattern:"",results:[]}}),m.focus(L),m.unblock()})))};t.add("emoticons",((t,e)=>{((t,e)=>{const o=t.options.register;o("emoticons_database",{processor:"string",default:"emojis"}),o("emoticons_database_url",{processor:"string",default:`${e}/js/${v(t)}${t.suffix}.js`}),o("emoticons_database_id",{processor:"string",default:"tinymce.plugins.emoticons"}),o("emoticons_append",{processor:"object",default:{}}),o("emoticons_images_url",{processor:"string",default:"https://twemoji.maxcdn.com/v/13.0.1/72x72/"})})(t,e);const o=((t,e,o)=>{const n=h(),r=h(),a=_(t),l=t=>{return o="<img",(e=t.char).length>=o.length&&e.substr(0,0+o.length)===o?t.char.replace(/src="([^"]+)"/,((t,e)=>`src="${a}${e}"`)):t.char;var e,o};t.on("init",(()=>{p.load(o,e).then((e=>{const o=A(t);(t=>{const e={},o=[];c(t,((t,n)=>{const s={title:n,keywords:t.keywords,char:l(t),category:k(C,t.category)},r=void 0!==e[s.category]?e[s.category]:[];e[s.category]=r.concat([s]),o.push(s)})),n.set(e),r.set(o)})(g(e,o))}),(t=>{console.log(`Failed to load emojis: ${t}`),n.set({}),r.set([])}))}));const u=()=>r.get().getOr([]),m=()=>n.isSet()&&r.isSet();return{listCategories:()=>[j].concat(i(n.get().getOr({}))),hasLoaded:m,waitForLoad:()=>m()?Promise.resolve(!0):new Promise(((t,o)=>{let n=15;const s=setInterval((()=>{m()?(clearInterval(s),t(!0)):(n--,n<0&&(console.log("Could not load emojis from url: "+e),clearInterval(s),o(!1)))}),100)})),listAll:u,listCategory:t=>t===j?u():n.get().bind((e=>s.from(e[t]))).getOr([])}})(t,f(t),b(t));((t,e)=>{t.addCommand("mceEmoticons",(()=>T(t,e)))})(t,o),(t=>{const e=()=>t.execCommand("mceEmoticons");t.ui.registry.addButton("emoticons",{tooltip:"Emojis",icon:"emoji",onAction:e}),t.ui.registry.addMenuItem("emoticons",{text:"Emojis...",icon:"emoji",onAction:e})})(t),((t,e)=>{t.ui.registry.addAutocompleter("emoticons",{ch:":",columns:"auto",minChars:2,fetch:(t,o)=>e.waitForLoad().then((()=>{const n=e.listAll();return x(n,t,s.some(o))})),onAction:(e,o,n)=>{t.selection.setRng(o),t.insertContent(n),e.hide()}})})(t,o),(t=>{t.on("PreInit",(()=>{t.parser.addAttributeFilter("data-emoticon",(t=>{((t,e)=>{for(let e=0,n=t.length;e<n;e++)(o=t[e]).attr("data-mce-resize","false"),o.attr("data-mce-placeholder","1");var o})(t)}))}))})(t)}))}();
import React from 'react';
import { module } from '@datatp-ui/erp';
import { entity } from '@datatp-ui/lib';
export declare class DocumentSetEditor extends entity.AppDbEntityEditor {
    render(): React.JSX.Element;
}
interface DocumentSetProps extends entity.AppDbEntityEditorProps {
    storage: module.storage.IStorage;
}
export declare class DocumentSet extends entity.AppDbEntityEditor<DocumentSetProps> {
    onModifyBean: (bean: any, action?: entity.ModifyBeanActions) => void;
    render(): React.JSX.Element;
}
export {};
//# sourceMappingURL=DocumentSet.d.ts.map
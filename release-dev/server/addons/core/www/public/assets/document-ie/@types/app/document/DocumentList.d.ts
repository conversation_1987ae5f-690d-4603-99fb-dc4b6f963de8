import React, { ReactElement } from 'react';
import { module } from '@datatp-ui/erp';
import { component, entity, grid } from '@datatp-ui/lib';
export declare class DocumentListPlugin extends entity.DbEntityListPlugin {
    documentSetId: number;
    constructor();
    withScope(scope: any): this;
    withDocumentSet(documentSetId: number): this;
    withDocumentRoot(documentRootId: number): this;
    withRequestType(requestType: 'None' | 'IE'): this;
    loadData(uiList: entity.DbEntityList<any>): void;
    backendDelete(uiList: entity.DbEntityList<entity.DbEntityListProps>, targetIds: Array<number>): void;
}
interface DocumentListProps extends entity.DbEntityListProps {
    storage: module.storage.IStorage;
    documentSet?: any;
    viewName?: 'table' | 'aggregation';
    viewType: 'DocumentSet' | 'RequestIE' | 'Cache';
    createDocument?: (document: any) => any;
}
export declare class DocumentList extends entity.DbEntityList<DocumentListProps> {
    showCheckProcessStatusToolTip(record: any): ReactElement;
    showCheckMissingStatusToolTip(record: any): ReactElement;
    createVGridConfig(): grid.VGridConfig;
    organizeByHBL: () => void;
    onSave: () => void;
    onRename: () => void;
    onUpdateDocumentTypes(ctx: grid.VGridContext, docType: string): void;
    onProcessIE: () => void;
    onDownload: () => void;
    onShowUpload: () => void;
    onUpload: (uploadResources: Array<component.UploadResource>) => void;
    onRenderLoading(): React.JSX.Element;
    renderDialogConfirmMessage(): ReactElement;
    onCreateAccountingSummary: () => void;
    onBatchRequestIE: () => void;
    onRequestIE: () => void;
    onCancelIE: () => void;
    onMergePdfDocuments: () => void;
    _ensureSelectedRecords(): boolean;
    onDefaultSelect(dRecord: grid.DisplayRecord): void;
}
export {};
//# sourceMappingURL=DocumentList.d.ts.map
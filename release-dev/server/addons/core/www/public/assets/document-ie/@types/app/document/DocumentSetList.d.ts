import { module } from '@datatp-ui/erp';
import { grid, app, entity } from '@datatp-ui/lib';
export declare class DocumentSetListPlugin extends entity.DbEntityListPlugin {
    categoryId: number;
    constructor();
    loadData(uiList: entity.DbEntityList<any>): void;
    withScope(scope: any): this;
    withCategory(categoryId: number): this;
    backendDelete(uiList: entity.DbEntityList<entity.DbEntityListProps>, targetIds: Array<number>): void;
}
interface DocumentSetListProps extends entity.DbEntityListProps {
    storage: module.storage.IStorage;
}
export declare class DocumentSetList extends entity.DbEntityList<DocumentSetListProps> {
    createVGridConfig(): grid.VGridConfig;
    onAddDocumentSet: (entity: any, uiEditor?: app.AppComponent) => void;
    onDefaultSelect(dRecord: grid.DisplayRecord): void;
    onNewAction: () => void;
    filterByCategory(category: any): void;
}
export {};
//# sourceMappingURL=DocumentSetList.d.ts.map
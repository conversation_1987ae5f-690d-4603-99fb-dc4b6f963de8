import React from 'react';
import { entity, app } from '@datatp-ui/lib';
import { UIDocumentPlugin } from "../DocumentPlugin";
interface DocTmsChargeSummaryEditorProps extends entity.AppComplexEntityEditorProps {
    doc: any;
    documentSet: any;
}
export declare class DocTmsChargeSummaryEditor extends entity.AppDbComplexEntityEditor<DocTmsChargeSummaryEditorProps> {
    enrichDocTmsSummaryData: () => void;
    onSave: () => void;
    toBFSOneTemplate: () => void;
    verifyInvoice: () => void;
    onDistributeToDocInvoice: () => void;
    render(): React.JSX.Element;
}
interface DocTmsChargeSummaryLoadableProps extends app.AppComponentProps {
    data: any;
    onPostCommit?(entity: any): void;
}
export declare class DocTmsChargeSummaryLoadable extends app.AppComponent<DocTmsChargeSummaryLoadableProps> {
    entity: any;
    componentDidMount(): void;
    onPostCommit: (entity: any) => void;
    renderEntity(_entity: any): React.ReactNode;
    render(): React.ReactNode;
}
export declare class FCLDocTmsChargeSummary extends UIDocumentPlugin {
    constructor();
    hasPreviewTab(): boolean;
    render(_appContext: app.AppContext, _pageContext: app.PageContext, data: any, onCommit: (bean: any) => void): React.JSX.Element;
}
export declare class LCLDocTmsChargeSummary extends UIDocumentPlugin {
    constructor();
    hasPreviewTab(): boolean;
    render(_appContext: app.AppContext, _pageContext: app.PageContext, data: any, onCommit: (bean: any) => void): React.JSX.Element;
}
export {};
//# sourceMappingURL=DocTmsChargeSummary.d.ts.map
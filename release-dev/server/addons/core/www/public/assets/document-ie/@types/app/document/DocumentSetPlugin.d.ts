import React from "react";
import { app } from '@datatp-ui/lib';
import { DocumentList } from "./DocumentList";
export declare abstract class UIDocumentSetPlugin {
    name: string;
    protected constructor(name: string);
    renderActionButton(_appContext: app.AppContext, _pageContext: app.PageContext, uiList: DocumentList, viewType: string): React.JSX.Element;
}
export declare class DocumentSetPluginManager {
    plugins: Record<string, UIDocumentSetPlugin>;
    getPluginMap(): Record<string, UIDocumentSetPlugin>;
    getPluginByName(name: string): UIDocumentSetPlugin | null;
    register(plugin: UIDocumentSetPlugin): void;
}
export declare const DOCUMENT_SET_PLUGIN_REGISTRY: DocumentSetPluginManager;
//# sourceMappingURL=DocumentSetPlugin.d.ts.map
"use strict";
(self["webpackChunkdatatp_ui_document_ie"] = self["webpackChunkdatatp_ui_document_ie"] || []).push([["src_Init_tsx"],{

/***/ "./src/Init.tsx":
/*!**********************************!*\
  !*** ./src/Init.tsx + 3 modules ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ Init)
});

// EXTERNAL MODULE: consume shared module (default) react@^18.3.1 (singleton) (fallback: ./node_modules/.pnpm/react@18.3.1/node_modules/react/index.js)
var index_js_ = __webpack_require__("webpack/sharing/consume/default/react/react?55d4");
var index_js_default = /*#__PURE__*/__webpack_require__.n(index_js_);
// EXTERNAL MODULE: consume shared module (default) react@>=16.8.6 (singleton) (fallback: ./node_modules/.pnpm/react@18.3.1/node_modules/react/index.js)
var react_index_js_ = __webpack_require__("webpack/sharing/consume/default/react/react?ac0e");
var react_index_js_default = /*#__PURE__*/__webpack_require__.n(react_index_js_);
// EXTERNAL MODULE: ./node_modules/.pnpm/prop-types@15.8.1/node_modules/prop-types/index.js
var prop_types = __webpack_require__("./node_modules/.pnpm/prop-types@15.8.1/node_modules/prop-types/index.js");
var prop_types_default = /*#__PURE__*/__webpack_require__.n(prop_types);
;// CONCATENATED MODULE: ./node_modules/.pnpm/react-feather@2.0.10_react@18.3.1/node_modules/react-feather/dist/icons/folder.js
function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }



var Folder = (0,react_index_js_.forwardRef)(function (_ref, ref) {
  var _ref$color = _ref.color,
      color = _ref$color === void 0 ? 'currentColor' : _ref$color,
      _ref$size = _ref.size,
      size = _ref$size === void 0 ? 24 : _ref$size,
      rest = _objectWithoutProperties(_ref, ["color", "size"]);

  return /*#__PURE__*/react_index_js_default().createElement("svg", _extends({
    ref: ref,
    xmlns: "http://www.w3.org/2000/svg",
    width: size,
    height: size,
    viewBox: "0 0 24 24",
    fill: "none",
    stroke: color,
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round"
  }, rest), /*#__PURE__*/react_index_js_default().createElement("path", {
    d: "M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"
  }));
});
Folder.propTypes = {
  color: (prop_types_default()).string,
  size: prop_types_default().oneOfType([(prop_types_default()).string, (prop_types_default()).number])
};
Folder.displayName = 'Folder';
/* harmony default export */ const folder = (Folder);
;// CONCATENATED MODULE: ./node_modules/.pnpm/react-feather@2.0.10_react@18.3.1/node_modules/react-feather/dist/icons/file-text.js
function file_text_extends() { file_text_extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return file_text_extends.apply(this, arguments); }

function file_text_objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = file_text_objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function file_text_objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }



var FileText = (0,react_index_js_.forwardRef)(function (_ref, ref) {
  var _ref$color = _ref.color,
      color = _ref$color === void 0 ? 'currentColor' : _ref$color,
      _ref$size = _ref.size,
      size = _ref$size === void 0 ? 24 : _ref$size,
      rest = file_text_objectWithoutProperties(_ref, ["color", "size"]);

  return /*#__PURE__*/react_index_js_default().createElement("svg", file_text_extends({
    ref: ref,
    xmlns: "http://www.w3.org/2000/svg",
    width: size,
    height: size,
    viewBox: "0 0 24 24",
    fill: "none",
    stroke: color,
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round"
  }, rest), /*#__PURE__*/react_index_js_default().createElement("path", {
    d: "M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
  }), /*#__PURE__*/react_index_js_default().createElement("polyline", {
    points: "14 2 14 8 20 8"
  }), /*#__PURE__*/react_index_js_default().createElement("line", {
    x1: "16",
    y1: "13",
    x2: "8",
    y2: "13"
  }), /*#__PURE__*/react_index_js_default().createElement("line", {
    x1: "16",
    y1: "17",
    x2: "8",
    y2: "17"
  }), /*#__PURE__*/react_index_js_default().createElement("polyline", {
    points: "10 9 9 9 8 9"
  }));
});
FileText.propTypes = {
  color: (prop_types_default()).string,
  size: prop_types_default().oneOfType([(prop_types_default()).string, (prop_types_default()).number])
};
FileText.displayName = 'FileText';
/* harmony default export */ const file_text = (FileText);
// EXTERNAL MODULE: consume shared module (default) @datatp-ui/lib@* (singleton) (fallback: ./node_modules/.pnpm/@datatp-ui+lib@file+..+..+..+datatp-erp+webui+lib_@lexical+code@0.24.0_@lexical+html@0.24.0_@_ywz5fpufom7uk2g2nnqcl4ih44/node_modules/@datatp-ui/lib/dist/main.js)
var main_js_ = __webpack_require__("webpack/sharing/consume/default/@datatp-ui/lib/@datatp-ui/lib");
;// CONCATENATED MODULE: ./src/backend.tsx

const T = main_js_.i18n.getT(['document']);

;// CONCATENATED MODULE: ./src/Init.tsx




var space = main_js_.app.space;
class NewDocumentSpacePlugin extends space.SpacePlugin {
    constructor() {
        super('new-document', 'Document Navigation');
    }
    createUserScreens() {
        let company = main_js_.app.host.DATATP_HOST.session.getAccountAcl().getCompanyAcl();
        let companyCode = company.companyCode;
        let configs = [
            {
                id: "new-document-ie", label: T("New Document IE"), icon: folder,
                checkPermission: {
                    feature: { module: 'user', name: 'my-space' },
                    requiredCapability: main_js_.app.READ,
                },
                renderUI: (appCtx, pageCtx) => {
                    let config = {
                        tabs: [
                            {
                                name: 'document-set', label: 'Document Set', active: true, Icon: folder,
                                renderContent: (_ctx) => {
                                    return (index_js_default().createElement("div", null, "TODO: DocumentSetList"));
                                }
                            },
                            {
                                name: 'documents', label: 'Documents', Icon: file_text,
                                renderContent: (_ctx) => {
                                    return (index_js_default().createElement("div", null, "TODO: DocumentList"));
                                }
                            },
                        ]
                    };
                    let html = (index_js_default().createElement(main_js_.bs.DefaultTabPane, { config: config }));
                    return html;
                },
                screens: []
            }
        ];
        return configs;
    }
    createCompanyScreens() {
        let company = main_js_.app.host.DATATP_HOST.session.getAccountAcl().getCompanyAcl();
        let companyCode = company.companyCode;
        let configs = [
            {
                id: "new-document-ie", label: T("New Document IE"), icon: folder,
                checkPermission: {
                    feature: { module: 'document', name: 'company-document' },
                    requiredCapability: main_js_.app.READ,
                },
                renderUI: (appCtx, pageCtx) => {
                    let config = {
                        tabs: [
                            {
                                name: 'document-set', label: 'Document Set', active: true, Icon: folder,
                                renderContent: (_ctx) => {
                                    return (index_js_default().createElement("div", null, "TODO: DocumentSetList"));
                                }
                            },
                            {
                                name: 'documents', label: 'Documents', Icon: file_text,
                                renderContent: (_ctx) => {
                                    return (index_js_default().createElement("div", null, "TODO: DocumentList"));
                                }
                            },
                            {
                                name: "document-set-tag", label: T("Document Set Tag"),
                                renderContent: (_ctx) => {
                                    return (index_js_default().createElement("div", null, "TODO: DocumentSetTagList"));
                                }
                            },
                        ]
                    };
                    let html = (index_js_default().createElement(main_js_.bs.DefaultTabPane, { config: config }));
                    return html;
                },
                screens: []
            }
        ];
        return configs;
    }
}
function Init() {
    console.log('DocumentIE: Init().........');
    space.SpacePluginManager.register(new NewDocumentSpacePlugin());
}
let react = (index_js_default());
if (!react['id']) {
    react['id'] = '@datatp-ui/lib';
    console.log('Load React in module @datatp-ui/lib');
}


/***/ })

}]);
//# sourceMappingURL=chunk-src_Init_tsx.js.map
import React from 'react';
import { app } from '@datatp-ui/lib';
import { UIDocumentSetPlugin } from "../DocumentSetPlugin";
import { DocumentList } from "../DocumentList";
export declare class TMSDocChargeSummaryPlugin extends UIDocumentSetPlugin {
    constructor();
    renderActionButton(_appContext: app.AppContext, _pageContext: app.PageContext, uiList: DocumentList, viewType: string): React.JSX.Element;
}
//# sourceMappingURL=TMSDocumentSetAction.d.ts.map
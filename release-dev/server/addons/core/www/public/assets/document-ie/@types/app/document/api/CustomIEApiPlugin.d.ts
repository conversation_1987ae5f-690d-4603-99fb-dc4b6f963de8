import React from 'react';
import { app } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';
export declare class UICustomIEApiPlugin extends module.security.UIApiPlugin {
    constructor();
    render(appContext: app.AppContext, pageContext: app.PageContext, resp: module.security.ApiResponse): React.JSX.Element;
}
export declare class UICreateCustomIEApiToken extends app.AppComponent {
    tokenTemplate: {
        tokenId: number;
        tokenLabel: string;
        resourceHandler: string;
        authorization: string;
    };
    onSubmit: () => void;
    test: () => void;
    render(): React.JSX.Element;
}
//# sourceMappingURL=CustomIEApiPlugin.d.ts.map
import { grid } from '@datatp-ui/lib';
import { BFSOneHwbListPlugin } from './BFSOneHwbList';
declare abstract class DocInvChargeSummaryPlugin {
    name: string;
    constructor(name: string);
    getName(): string;
    buildGridColumnConfig: (ctx: grid.VGridContext) => grid.FieldConfig[];
    getBFSOneHwbListPlugin: (item: any) => BFSOneHwbListPlugin;
    mappingToBFSOneFields: () => any;
}
export declare class DocInvChargeSummaryPluginManager {
    static plugins: Record<string, DocInvChargeSummaryPlugin>;
    static getPlugin(name: string): DocInvChargeSummaryPlugin;
    static register(plugin: DocInvChargeSummaryPlugin): void;
}
export {};
//# sourceMappingURL=DocInvChargeSummaryManager.d.ts.map
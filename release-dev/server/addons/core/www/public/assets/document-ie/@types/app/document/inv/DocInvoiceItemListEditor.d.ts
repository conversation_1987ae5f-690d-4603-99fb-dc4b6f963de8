import React from 'react';
import { entity, grid } from '@datatp-ui/lib';
export declare class DocInvoiceItemForm extends entity.AppDbEntity {
    render(): React.JSX.Element;
}
export declare class DocInvoiceItemListEditor extends entity.VGridEntityListEditor {
    renderBeanEditor(): React.JSX.Element;
    renderAttributesEditor(): void;
    createVGridConfig(): grid.VGridConfig;
}
export declare class DocInvoiceHblDistributionListEditor extends entity.VGridEntityListEditor {
    renderBeanEditor(): React.JSX.Element;
    createVGridConfig(): grid.VGridConfig;
}
export declare class DocInvoiceHblDistributionForm extends entity.AppDbEntity {
    render(): React.JSX.Element;
}
//# sourceMappingURL=DocInvoiceItemListEditor.d.ts.map
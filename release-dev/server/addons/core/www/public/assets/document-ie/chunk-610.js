(self.webpackChunkdatatp_ui_document_ie=self.webpackChunkdatatp_ui_document_ie||[]).push([[610,736],{6186:(e,t,n)=>{"use strict";var r=n(2985);function o(){}function a(){}a.resetWarningCache=o,e.exports=function(){function e(e,t,n,o,a,c){if(c!==r){var i=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:o};return n.PropTypes=n,n}},2736:(e,t,n)=>{e.exports=n(6186)()},2985:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},5610:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>v});var r=n(5208),o=n.n(r),a=n(789),c=n.n(a),i=n(2736),l=n.n(i);function s(){return s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},s.apply(this,arguments)}var u=(0,a.forwardRef)((function(e,t){var n=e.color,r=void 0===n?"currentColor":n,o=e.size,a=void 0===o?24:o,i=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,["color","size"]);return c().createElement("svg",s({ref:t,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),c().createElement("path",{d:"M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"}))}));u.propTypes={color:l().string,size:l().oneOfType([l().string,l().number])},u.displayName="Folder";const p=u;function m(){return m=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},m.apply(this,arguments)}var d=(0,a.forwardRef)((function(e,t){var n=e.color,r=void 0===n?"currentColor":n,o=e.size,a=void 0===o?24:o,i=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,["color","size"]);return c().createElement("svg",m({ref:t,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),c().createElement("path",{d:"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"}),c().createElement("polyline",{points:"14 2 14 8 20 8"}),c().createElement("line",{x1:"16",y1:"13",x2:"8",y2:"13"}),c().createElement("line",{x1:"16",y1:"17",x2:"8",y2:"17"}),c().createElement("polyline",{points:"10 9 9 9 8 9"}))}));d.propTypes={color:l().string,size:l().oneOfType([l().string,l().number])},d.displayName="FileText";const f=d;var y=n(8754);const g=y.i18n.getT(["document"]);var b=y.app.space;class O extends b.SpacePlugin{constructor(){super("new-document","Document Navigation")}createUserScreens(){return y.app.host.DATATP_HOST.session.getAccountAcl().getCompanyAcl().companyCode,[{id:"new-document-ie",label:g("New Document IE"),icon:p,checkPermission:{feature:{module:"user",name:"my-space"},requiredCapability:y.app.READ},renderUI:(e,t)=>{let n={tabs:[{name:"document-set",label:"Document Set",active:!0,Icon:p,renderContent:e=>o().createElement("div",null,"TODO: DocumentSetList")},{name:"documents",label:"Documents",Icon:f,renderContent:e=>o().createElement("div",null,"TODO: DocumentList")}]};return o().createElement(y.bs.DefaultTabPane,{config:n})},screens:[]}]}createCompanyScreens(){return y.app.host.DATATP_HOST.session.getAccountAcl().getCompanyAcl().companyCode,[{id:"new-document-ie",label:g("New Document IE"),icon:p,checkPermission:{feature:{module:"document",name:"company-document"},requiredCapability:y.app.READ},renderUI:(e,t)=>{let n={tabs:[{name:"document-set",label:"Document Set",active:!0,Icon:p,renderContent:e=>o().createElement("div",null,"TODO: DocumentSetList")},{name:"documents",label:"Documents",Icon:f,renderContent:e=>o().createElement("div",null,"TODO: DocumentList")},{name:"document-set-tag",label:g("Document Set Tag"),renderContent:e=>o().createElement("div",null,"TODO: DocumentSetTagList")}]};return o().createElement(y.bs.DefaultTabPane,{config:n})},screens:[]}]}}function v(){console.log("DocumentIE: Init()........."),b.SpacePluginManager.register(new O)}}}]);
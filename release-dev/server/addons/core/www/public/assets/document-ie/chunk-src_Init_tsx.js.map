{"version": 3, "file": "chunk-src_Init_tsx.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sBAAsB,gDAAgD,gBAAgB,sBAAsB,OAAO,2BAA2B,0BAA0B,yDAAyD,iCAAiC,kBAAkB;;AAEpR,sDAAsD,+BAA+B,8DAA8D,YAAY,oCAAoC,6DAA6D,YAAY,6BAA6B,OAAO,2BAA2B,0CAA0C,wEAAwE,+BAA+B;;AAE5d,2DAA2D,+BAA+B,iBAAiB,sCAAsC,YAAY,YAAY,uBAAuB,OAAO,qBAAqB,0CAA0C,6BAA6B;;AAEzP;AACP;AACnC,aAAa,8BAAU;AACvB;AACA;AACA;AACA;AACA;;AAEA,sBAAsB,sCAAmB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,sBAAsB,sCAAmB;AAC5C;AACA,GAAG;AACH,CAAC;AACD;AACA,SAAS,6BAAgB;AACzB,QAAQ,8BAAmB,EAAE,6BAAgB,EAAE,6BAAgB;AAC/D;AACA;AACA,6CAAe,MAAM;;ACnCrB,SAAS,iBAAQ,KAAK,iBAAQ,wCAAwC,gBAAgB,sBAAsB,OAAO,2BAA2B,0BAA0B,yDAAyD,iCAAiC,kBAAkB,OAAO,iBAAQ;;AAEnS,SAAS,iCAAwB,qBAAqB,+BAA+B,aAAa,sCAA6B,oBAAoB,YAAY,oCAAoC,6DAA6D,YAAY,6BAA6B,OAAO,2BAA2B,0CAA0C,wEAAwE,+BAA+B;;AAE5d,SAAS,sCAA6B,qBAAqB,+BAA+B,iBAAiB,sCAAsC,YAAY,YAAY,uBAAuB,OAAO,qBAAqB,0CAA0C,6BAA6B;;AAEzP;AACP;AACnC,eAAe,8BAAU;AACzB;AACA;AACA;AACA;AACA,aAAa,iCAAwB;;AAErC,sBAAsB,sCAAmB,QAAQ,iBAAQ;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,sBAAsB,sCAAmB;AAC5C;AACA,GAAG,gBAAgB,sCAAmB;AACtC;AACA,GAAG,gBAAgB,sCAAmB;AACtC;AACA;AACA;AACA;AACA,GAAG,gBAAgB,sCAAmB;AACtC;AACA;AACA;AACA;AACA,GAAG,gBAAgB,sCAAmB;AACtC;AACA,GAAG;AACH,CAAC;AACD;AACA,SAAS,6BAAgB;AACzB,QAAQ,8BAAmB,EAAE,6BAAgB,EAAE,6BAAgB;AAC/D;AACA;AACA,gDAAe,QAAQ;;;;ACjDe;AAE/B,MAAM,CAAC,GAAG,aAAI,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC;;;ACFd;AACY;AACG;AAEX;AAC9B,IAAO,KAAK,GAAG,YAAG,CAAC,KAAK,CAAC;AAEzB,MAAM,sBAAuB,SAAQ,KAAK,CAAC,WAAW;IACpD;QACE,KAAK,CAAC,cAAc,EAAE,qBAAqB,CAAC,CAAC;IAC/C,CAAC;IAED,iBAAiB;QACf,IAAI,OAAO,GAAG,YAAG,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,CAAC;QAC3E,IAAI,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACtC,IAAI,OAAO,GAAyB;YAClC;gBACE,EAAE,EAAE,iBAAiB,EAAE,KAAK,EAAE,CAAC,CAAC,iBAAiB,CAAC,EAAE,IAAI,EAAE,MAAW;gBACrE,eAAe,EAAE;oBACf,OAAO,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE;oBAC7C,kBAAkB,EAAE,YAAG,CAAC,IAAI;iBAC7B;gBACD,QAAQ,EAAE,CAAC,MAAsB,EAAE,OAAwB,EAAE,EAAE;oBAC7D,IAAI,MAAM,GAAqB;wBAC7B,IAAI,EAAE;4BACJ;gCACE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,cAAc,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAW;gCAC5E,aAAa,EAAE,CAAC,IAAkB,EAAE,EAAE;oCACpC,OAAO,CACL,sEAAgC,CACjC,CAAC;gCACJ,CAAC;6BACF;4BACD;gCACE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,SAAa;gCAC1D,aAAa,EAAE,CAAC,IAAkB,EAAE,EAAE;oCACpC,OAAO,CACL,mEAA6B,CAC9B,CAAC;gCACJ,CAAC;6BACF;yBACF;qBACF,CAAC;oBACF,IAAI,IAAI,GAAG,CAAC,iCAAC,WAAE,CAAC,cAAc,IAAC,MAAM,EAAE,MAAM,GAAI,CAAC,CAAC;oBACnD,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,OAAO,EAAE,EACR;aACF;SACF;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,oBAAoB;QAClB,IAAI,OAAO,GAAG,YAAG,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,CAAC;QAC3E,IAAI,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACtC,IAAI,OAAO,GAAyB;YAClC;gBACE,EAAE,EAAE,iBAAiB,EAAE,KAAK,EAAE,CAAC,CAAC,iBAAiB,CAAC,EAAE,IAAI,EAAE,MAAW;gBACrE,eAAe,EAAE;oBACf,OAAO,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,kBAAkB,EAAE;oBACzD,kBAAkB,EAAE,YAAG,CAAC,IAAI;iBAC7B;gBACD,QAAQ,EAAE,CAAC,MAAsB,EAAE,OAAwB,EAAE,EAAE;oBAC7D,IAAI,MAAM,GAAqB;wBAC7B,IAAI,EAAE;4BACJ;gCACE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,cAAc,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAW;gCAC5E,aAAa,EAAE,CAAC,IAAkB,EAAE,EAAE;oCACpC,OAAO,CACL,sEAAgC,CACjC,CAAC;gCACJ,CAAC;6BACF;4BACD;gCACE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,SAAa;gCAC1D,aAAa,EAAE,CAAC,IAAkB,EAAE,EAAE;oCACpC,OAAO,CACL,mEAA6B,CAC9B,CAAC;gCACJ,CAAC;6BACF;4BACD;gCACE,IAAI,EAAE,kBAAkB,EAAE,KAAK,EAAE,CAAC,CAAC,kBAAkB,CAAC;gCAEtD,aAAa,EAAE,CAAC,IAAkB,EAAE,EAAE;oCACpC,OAAO,CACL,yEAAmC,CACpC,CAAC;gCACJ,CAAC;6BACF;yBACF;qBACF,CAAC;oBACF,IAAI,IAAI,GAAG,CAAC,iCAAC,WAAE,CAAC,cAAc,IAAC,MAAM,EAAE,MAAM,GAAI,CAAC,CAAC;oBACnD,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,OAAO,EAAE,EACR;aACF;SACF;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;CACF;AAEc,SAAS,IAAI;IAC1B,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC;IAC1C,KAAK,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,sBAAsB,EAAE,CAAC,CAAC;AAClE,CAAC;AAED,IAAI,KAAK,GAAG,oBAAY,CAAC;AACzB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;IACjB,KAAK,CAAC,IAAI,CAAC,GAAG,gBAAgB;IAC9B,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;AACrD,CAAC", "sources": ["webpack://datatp_ui_document_ie/./node_modules/.pnpm/react-feather@2.0.10_react@18.3.1/node_modules/react-feather/dist/icons/folder.js", "webpack://datatp_ui_document_ie/./node_modules/.pnpm/react-feather@2.0.10_react@18.3.1/node_modules/react-feather/dist/icons/file-text.js", "webpack://datatp_ui_document_ie/./src/backend.tsx", "webpack://datatp_ui_document_ie/./src/Init.tsx"], "sourcesContent": ["function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nimport React, { forwardRef } from 'react';\nimport PropTypes from 'prop-types';\nvar Folder = forwardRef(function (_ref, ref) {\n  var _ref$color = _ref.color,\n      color = _ref$color === void 0 ? 'currentColor' : _ref$color,\n      _ref$size = _ref.size,\n      size = _ref$size === void 0 ? 24 : _ref$size,\n      rest = _objectWithoutProperties(_ref, [\"color\", \"size\"]);\n\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    ref: ref,\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: size,\n    height: size,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, rest), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z\"\n  }));\n});\nFolder.propTypes = {\n  color: PropTypes.string,\n  size: PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n};\nFolder.displayName = 'Folder';\nexport default Folder;", "function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nimport React, { forwardRef } from 'react';\nimport PropTypes from 'prop-types';\nvar FileText = forwardRef(function (_ref, ref) {\n  var _ref$color = _ref.color,\n      color = _ref$color === void 0 ? 'currentColor' : _ref$color,\n      _ref$size = _ref.size,\n      size = _ref$size === void 0 ? 24 : _ref$size,\n      rest = _objectWithoutProperties(_ref, [\"color\", \"size\"]);\n\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    ref: ref,\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: size,\n    height: size,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, rest), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\"\n  }), /*#__PURE__*/React.createElement(\"polyline\", {\n    points: \"14 2 14 8 20 8\"\n  }), /*#__PURE__*/React.createElement(\"line\", {\n    x1: \"16\",\n    y1: \"13\",\n    x2: \"8\",\n    y2: \"13\"\n  }), /*#__PURE__*/React.createElement(\"line\", {\n    x1: \"16\",\n    y1: \"17\",\n    x2: \"8\",\n    y2: \"17\"\n  }), /*#__PURE__*/React.createElement(\"polyline\", {\n    points: \"10 9 9 9 8 9\"\n  }));\n});\nFileText.propTypes = {\n  color: PropTypes.string,\n  size: PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n};\nFileText.displayName = 'FileText';\nexport default FileText;", "import { i18n } from '@datatp-ui/lib';\n\nexport const T = i18n.getT(['document'])\n", "import React from \"react\";\r\nimport * as icon from 'react-feather';\r\nimport { app, bs } from '@datatp-ui/lib';\r\n\r\nimport { T } from \"./backend\";\r\nimport space = app.space;\r\n\r\nclass NewDocumentSpacePlugin extends space.SpacePlugin {\r\n  constructor() {\r\n    super('new-document', 'Document Navigation');\r\n  }\r\n\r\n  createUserScreens(): app.space.ScreenConfig[] {\r\n    let company = app.host.DATATP_HOST.session.getAccountAcl().getCompanyAcl();\r\n    let companyCode = company.companyCode;\r\n    let configs: space.ScreenConfig[] = [\r\n      {\r\n        id: \"new-document-ie\", label: T(\"New Document IE\"), icon: icon.Folder,\r\n        checkPermission: {\r\n          feature: { module: 'user', name: 'my-space' },\r\n          requiredCapability: app.READ,\r\n        },\r\n        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {\r\n          let config: bs.TabPaneConfig = {\r\n            tabs: [\r\n              {\r\n                name: 'document-set', label: 'Document Set', active: true, Icon: icon.Folder,\r\n                renderContent: (_ctx: bs.UIContext) => {\r\n                  return (\r\n                    <div>TODO: DocumentSetList</div>\r\n                  );\r\n                }\r\n              },\r\n              {\r\n                name: 'documents', label: 'Documents', Icon: icon.FileText,\r\n                renderContent: (_ctx: bs.UIContext) => {\r\n                  return (\r\n                    <div>TODO: DocumentList</div>\r\n                  );\r\n                }\r\n              },\r\n            ]\r\n          };\r\n          let html = (<bs.DefaultTabPane config={config} />);\r\n          return html;\r\n        },\r\n        screens: [\r\n        ]\r\n      }\r\n    ]\r\n    return configs;\r\n  }\r\n\r\n  createCompanyScreens(): app.space.ScreenConfig[] {\r\n    let company = app.host.DATATP_HOST.session.getAccountAcl().getCompanyAcl();\r\n    let companyCode = company.companyCode;\r\n    let configs: space.ScreenConfig[] = [\r\n      {\r\n        id: \"new-document-ie\", label: T(\"New Document IE\"), icon: icon.Folder,\r\n        checkPermission: {\r\n          feature: { module: 'document', name: 'company-document' },\r\n          requiredCapability: app.READ,\r\n        },\r\n        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {\r\n          let config: bs.TabPaneConfig = {\r\n            tabs: [\r\n              {\r\n                name: 'document-set', label: 'Document Set', active: true, Icon: icon.Folder,\r\n                renderContent: (_ctx: bs.UIContext) => {\r\n                  return (\r\n                    <div>TODO: DocumentSetList</div>\r\n                  );\r\n                }\r\n              },\r\n              {\r\n                name: 'documents', label: 'Documents', Icon: icon.FileText,\r\n                renderContent: (_ctx: bs.UIContext) => {\r\n                  return (\r\n                    <div>TODO: DocumentList</div>\r\n                  );\r\n                }\r\n              },\r\n              {\r\n                name: \"document-set-tag\", label: T(\"Document Set Tag\"),\r\n\r\n                renderContent: (_ctx: bs.UIContext) => {\r\n                  return (\r\n                    <div>TODO: DocumentSetTagList</div>\r\n                  );\r\n                }\r\n              },\r\n            ]\r\n          };\r\n          let html = (<bs.DefaultTabPane config={config} />);\r\n          return html;\r\n        },\r\n        screens: [\r\n        ]\r\n      }\r\n    ]\r\n    return configs;\r\n  }\r\n}\r\n\r\nexport default function Init() {\r\n  console.log('DocumentIE: Init().........')\r\n  space.SpacePluginManager.register(new NewDocumentSpacePlugin());\r\n}\r\n\r\nlet react = React as any;\r\nif (!react['id']) {\r\n  react['id'] = '@datatp-ui/lib'\r\n  console.log('Load React in module @datatp-ui/lib');\r\n}"], "names": [], "sourceRoot": ""}
/*! For license information please see remoteEntry.js.LICENSE.txt */
var Products;(()=>{"use strict";var e,t,r,n,o,u,a,i,f,l,s,c,p,d,y,h,v={713:(e,t)=>{var r=Symbol.for("react.element"),n=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),u=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),f=Symbol.for("react.context"),l=Symbol.for("react.forward_ref"),s=Symbol.for("react.suspense"),c=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),d=Symbol.iterator,y={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},h=Object.assign,v={};function m(e,t,r){this.props=e,this.context=t,this.refs=v,this.updater=r||y}function b(){}function _(e,t,r){this.props=e,this.context=t,this.refs=v,this.updater=r||y}m.prototype.isReactComponent={},m.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},m.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},b.prototype=m.prototype;var g=_.prototype=new b;g.constructor=_,h(g,m.prototype),g.isPureReactComponent=!0;var S=Array.isArray,w=Object.prototype.hasOwnProperty,k={current:null},E={key:!0,ref:!0,__self:!0,__source:!0};function $(e,t,n){var o,u={},a=null,i=null;if(null!=t)for(o in void 0!==t.ref&&(i=t.ref),void 0!==t.key&&(a=""+t.key),t)w.call(t,o)&&!E.hasOwnProperty(o)&&(u[o]=t[o]);var f=arguments.length-2;if(1===f)u.children=n;else if(1<f){for(var l=Array(f),s=0;s<f;s++)l[s]=arguments[s+2];u.children=l}if(e&&e.defaultProps)for(o in f=e.defaultProps)void 0===u[o]&&(u[o]=f[o]);return{$$typeof:r,type:e,key:a,ref:i,props:u,_owner:k.current}}function C(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}var j=/\/+/g;function R(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function P(e,t,o,u,a){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var f=!1;if(null===e)f=!0;else switch(i){case"string":case"number":f=!0;break;case"object":switch(e.$$typeof){case r:case n:f=!0}}if(f)return a=a(f=e),e=""===u?"."+R(f,0):u,S(a)?(o="",null!=e&&(o=e.replace(j,"$&/")+"/"),P(a,t,o,"",(function(e){return e}))):null!=a&&(C(a)&&(a=function(e,t){return{$$typeof:r,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(a,o+(!a.key||f&&f.key===a.key?"":(""+a.key).replace(j,"$&/")+"/")+e)),t.push(a)),1;if(f=0,u=""===u?".":u+":",S(e))for(var l=0;l<e.length;l++){var s=u+R(i=e[l],l);f+=P(i,t,o,s,a)}else if(s=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=d&&e[d]||e["@@iterator"])?e:null}(e),"function"==typeof s)for(e=s.call(e),l=0;!(i=e.next()).done;)f+=P(i=i.value,t,o,s=u+R(i,l++),a);else if("object"===i)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return f}function O(e,t,r){if(null==e)return e;var n=[],o=0;return P(e,n,"","",(function(e){return t.call(r,e,o++)})),n}function x(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var I={current:null},T={transition:null},A={ReactCurrentDispatcher:I,ReactCurrentBatchConfig:T,ReactCurrentOwner:k};function M(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:O,forEach:function(e,t,r){O(e,(function(){t.apply(this,arguments)}),r)},count:function(e){var t=0;return O(e,(function(){t++})),t},toArray:function(e){return O(e,(function(e){return e}))||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=m,t.Fragment=o,t.Profiler=a,t.PureComponent=_,t.StrictMode=u,t.Suspense=s,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=A,t.act=M,t.cloneElement=function(e,t,n){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var o=h({},e.props),u=e.key,a=e.ref,i=e._owner;if(null!=t){if(void 0!==t.ref&&(a=t.ref,i=k.current),void 0!==t.key&&(u=""+t.key),e.type&&e.type.defaultProps)var f=e.type.defaultProps;for(l in t)w.call(t,l)&&!E.hasOwnProperty(l)&&(o[l]=void 0===t[l]&&void 0!==f?f[l]:t[l])}var l=arguments.length-2;if(1===l)o.children=n;else if(1<l){f=Array(l);for(var s=0;s<l;s++)f[s]=arguments[s+2];o.children=f}return{$$typeof:r,type:e.type,key:u,ref:a,props:o,_owner:i}},t.createContext=function(e){return(e={$$typeof:f,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:i,_context:e},e.Consumer=e},t.createElement=$,t.createFactory=function(e){var t=$.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:l,render:e}},t.isValidElement=C,t.lazy=function(e){return{$$typeof:p,_payload:{_status:-1,_result:e},_init:x}},t.memo=function(e,t){return{$$typeof:c,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=T.transition;T.transition={};try{e()}finally{T.transition=t}},t.unstable_act=M,t.useCallback=function(e,t){return I.current.useCallback(e,t)},t.useContext=function(e){return I.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return I.current.useDeferredValue(e)},t.useEffect=function(e,t){return I.current.useEffect(e,t)},t.useId=function(){return I.current.useId()},t.useImperativeHandle=function(e,t,r){return I.current.useImperativeHandle(e,t,r)},t.useInsertionEffect=function(e,t){return I.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return I.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return I.current.useMemo(e,t)},t.useReducer=function(e,t,r){return I.current.useReducer(e,t,r)},t.useRef=function(e){return I.current.useRef(e)},t.useState=function(e){return I.current.useState(e)},t.useSyncExternalStore=function(e,t,r){return I.current.useSyncExternalStore(e,t,r)},t.useTransition=function(){return I.current.useTransition()},t.version="18.3.1"},758:(e,t,r)=>{e.exports=r(713)},253:(e,t,r)=>{var n={"./App":()=>r.e(876).then((()=>()=>r(876)))},o=(e,t)=>(r.R=t,t=r.o(n,e)?n[e]():Promise.resolve().then((()=>{throw new Error('Module "'+e+'" does not exist in container.')})),r.R=void 0,t),u=(e,t)=>{if(r.S){var n="default",o=r.S[n];if(o&&o!==e)throw new Error("Container initialization failed as it has already been initialized with a different share scope");return r.S[n]=e,r.I(n,t)}};r.d(t,{get:()=>o,init:()=>u})}},m={};function b(e){var t=m[e];if(void 0!==t)return t.exports;var r=m[e]={exports:{}};return v[e](r,r.exports,b),r.exports}b.m=v,b.c=m,b.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return b.d(t,{a:t}),t},b.d=(e,t)=>{for(var r in t)b.o(t,r)&&!b.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},b.f={},b.e=e=>Promise.all(Object.keys(b.f).reduce(((t,r)=>(b.f[r](e,t),t)),[])),b.u=e=>e+".js",b.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),e={},t="datatp_ui_test:",b.l=(r,n,o,u)=>{if(e[r])e[r].push(n);else{var a,i;if(void 0!==o)for(var f=document.getElementsByTagName("script"),l=0;l<f.length;l++){var s=f[l];if(s.getAttribute("src")==r||s.getAttribute("data-webpack")==t+o){a=s;break}}a||(i=!0,(a=document.createElement("script")).charset="utf-8",a.timeout=120,b.nc&&a.setAttribute("nonce",b.nc),a.setAttribute("data-webpack",t+o),a.src=r),e[r]=[n];var c=(t,n)=>{a.onerror=a.onload=null,clearTimeout(p);var o=e[r];if(delete e[r],a.parentNode&&a.parentNode.removeChild(a),o&&o.forEach((e=>e(n))),t)return t(n)},p=setTimeout(c.bind(null,void 0,{type:"timeout",target:a}),12e4);a.onerror=c.bind(null,a.onerror),a.onload=c.bind(null,a.onload),i&&document.head.appendChild(a)}},b.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{b.S={};var e={},t={};b.I=(r,n)=>{n||(n=[]);var o=t[r];if(o||(o=t[r]={}),!(n.indexOf(o)>=0)){if(n.push(o),e[r])return e[r];b.o(b.S,r)||(b.S[r]={});var u=b.S[r],a="datatp_ui_test",i=[];return"default"===r&&((e,t)=>{var r=u[e]=u[e]||{},n=r[t];(!n||!n.loaded&&(0!=!n.eager||a>n.from))&&(r[t]={get:()=>()=>b(758),from:a,eager:!0})})("react","18.3.1"),e[r]=i.length?Promise.all(i).then((()=>e[r]=1)):1}}})(),b.p="/assets/test/",r=e=>{var t=e=>e.split(".").map((e=>+e==e?+e:e)),r=/^([^-+]+)?(?:-([^+]+))?(?:\+(.+))?$/.exec(e),n=r[1]?t(r[1]):[];return r[2]&&(n.length++,n.push.apply(n,t(r[2]))),r[3]&&(n.push([]),n.push.apply(n,t(r[3]))),n},n=(e,t)=>{e=r(e),t=r(t);for(var n=0;;){if(n>=e.length)return n<t.length&&"u"!=(typeof t[n])[0];var o=e[n],u=(typeof o)[0];if(n>=t.length)return"u"==u;var a=t[n],i=(typeof a)[0];if(u!=i)return"o"==u&&"n"==i||"s"==i||"u"==u;if("o"!=u&&"u"!=u&&o!=a)return o<a;n++}},o=e=>{var t=e[0],r="";if(1===e.length)return"*";if(t+.5){r+=0==t?">=":-1==t?"<":1==t?"^":2==t?"~":t>0?"=":"!=";for(var n=1,u=1;u<e.length;u++)n--,r+="u"==(typeof(i=e[u]))[0]?"-":(n>0?".":"")+(n=2,i);return r}var a=[];for(u=1;u<e.length;u++){var i=e[u];a.push(0===i?"not("+f()+")":1===i?"("+f()+" || "+f()+")":2===i?a.pop()+" "+a.pop():o(i))}return f();function f(){return a.pop().replace(/^\((.+)\)$/,"$1")}},u=(e,t)=>{if(0 in e){t=r(t);var n=e[0],o=n<0;o&&(n=-n-1);for(var a=0,i=1,f=!0;;i++,a++){var l,s,c=i<e.length?(typeof e[i])[0]:"";if(a>=t.length||"o"==(s=(typeof(l=t[a]))[0]))return!f||("u"==c?i>n&&!o:""==c!=o);if("u"==s){if(!f||"u"!=c)return!1}else if(f)if(c==s)if(i<=n){if(l!=e[i])return!1}else{if(o?l>e[i]:l<e[i])return!1;l!=e[i]&&(f=!1)}else if("s"!=c&&"n"!=c){if(o||i<=n)return!1;f=!1,i--}else{if(i<=n||s<c!=o)return!1;f=!1}else"s"!=c&&"n"!=c&&(f=!1,i--)}}var p=[],d=p.pop.bind(p);for(a=1;a<e.length;a++){var y=e[a];p.push(1==y?d()|d():2==y?d()&d():y?u(y,t):!d())}return!!d()},a=(e,t)=>{var r=e[t];return Object.keys(r).reduce(((e,t)=>!e||!r[e].loaded&&n(e,t)?t:e),0)},i=(e,t,r,n)=>"Unsatisfied version "+r+" from "+(r&&e[t][r].from)+" of shared singleton module "+t+" (required "+o(n)+")",f=(e,t,r,n)=>{var o=a(e,r);return u(n,o)||l(i(e,r,o,n)),s(e[r][o])},l=e=>{"undefined"!=typeof console&&console.warn&&console.warn(e)},s=e=>(e.loaded=1,e.get()),c=(e=>function(t,r,n,o){var u=b.I(t);return u&&u.then?u.then(e.bind(e,t,b.S[t],r,n,o)):e(0,b.S[t],r,n,o)})(((e,t,r,n,o)=>t&&b.o(t,r)?f(t,0,r,n):o())),p={},d={815:()=>c("default","react",[1,18,3,1],(()=>()=>b(758)))},y={876:[815]},h={},b.f.consumes=(e,t)=>{b.o(y,e)&&y[e].forEach((e=>{if(b.o(p,e))return t.push(p[e]);if(!h[e]){var r=t=>{p[e]=0,b.m[e]=r=>{delete b.c[e],r.exports=t()}};h[e]=!0;var n=t=>{delete p[e],b.m[e]=r=>{throw delete b.c[e],t}};try{var o=d[e]();o.then?t.push(p[e]=o.then(r).catch(n)):r(o)}catch(e){n(e)}}}))},(()=>{var e={327:0};b.f.j=(t,r)=>{var n=b.o(e,t)?e[t]:void 0;if(0!==n)if(n)r.push(n[2]);else{var o=new Promise(((r,o)=>n=e[t]=[r,o]));r.push(n[2]=o);var u=b.p+b.u(t),a=new Error;b.l(u,(r=>{if(b.o(e,t)&&(0!==(n=e[t])&&(e[t]=void 0),n)){var o=r&&("load"===r.type?"missing":r.type),u=r&&r.target&&r.target.src;a.message="Loading chunk "+t+" failed.\n("+o+": "+u+")",a.name="ChunkLoadError",a.type=o,a.request=u,n[1](a)}}),"chunk-"+t,t)}};var t=(t,r)=>{var n,o,[u,a,i]=r,f=0;if(u.some((t=>0!==e[t]))){for(n in a)b.o(a,n)&&(b.m[n]=a[n]);i&&i(b)}for(t&&t(r);f<u.length;f++)o=u[f],b.o(e,o)&&e[o]&&e[o][0](),e[o]=0},r=self.webpackChunkdatatp_ui_test=self.webpackChunkdatatp_ui_test||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var _=b(253);Products=_})();
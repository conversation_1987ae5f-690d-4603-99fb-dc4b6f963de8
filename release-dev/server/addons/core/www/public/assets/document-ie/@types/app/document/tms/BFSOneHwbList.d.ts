import { entity, grid } from '@datatp-ui/lib';
export declare class BFSOneHwbListPlugin extends entity.DbEntityListPlugin {
    bean: any;
    categoryId: number;
    constructor(searchMethod: any, search?: string);
    loadData(uiList: entity.DbEntityList<any>): void;
    withNeedFilter(): this;
    searchBy(): this;
}
export declare class B<PERSON>OneHwbList extends entity.DbEntityList {
    createVGridConfig(): grid.VGridConfig;
    reloadData(_forceReload?: boolean): void;
}
//# sourceMappingURL=BFSOneHwbList.d.ts.map
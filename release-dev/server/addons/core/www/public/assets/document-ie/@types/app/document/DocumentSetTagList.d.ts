import { grid, entity } from '@datatp-ui/lib';
export declare class DocumentSetTagListPlugin extends entity.DbEntityListPlugin {
    constructor();
    loadData(uiList: entity.DbEntityList<any>): void;
}
export declare class DocumentSetTagList extends entity.DbEntityList {
    createVGridConfig(): grid.VGridConfig;
    onNew(): void;
    onDefaultSelect(dRecord: grid.DisplayRecord): void;
}
//# sourceMappingURL=DocumentSetTagList.d.ts.map
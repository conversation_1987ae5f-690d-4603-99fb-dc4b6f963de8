import React from 'react';
import { app } from '@datatp-ui/lib';
import { UIDocumentSetPlugin } from "../DocumentSetPlugin";
import { DocumentList } from "../DocumentList";
export declare class AccountingDocChargeSummaryPlugin extends UIDocumentSetPlugin {
    constructor();
    getAccountingInvoiceSummary: (uiList: DocumentList) => void;
    renderActionButton(_appContext: app.AppContext, _pageContext: app.PageContext, uiList: DocumentList, viewType: string): React.JSX.Element;
}
//# sourceMappingURL=AccountingDocumentSetAction.d.ts.map
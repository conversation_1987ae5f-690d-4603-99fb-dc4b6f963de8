import React from "react";
import { app } from '@datatp-ui/lib';
export declare abstract class UIDocumentPlugin {
    name: string;
    protected constructor(name: string);
    hasPreviewTab(): boolean;
    render(_appContext: app.AppContext, _pageContext: app.PageContext, data: any, onCommit: (bean: any) => void): React.JSX.Element;
}
export declare class DocumentPluginManager {
    plugins: Record<string, UIDocumentPlugin>;
    getPluginMap(): Record<string, UIDocumentPlugin>;
    getPluginByName(name: string): UIDocumentPlugin | null;
    register(plugin: UIDocumentPlugin): void;
}
export declare const DOCUMENT_PLUGIN_REGISTRY: DocumentPluginManager;
//# sourceMappingURL=DocumentPlugin.d.ts.map
{"version": 3, "file": "src_App_tsx.js", "mappings": ";;;;;;;;;;;;;;;;;AAA0B;AACU;AAErB,MAAM,aAAc,SAAQ,8CAAE,CAAC,aAAa;IACvD,MAAM;QACF,IAAI,IAAI,GAAG,CACP,uGAAuC,CAC1C,CAAC;QACF,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ", "sources": ["webpack://datatp_ui_document_ie/./src/App.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { bs } from '@datatp-ui/lib';\r\n\r\nexport default class DocumentIEApp extends bs.BaseComponent {\r\n    render() {\r\n        let html = (\r\n            <div>Hello Remote Document IE App</div>\r\n        );\r\n        return html;\r\n    }\r\n}"], "names": [], "sourceRoot": ""}
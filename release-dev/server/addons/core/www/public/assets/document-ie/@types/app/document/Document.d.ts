import React from 'react';
import { module } from '@datatp-ui/erp';
import { entity } from '@datatp-ui/lib';
export declare class DocumentEditor extends entity.AppDbEntityEditor {
    onChangeType: (bean: any, field: string, oldVal: any, newVal: any) => void;
    render(): React.JSX.Element;
}
interface DocumentProps extends entity.AppDbEntityEditorProps {
    storage: module.storage.IStorage;
    documentSet: any;
}
export declare class Document extends entity.AppDbEntityEditor<DocumentProps> {
    reloadData: () => void;
    processIE: () => void;
    onModifyDocument: (_bean: any, field: string, _oldVal: any, newVal: any) => void;
    DownloadButton: () => React.JSX.Element | undefined;
    PreviewDocument: () => React.JSX.Element;
    InvoicePreviewTab: () => React.JSX.Element | undefined;
    IETab(doc: any): React.JSX.Element;
    pluginNames: Array<string>;
    loadIEPluginNames(): void;
    IEPluginOptions(doc: any): React.JSX.Element;
    onUpdateDocumentTypes(docType: string): void;
    render(): React.JSX.Element;
}
export {};
//# sourceMappingURL=Document.d.ts.map
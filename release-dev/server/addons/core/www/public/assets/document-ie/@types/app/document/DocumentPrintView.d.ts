import React from 'react';
import { module } from '@datatp-ui/erp';
import { bs, component, app, sql } from '@datatp-ui/lib';
interface UIDocumentPrintViewProps extends module.misc.UIPrintViewProps {
    title: string;
    documentIds: number[];
}
export declare class UIDocumentPrintView extends module.misc.UIPrintView<UIDocumentPrintViewProps> {
    createPrintData(): void;
    createLoadPrintUrl(config: component.PrintConfig, format: string): string;
    loadPrint: (_ctx: bs.WidgetContext, config: component.PrintConfig, format: string, reportCallback: component.PrintCallback) => void;
    createPrintConfigs(): component.PrintConfig[];
}
interface DocumentPrintViewLoadableProps extends app.AppComponentProps {
    documentSetId: any;
    title: string;
    type?: 'invoice';
}
export declare class DocumentPrintViewLoadable extends app.AppComponent<DocumentPrintViewLoadableProps> {
    documentIds: Array<any>;
    createSearchParams(documentSetId: number): sql.SqlSearchParams;
    componentDidMount(): void;
    render(): React.ReactNode;
}
export {};
//# sourceMappingURL=DocumentPrintView.d.ts.map
import React from 'react';
import { entity, grid } from '@datatp-ui/lib';
export declare class DocTmsChargeSummaryItemForm extends entity.AppDbEntity {
    render(): React.JSX.Element;
}
interface DocTmsChargeSummaryItemListEditorProps extends entity.VGridEntityListEditorProps {
    documentType: string;
}
export declare class DocTmsChargeSummaryItemListEditor extends entity.VGridEntityListEditor<DocTmsChargeSummaryItemListEditorProps> {
    bfsOneHeaderRender(_ctx: grid.VGridContext, _field: grid.FieldConfig, headerEle: any): React.JSX.Element;
    cssCheckField: (record: any, field: string) => "" | "bg-danger bg-opacity-25";
    onShowInvoice: (item: any) => void;
    onSearchBFSOneHwb: (item: any) => void;
    buildPluginCol(): grid.FieldConfig[];
    onInputChange: (ctx: grid.FieldContext, _oldVal: any, _newVal: any) => void;
    onInputChangeAndClearHwb: (item: any, field: string, _oldVal: any, _newVal: any) => void;
    showCheckVerifyStatusToolTip(record: any): React.JSX.Element;
    createVGridConfig(): grid.VGridConfig;
    static recordErrorFields: (item: any) => any[];
    onAddRecordConverted: (records: Array<any>) => void;
    onNewAction(): void;
}
export {};
//# sourceMappingURL=DocTmsChargeSummaryItemListEditor.d.ts.map
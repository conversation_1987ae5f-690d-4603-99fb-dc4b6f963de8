import React, { ReactElement } from 'react';
import { entity, app } from '@datatp-ui/lib';
import { UIDocumentPlugin } from "../DocumentPlugin";
export declare class UISellerForm extends entity.AppDbEntity {
    render(): React.JSX.Element;
}
export declare class UIBuyerForm extends entity.AppDbEntity {
    render(): React.JSX.Element;
}
export declare class DocInvoiceEditor extends entity.AppDbComplexEntityEditor {
    getVerificationStyle: (status: any) => {
        backgroundColor: string;
        color: string;
        padding: string;
        borderRadius: string;
    } | {
        backgroundColor?: undefined;
        color?: undefined;
        padding?: undefined;
        borderRadius?: undefined;
    };
    getVerificationIcon: (status: any) => ReactElement;
    render(): React.JSX.Element;
}
interface DocInvoiceLoadableProps extends app.AppComponentProps {
    invoiceData: any;
}
export declare class DocInvoiceLoadable extends app.AppComponent<DocInvoiceLoadableProps> {
    entity: any;
    componentDidMount(): void;
    renderEntity(_entity: any): React.ReactNode;
    render(): React.ReactNode;
}
export declare class DocInvoicePlugin extends UIDocumentPlugin {
    constructor();
    hasPreviewTab(): boolean;
    render(_appContext: app.AppContext, _pageContext: app.PageContext, data: any, onCommit: (bean: any) => void): React.JSX.Element;
}
export {};
//# sourceMappingURL=DocInvoice.d.ts.map
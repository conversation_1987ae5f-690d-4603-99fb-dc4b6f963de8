"use strict";
(self["webpackChunkdatatp_ui_document_ie"] = self["webpackChunkdatatp_ui_document_ie"] || []).push([["src_App_tsx"],{

/***/ "./src/App.tsx":
/*!*********************!*\
  !*** ./src/App.tsx ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ DocumentIEApp)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "webpack/sharing/consume/default/react/react?55d4");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _datatp_ui_lib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @datatp-ui/lib */ "webpack/sharing/consume/default/@datatp-ui/lib/@datatp-ui/lib");
/* harmony import */ var _datatp_ui_lib__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_datatp_ui_lib__WEBPACK_IMPORTED_MODULE_1__);


class DocumentIEApp extends _datatp_ui_lib__WEBPACK_IMPORTED_MODULE_1__.bs.BaseComponent {
    render() {
        let html = (react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", null, "Hello Remote Document IE App"));
        return html;
    }
}


/***/ })

}]);
//# sourceMappingURL=src_App_tsx.js.map
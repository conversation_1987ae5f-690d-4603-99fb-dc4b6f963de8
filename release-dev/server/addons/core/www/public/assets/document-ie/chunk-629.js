"use strict";(self.webpackChunkdatatp_ui_document_ie=self.webpackChunkdatatp_ui_document_ie||[]).push([[629],{1088:(e,t,a)=>{a.d(t,{y:()=>h});var n=a(5208),o=a.n(n),i=a(1215),r=a(3437),s=a(570),l=a(1529),c=a(1369),p=a(523),d=a(5161),m=a(1980),u=a(5813);class g extends c.entity.AppDbEntityEditor{onChangeType=(e,t,a,n)=>{const{observer:o,onModify:i}=this.props;o.replaceBeanProperty(t,n),i&&i(e,t,a,n)};render(){let{observer:e,appContext:t,pageContext:a}=this.props,n=e.getMutableBean(),i=a.hasUserWriteCapability();return o().createElement("div",{className:"flex-vbox p-1"},o().createElement("div",{className:"flex-hbox"},o().createElement("div",{className:"flex-hbox flex-grow-1 border rounded p-1"},o().createElement("div",{className:"flex-vbox w-50 me-2"},o().createElement(c.bs.Row,null,o().createElement(c.bs.Col,{span:6},o().createElement(c.input.BBStringField,{bean:n,field:"name",label:(0,p.T)("Name"),inputObserver:e,disable:!i})),o().createElement(c.bs.Col,{span:6},o().createElement(l.module.resource.BBRefResource,{appContext:t,pageContext:a,placement:"bottom-start",offset:[0,5],minWidth:300,disable:!i,label:(0,p.T)("Document Type"),placeholder:"",required:!0,bean:n,beanIdField:"type",beanLabelField:"type",resourceType:"document-type",refResourceBy:"identifier"}))),o().createElement(c.bs.Row,null,o().createElement(c.bs.Col,{span:6},o().createElement(c.input.BBStringField,{label:(0,p.T)("Media Type"),bean:n,field:"mediaType",inputObserver:e,disable:!i})),o().createElement(c.bs.Col,{span:6},o().createElement(c.input.BBStringField,{label:(0,p.T)("Path"),bean:n,field:"path",inputObserver:e,disable:!0})))),o().createElement("div",{className:"w-50"},o().createElement(c.input.BBTextField,{label:(0,p.T)("Description"),style:{height:"3.5em"},bean:n,field:"description",inputObserver:e,disable:!i}),o().createElement("div",{className:"flex-hbox"},o().createElement(c.bs.FormLabel,{className:"align-content-center"},"Task Status"),o().createElement(c.input.BBMultiLabelSelector,{labelBeans:n.tasks,labelField:"status",disable:!0})))),o().createElement("div",{className:"flex-vbox flex-grow-0 ps-2 align-content-center justify-content-center"},o().createElement(c.entity.ButtonEntityCommit,{className:"p-1",style:{width:"70px"},appContext:t,pageContext:a,observer:e,commit:{entityLabel:"Document",context:"company",service:"DocumentService",commitMethod:"saveDocument"},onPostCommit:this.onPostCommit}))))}}class h extends c.entity.AppDbEntityEditor{reloadData=()=>{let{appContext:e,observer:t}=this.props,a=t.getBeanProperty("id");e.createHttpBackendCall("DocumentService","getDocument",{id:a}).withSuccessData(e=>{t.replaceWithUpdate(e),this.forceUpdate()}).call()};processIE=()=>{let{appContext:e,observer:t}=this.props,a=t.getMutableBean();e.createHttpBackendCall("DocumentService","processIE",{documentIds:[a.id]}).withSuccessData(e=>{this.markLoading(!1),this.nextViewId(),this.forceUpdate()}).withFail(()=>{this.markLoading(!1),this.forceUpdate()}).withEntityOpNotification("commit","Process IE").call(),this.markLoading(!0),this.forceUpdate()};onModifyDocument=(e,t,a,n)=>{let{observer:o}=this.props;o.replaceBeanProperty(t,n),this.forceUpdate()};DownloadButton=()=>{let{appContext:e,observer:t}=this.props,a=t.getMutableBean(),n=a.mediaType;if(d.n.includes(n)){let t=e.getServerContext().createServerURL(`/storage/company/get-encrypt/${a.storeId}`,{download:!0});return o().createElement("a",{className:"btn btn-primary btn-sm",href:t,target:"_blank",download:!0},o().createElement(i.A,{size:12})," Download")}};PreviewDocument=()=>{let{appContext:e,pageContext:t,observer:a}=this.props,n=a.getMutableBean(),i=n.storeId,r=n.mediaType,s=o().createElement("div",null,"This plugin is not support this file!");if(d.n.includes(r)){let t="https://view.officeapps.live.com/op/embed.aspx?src="+e.getServerContext().createServerURL(`/storage/company/get-encrypt/${i}`,{download:!0});s=o().createElement("iframe",{src:t,className:"flex-vbox",style:{fontSize:"0.85rem"}})}else d.P.includes(r)&&(s=o().createElement(l.module.storage.UICompanyStoragePreview,{appContext:e,pageContext:t,key:`${this.viewId}-preview`,url:i,params:"",allowDownload:!1}));return o().createElement(c.bs.Tab,{label:"Document",name:"document",active:!0},s)};InvoicePreviewTab=()=>{let{appContext:e,pageContext:t,observer:a,documentSet:n}=this.props,i=a.getMutableBean().type;if(!i||i.endsWith("inv-summary"))return o().createElement(c.bs.Tab,{label:"Invoices",name:"invoices"},o().createElement(m.y,{title:"Invoices",appContext:e,pageContext:t,documentSetId:n.id,readOnly:!0}))};IETab(e){let t,{appContext:a,pageContext:n,documentSet:i}=this.props,r=null,s=e.type,l=s||"Unknown";return t=u.mC.getPluginByName(s),t?(r=t.render(a,n,{plugin:s,document:e,documentSet:i},e=>{this.onPostCommit(e),this.reloadData()}),o().createElement(c.bs.Tab,{name:"document-ie",label:`Document IE[${l}]`,active:!0},r)):(this.loadIEPluginNames(),this.IEPluginOptions(e))}pluginNames=[];loadIEPluginNames(){let{appContext:e}=this.props;this.pluginNames.length>0||e.createHttpBackendCall("DocumentService","getDocumentIEPluginNames").withSuccessData(e=>{this.pluginNames=e,this.forceUpdate()}).call()}IEPluginOptions(e){let t=o().createElement("div",{className:"py-5"},"Unknown Document Type ",e.type);if(0==this.pluginNames.length)return t;let a=[];return this.pluginNames.forEach(e=>{a.push(o().createElement(c.bs.Button,{laf:"info",outline:!0,style:{height:100},className:"mx-1",onClick:()=>this.onUpdateDocumentTypes(e)},o().createElement(r.A,{size:12})," ",e.toUpperCase()))}),o().createElement(c.bs.Tab,{name:"document-ie",label:`Document IE[${e.type}]`,active:!0},o().createElement("div",{className:"flex-hbox"},a))}onUpdateDocumentTypes(e){let{appContext:t,observer:a}=this.props,n=a.getBeanProperty("id");t.createHttpBackendCall("DocumentService","updateDocumentTypes",{docIds:[n],docType:e}).withSuccessData(e=>{this.reloadData()}).call()}render(){let{appContext:e,pageContext:t,observer:a}=this.props,n=this,i=a.getMutableBean();const r=t.hasUserWriteCapability();let l=r&&["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel"].includes(i.mediaType),d=i.name;i.tasks&&i.tasks.length>0&&(d=`${i.name} [${i.tasks[0].status}]`);let m=u.mC.getPluginByName(i.type),h=!1;return m&&(h=m.hasPreviewTab()),o().createElement("div",{className:"flex-vbox"},o().createElement(c.bs.Card,{className:"flex-vbox-grow-0",header:`Document: ${d}`,collapse:!0},o().createElement(g,{appContext:e,pageContext:t,observer:a,readOnly:!r,onModify:this.onModifyDocument})),h?o().createElement(c.bs.VSplit,{className:"flex-grow-1"},o().createElement(c.bs.VSplitPane,{className:"pr-1",width:"50%"},o().createElement(c.bs.TabPane,null,n.IETab(i))),o().createElement(c.bs.VSplitPane,{className:"flex-vbox border rounded p-1"},o().createElement(c.bs.TabPane,null,n.PreviewDocument(),n.InvoicePreviewTab()),o().createElement(c.bs.Toolbar,{hide:!l},n.DownloadButton(),o().createElement(c.bs.Button,{laf:"primary",onClick:this.processIE,disabled:this.loading},o().createElement(s.A,{size:12})," ",(0,p.T)("Process IE"))))):o().createElement(c.bs.TabPane,null,n.IETab(i)))}}},2036:(e,t,a)=>{a.d(t,{$:()=>w,g:()=>I});var n=a(5208),o=a.n(n),i=a(1529),r=a(1369),s=a(1697),l=a(9821),c=a(3437),p=a(5929),d=a(570),m=a(1675),u=a(1215),g=a(2466),h=a(8614),b=a(9095),C=a(9959),E=a(7407),y=a(944),f=a(8764),x=a(523),S=a(1088),v=a(1980),T=a(2528),D=a(5343);class w extends r.entity.DbEntityListPlugin{documentSetId;constructor(){super([]),this.backend={context:"company",service:"DocumentService",searchMethod:"searchDocuments",deleteMethod:"deleteDocuments"},this.searchParams={filters:[...r.sql.createSearchFilter()],optionFilters:[r.sql.createStorageStateFilter([r.entity.StorageState.ACTIVE,r.entity.StorageState.ARCHIVED])],orderBy:{fields:["modifiedTime"],fieldLabels:["Modified Time"],selectFields:["modifiedTime"],sort:"DESC"},maxReturn:1e3}}withScope(e){return this.addSearchParam("scope",e),this}withDocumentSet(e){return e&&(this.documentSetId=e,this.addSearchParam("documentSetId",e)),this}withDocumentRoot(e){return e&&this.addSearchParam("rootId",e),this}withRequestType(e){return e&&this.addSearchParam("requestType",e),this}loadData(e){let t={params:this.searchParams};this.createBackendSearch(e,t).call()}backendDelete(e,t){let a={docIds:t};this.createBackendDelete(e,a,t).call()}}class I extends r.entity.DbEntityList{showCheckProcessStatusToolTip(e){let t=e.processNote;if(t){if(t.includes("INCORRECT"))return o().createElement(y.A,{size:18,className:"text-danger"});if(t.includes("CORRECT"))return o().createElement(y.A,{size:18,className:"text-success"});if(t.includes("PARTIAL")){let e=t.split("|")[0],a=t.split("-")[1];return o().createElement(r.bs.Tooltip,{tooltip:a,className:"text-warning flex-hbox align-items-center"},o().createElement(y.A,{size:18}),o().createElement("span",{className:"ms-1"},e))}}return o().createElement(o().Fragment,null)}showCheckMissingStatusToolTip(e){let t=e.missingField;if(t){if(t.includes("-")){let e=t.split("-")[0],a=t.split("-")[1];return o().createElement(r.bs.Tooltip,{tooltip:a,className:"text-warning flex-hbox align-items-center"},o().createElement(f.A,{size:18}),o().createElement("span",{className:"ms-1"},e))}return o().createElement(f.A,{size:18,className:"text-success"})}return o().createElement(o().Fragment,null)}createVGridConfig(){let{pageContext:e,documentSet:t,viewType:a,viewName:n,plugin:i}=this.props,b=e.hasUserWriteCapability(),C=e.hasUserModeratorCapability(),E=this,y=r.entity.DbEntityListConfigTool.FIELD_ON_SELECT("name",(0,x.T)("Name"),250);delete y.editor;let f={record:{fields:[...r.entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),r.entity.DbEntityListConfigTool.FIELD_INDEX(),{name:"missingField",label:(0,x.T)(""),width:50,container:"fixed-left",state:{showRecordState:!0},editor:{type:"string",renderCustom(e,t){const{fieldConfig:a,displayRecord:n,tabIndex:o,focus:i}=e;let r=n.record;return E.showCheckMissingStatusToolTip(r)}}},{...y,state:{showRecordState:!0}},{name:"path",label:(0,x.T)("Path"),width:250,state:{visible:!1}},{name:"type",label:(0,x.T)("Document Type"),width:140,filterable:!0,filterableType:"Options",editor:{enable:!0,type:"string"}},{name:"mediaType",label:(0,x.T)("Media Type"),width:120,filterable:!0,filterableType:"Options",editor:{enable:!0,type:"string"}},{name:"requestProcessType",label:(0,x.T)("Req Process"),width:90,state:{visible:!1}},{name:"tasks",label:(0,x.T)("Tasks"),width:300,fieldDataGetter:e=>{if(!e.tasks)return"";let t=[],a=e.tasks;for(let e of a){let a=e.processStatus;a&&t.push(a)}return t.join(", ")},dataTooltip:!0},{name:"description",label:(0,x.T)("Description"),width:300,editor:{enable:!0,type:"string"}},{name:"searchableWords",label:(0,x.T)("Searchable Words"),width:300,dataTooltip:!0,state:{visible:!1}},{name:"documentSetName",label:(0,x.T)("D.Set Name"),width:150,state:{visible:!1}},{name:"processNote",label:(0,x.T)("Note"),width:50,container:"fixed-right",state:{showRecordState:!0},editor:{type:"string",renderCustom(e,t){const{fieldConfig:a,displayRecord:n,tabIndex:o,focus:i}=e;let r=n.record;return E.showCheckProcessStatusToolTip(r)}}}],editor:{supportViewMode:["table"],enable:!0}},toolbar:{actions:[...r.entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!b&&"RequestIE"==a,"Del")],filters:r.entity.DbEntityListConfigTool.TOOLBAR_FILTERS(!!i.searchParams)},footer:{page:{render:e=>{let t,n=e.uiRoot,{appContext:i,pageContext:E,documentSet:y}=n.props;return t=y?D.fI.getPluginByName(y.type):null,o().createElement(r.bs.Toolbar,{hide:!b},o().createElement(r.entity.WButtonEntityWrite,{appContext:i,pageContext:E,icon:s.A,label:(0,x.T)("Rename File"),onClick:this.onRename}),o().createElement(r.entity.WButtonEntityWrite,{appContext:i,pageContext:E,icon:s.A,label:(0,x.T)("Organize by HBL"),onClick:this.organizeByHBL}),o().createElement(r.bs.Popover,{title:"Document Classification",className:"flex-hbox-grow-0",closeOnTrigger:".btn",offset:[0,5]},o().createElement(r.bs.PopoverToggle,{laf:"primary"},o().createElement(l.A,{size:12})," ","Classify"),o().createElement(r.bs.PopoverContent,{className:"flex-vbox"},o().createElement(r.bs.Button,{className:"text-start p-2",laf:"primary",outline:!0,onClick:()=>this.onUpdateDocumentTypes(e,"invoice")},o().createElement(c.A,{size:12})," ",(0,x.T)("Invoice")),o().createElement(r.bs.Button,{className:"text-start p-2 my-1",laf:"primary",outline:!0,onClick:()=>this.onUpdateDocumentTypes(e,"tms-fcl-inv-summary")},o().createElement(c.A,{size:12})," ",(0,x.T)("FCL Inv Summary")),o().createElement(r.bs.Button,{className:"text-start p-2 my-1",laf:"primary",outline:!0,onClick:()=>this.onUpdateDocumentTypes(e,"tms-lcl-inv-summary")},o().createElement(c.A,{size:12})," ",(0,x.T)("LCL Inv Summary")))),o().createElement(r.bs.Popover,{title:"Document IE",className:"flex-hbox-grow-0",closeOnTrigger:".btn",offset:[0,5]},o().createElement(r.bs.PopoverToggle,{laf:"primary"},o().createElement(l.A,{size:12})," ","Document IE"),o().createElement(r.bs.PopoverContent,{className:"flex-vbox-grow-0",style:{minWidth:150}},o().createElement(r.bs.Button,{laf:"primary",outline:!0,className:"text-start p-2 my-1",onClick:n.onBatchRequestIE,hidden:"DocumentSet"!=a},o().createElement(p.A,{size:12})," ",(0,x.T)("Request IE")),o().createElement(r.bs.Button,{laf:"primary",outline:!0,className:"text-start p-2 my-1",onClick:n.onCancelIE},o().createElement(p.A,{size:12})," ",(0,x.T)("Cancel IE")),o().createElement(r.bs.Button,{laf:"primary",outline:!0,className:"text-start p-2 my-1",onClick:n.onProcessIE,hidden:!C||"RequestIE"!=a},o().createElement(d.A,{size:12})," ",(0,x.T)("Process IE")))),t?t.renderActionButton(i,E,n,a):o().createElement(o().Fragment,null),o().createElement(r.bs.Popover,{title:"Upload/Download",className:"flex-hbox-grow-0",closeOnTrigger:".btn",offset:[-10,5]},o().createElement(r.bs.PopoverToggle,{laf:"primary"},o().createElement(l.A,{size:12})," ","Transfer"),o().createElement(r.bs.PopoverContent,{className:"flex-vbox-grow-0",style:{minWidth:150}},o().createElement(r.bs.Button,{laf:"primary",outline:!0,className:"text-start p-2 my-1",onClick:n.onMergePdfDocuments},o().createElement(m.A,{size:12})," ",(0,x.T)("Merge PDFs")),o().createElement(r.bs.Button,{laf:"primary",outline:!0,className:"text-start p-2 my-1",onClick:n.onDownload},o().createElement(u.A,{size:12})," ",(0,x.T)("Download")),o().createElement(r.bs.Button,{laf:"primary",outline:!0,className:"text-start p-2 my-1",onClick:n.onShowUpload,hidden:!y},o().createElement(g.A,{size:12})," ",(0,x.T)("Upload")))),o().createElement(r.entity.WButtonEntityWrite,{appContext:i,pageContext:E,icon:h.A,label:(0,x.T)("Save"),onClick:this.onSave}))}}},view:{currentViewName:n||"table",availables:{table:{viewMode:"table"},aggregation:{viewMode:"aggregation",treeWidth:150,createAggregationModel(e){let t=new r.grid.AggregationDisplayModel("All",!1);return t.addAggregation(new r.grid.ValueAggregation((0,x.T)("Document Set"),"documentSetLabel",!0).withSortBucket("desc")),t}}}}};return f}organizeByHBL=()=>{let{appContext:e}=this.props,t=this.getVGridContext().model.getSelectedRecords();if(0===t.length)return void r.bs.notificationShow("warning",(0,x.T)("No records were selected"));for(let e of t)if("invoice"!=e.type)return void r.bs.notificationShow("danger","Error",(0,x.T)("Only invoice document can be organized by House Bill"));let a=t.map(e=>e.id);e.createHttpBackendCall("DocumentService","linkInvDocumentsToHouseBillDocSets",{docIds:a}).withSuccessNotification("success",(0,x.T)("Success")).call()};onSave=()=>{let{appContext:e,plugin:t}=this.props,a=t.getListModel().getModifiedRecords();e.createHttpBackendCall("DocumentService","bulkSaveDocuments",{docs:a}).withSuccessData(t=>{e.addOSNotification("success",(0,x.T)("Save Success")),this.reloadData()}).call()};onRename=()=>{let{appContext:e}=this.props,t=this.getVGridContext().model.getSelectedRecords();if(0===t.length)return void r.bs.notificationShow("warning",(0,x.T)("No records were selected"));for(let e of t){let t=e.missingField;if(t&&t.includes("-"))return void r.bs.notificationShow("danger",(0,x.T)("Error"),(0,x.T)(`Document ${e.name} has not been verified and therefore cannot be renamed.`))}let a=t.map(e=>e.id);e.createHttpBackendCall("DocumentService","renameDocuments",{docIds:a}).withSuccessData(e=>{let a=e;for(let e of t)for(let t of a)e.id===t.id&&(e.name=t.name,e.path=t.path);this.getVGridContext().getVGrid().forceUpdateView()}).withSuccessNotification("success",(0,x.T)("Success")).call()};onUpdateDocumentTypes(e,t){let a=e.uiRoot,{appContext:n,plugin:o}=a.props;if(!this._ensureSelectedRecords())return;let i=o.getListModel().getSelectedRecordIds();n.createHttpBackendCall("DocumentService","updateDocumentTypes",{docIds:i,docType:t}).withSuccessData(e=>{o.getListModel().getSelectedRecords().forEach(e=>e.type=t),this.getVGridContext().getVGrid().forceUpdateView()}).call()}onProcessIE=()=>{let{appContext:e,pageContext:t}=this.props;if(!this._ensureSelectedRecords())return;let a=this.getVGridContext().model.getSelectedRecordIds(),n=["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel","application/pdf"];for(let e of this.getVGridContext().model.getSelectedRecords())if(!n.includes(e.mediaType))return void r.bs.notificationShow("warning",(0,x.T)("Select xlsx only!"));e.createHttpBackendCall("DocumentService","processIE",{documentIds:a}).withSuccessData(e=>{this.nextViewId(),this.forceUpdate(),t.createPopupPage("ie-process-report",(0,x.T)("IE Process Report"),(t,a)=>o().createElement(T.k,{appContext:t,pageContext:a,report:e}),{size:"xl"})}).withSuccessNotification("success","Process Success").withFailNotification("danger","Process Failed").call()};onDownload=()=>{let{appContext:e,documentSet:t}=this.props,a=this.getVGridContext().model.getSelectedRecordIds();if(0===a.length)return void r.bs.notificationShow("warning",(0,x.T)("No records were selected"));let n={documentIds:a,keepFileName:!!t};e.createHttpBackendCall("DocumentService","downloadDocuments",n).withSuccessData(e=>{i.module.common.StoreInfo.privateDownload(e)}).withSuccessNotification("success","Request IE successfully").call()};onShowUpload=()=>{let{appContext:e,pageContext:t}=this.props,a=e.getServerContext().getRestClient(),n=this.onUpload;t.createPopupPage("upload",(0,x.T)("Upload"),(e,t)=>o().createElement("div",{className:"flex-vbox",style:{minHeight:450}},o().createElement(r.component.WUploadResourceList,{appContext:e,pageContext:t,multiple:!0,rest:a,onUpload:n})))};onUpload=e=>{let{appContext:t,documentSet:a,storage:n,createDocument:o,onModifyBean:i}=this.props,r={documentSetId:a.id,uploadResources:e};t.createHttpBackendCall("DocumentService","uploadDocuments",r).withSuccessData(e=>{i&&i(null)}).withSuccessNotification("info","Upload Successfully!").call()};onRenderLoading(){return o().createElement("div",{className:"flex-vbox"},o().createElement("div",{className:"mx-auto my-auto text-center"},o().createElement("div",{className:"spinner-border text-primary mb-3",role:"status",style:{width:"4rem",height:"4rem"}},o().createElement("span",{className:"visually-hidden"},"Loading...")),o().createElement("h5",{className:"text-muted"},"Processing your request..."),o().createElement("small",{className:"text-secondary"},"Please wait a moment ✨")))}renderDialogConfirmMessage(){return o().createElement("div",{className:"mb-3 d-flex align-items-start"},o().createElement("div",null,o().createElement("strong",null,"Warning:")," Verified data will be ",o().createElement("strong",null,"deleted"),". You will need to verify again."))}onCreateAccountingSummary=()=>{let{pageContext:e}=this.props;this._ensureSelectedRecords()&&e.createPopupPage("","",(e,t)=>{let a=this.getVGridContext().model.getSelectedRecordIds();return o().createElement(N,{appContext:e,pageContext:t,docIds:a,processMethod:"createAccountingChargeSummary",onConfirm:()=>{t.back(),this.nextViewId(),this.reloadData()}})},{backdrop:"static"})};onBatchRequestIE=()=>{let{pageContext:e}=this.props;this._ensureSelectedRecords()&&e.createPopupPage("","",(e,t)=>{let a=this.getVGridContext().model.getSelectedRecordIds();return o().createElement(N,{appContext:e,pageContext:t,docIds:a,processMethod:"batchRequestDocumentIE",onConfirm:()=>{t.back(),this.nextViewId(),this.reloadData()}})},{backdrop:"static"})};onRequestIE=()=>{let{appContext:e,pageContext:t}=this.props;if(!this._ensureSelectedRecords())return;let a=this.getVGridContext().model.getSelectedRecordIds();e.createHttpBackendCall("DocumentService","requestDocumentIE",{ids:a}).withSuccessData(e=>{this.nextViewId(),this.forceUpdate(),t.createPopupPage("ie-process-report",(0,x.T)("IE Process Report"),(t,a)=>o().createElement(T.k,{appContext:t,pageContext:a,report:e}),{size:"xl"})}).withSuccessNotification("success","Request IE successfully").withFailNotification("danger","Process Failed").call()};onCancelIE=()=>{let{appContext:e}=this.props;if(!this._ensureSelectedRecords())return;let t=this.getVGridContext().model.getSelectedRecordIds();e.createHttpBackendCall("DocumentService","cancelDocumentIE",{ids:t}).withSuccessData(e=>{this.nextViewId(),this.reloadData()}).withSuccessNotification("success","Request IE successfully").call()};onMergePdfDocuments=()=>{let{pageContext:e,documentSet:t}=this.props,a=this.vgridContext.model.getSelectedRecordIds();if(this._ensureSelectedRecords()){for(let e of this.vgridContext.model.getSelectedRecords())if("application/pdf"!==e.mediaType)return void r.bs.notificationShow("warning",(0,x.T)("Select PDF file only!"));e.createPopupPage("merged-pdf-documents",(0,x.T)("Merged PDF Documents"),(e,n)=>o().createElement(v.i,{appContext:e,pageContext:n,title:t?t.name:"Document",documentIds:a}),{size:"xl",zIndex:9999})}};_ensureSelectedRecords(){return 0!==this.vgridContext.model.getSelectedRecordIds().length||(r.bs.notificationShow("warning",(0,x.T)("No records were selected")),!1)}onDefaultSelect(e){let{appContext:t,pageContext:a,storage:n,documentSet:i}=this.props;t.createHttpBackendCall("DocumentService","getDocument",{id:e.record.id}).withSuccessData(t=>{a.addPageContent("document",(0,x.T)("Document"),(a,s)=>o().createElement(S.y,{appContext:a,pageContext:s,storage:n,documentSet:i,observer:new r.entity.BeanObserver(t),onPostCommit:t=>{e.record.hashtag="Record modified",this.getVGridContext().getVGrid().forceUpdateView()}}))}).call()}}class N extends r.app.AppComponent{renderLoading(){return o().createElement("div",{className:"flex-vbox"},o().createElement("div",{className:"mx-auto my-auto text-center"},o().createElement("div",{className:"spinner-border text-primary mb-3",role:"status",style:{width:"4rem",height:"4rem"}},o().createElement("span",{className:"visually-hidden"},"Loading...")),o().createElement("h5",{className:"text-muted"},"Processing your request..."),o().createElement("small",{className:"text-secondary"},"Please wait a moment ✨")))}onConfirm=()=>{let{appContext:e,docIds:t,processMethod:a,pageContext:n,onConfirm:o}=this.props;e.createHttpBackendCall("DocumentService",a,{ids:t}).withSuccessData(e=>{o&&o(e)}).withSuccessNotification("success","Request IE successfully").withFail(()=>{n.back()}).call(),this.markLoading(!0),this.forceUpdate()};onCancel=e=>{let{pageContext:t}=this.props;t.back()};render(){return this.isLoading()?this.renderLoading():o().createElement("div",{className:"flex-vbox"},o().createElement("div",{className:"modal-header"},o().createElement("h5",{className:"modal-title"},(0,x.T)("Confirm Request")),o().createElement("button",{className:"btn p-1",type:"button","data-bs-dismiss":"modal","aria-label":"Close"},o().createElement(b.A,{size:16}))),o().createElement("div",null,o().createElement("strong",null,"Warning:")," Verified data will be ",o().createElement("strong",null,"deleted"),". You will need to verify again."),o().createElement("div",{className:"flex-hbox-grow-0 justify-content-end my-2"},o().createElement(r.bs.Button,{laf:"secondary",outline:!0,className:"px-2 py-1 mx-1",onClick:this.onCancel},o().createElement(C.A,{size:12})," Cancel"),o().createElement(r.bs.Button,{laf:"secondary",outline:!0,className:"px-2 py-1 mx-1",onClick:this.onConfirm},o().createElement(E.A,{size:12})," OK")))}}},5813:(e,t,a)=>{a.d(t,{mC:()=>n}),a(5208);const n=new class{plugins={};getPluginMap(){return this.plugins}getPluginByName(e){return this.plugins[e]||null}register(e){let t=e.name;this.plugins[t]||(this.plugins[t]=e)}}},1980:(e,t,a)=>{a.d(t,{i:()=>s,y:()=>l});var n=a(5208),o=a.n(n),i=a(1529),r=a(1369);class s extends i.module.misc.UIPrintView{createPrintData(){throw new Error("Method not implemented.")}createLoadPrintUrl(e,t){throw new Error("Method not implemented.")}loadPrint=(e,t,a,n)=>{let{appContext:o,documentIds:i}=this.props;o.createHttpBackendCall("DocumentService","mergePdfDocuments",{id:i}).withSuccessData(e=>{let t=e,a=r.app.host.CONFIG.createServerLink(`/get/private/store/${t.storeId}`);n("success",a,"Load Report Success")}).call()};createPrintConfigs(){let{title:e}=this.props;return[{name:"document-print",label:e}]}}class l extends r.app.AppComponent{documentIds=[];createSearchParams(e){return{params:{documentSetId:e}}}componentDidMount(){let{appContext:e,documentSetId:t,type:a}=this.props;e.createHttpBackendCall("DocumentService","searchDocuments",{sqlParams:this.createSearchParams(t)}).withSuccessData(e=>{e.forEach(e=>{"application/pdf"===e.mediaType&&(a||this.documentIds.push(e.id),e.type==a&&this.documentIds.push(e.id))}),this.forceUpdate()}).call()}render(){let{appContext:e,pageContext:t,title:a}=this.props;return 0===this.documentIds.length?o().createElement("h3",null,a," not available..."):o().createElement(s,{title:a,appContext:e,pageContext:t,documentIds:this.documentIds})}}},5682:(e,t,a)=>{a.d(t,{C:()=>p,R:()=>c});var n=a(5208),o=a.n(n),i=a(1369),r=a(523),s=a(2036),l=a(6095);class c extends i.entity.AppDbEntityEditor{render(){let{observer:e,readOnly:t,appContext:a,pageContext:n}=this.props,s=e.getMutableBean(),c=n.hasUserWriteCapability();return o().createElement("div",{className:"flex-vbox"},o().createElement("div",{className:"flex-vbox p-1"},o().createElement("h5",null,"Info"),o().createElement("div",null,o().createElement(i.input.BBStringField,{bean:s,field:"name",label:(0,r.T)("Name"),inputObserver:e,disable:!c}),o().createElement(i.input.BBStringField,{bean:s,field:"path",label:(0,r.T)("Path"),inputObserver:e,disable:!0}),o().createElement(i.input.BBSelectField,{disable:!c,label:"Type",bean:s,field:"type",options:["tms-inv-summary","inv-summary","other"],optionLabels:["TMS Invoice Summary","Invoice Summary","Other"]}),o().createElement("div",{className:"flex-hbox-grow-0 align-items-center"},o().createElement("label",{className:"form-label"},(0,r.T)("Tags")),o().createElement(l.$,{appContext:a,pageContext:n,labelField:"name",labelBeans:s.tags?s.tags:[]})),o().createElement(i.input.BBTextField,{bean:s,field:"description",label:(0,r.T)("Description"),inputObserver:e,disable:!c}))),o().createElement(i.bs.Toolbar,{hide:!c||t},o().createElement(i.entity.ButtonEntityCommit,{appContext:a,pageContext:n,observer:e,commit:{entityLabel:"DocumentSet",context:"company",service:"DocumentService",commitMethod:"saveDocumentSet"},onPostCommit:this.onPostCommit})))}}class p extends i.entity.AppDbEntityEditor{onModifyBean=(e,t)=>{this.nextViewId(),this.forceUpdate()};render(){let{appContext:e,pageContext:t,observer:a,storage:n}=this.props,r=a.getMutableBean();const l=t.hasUserWriteCapability();return o().createElement("div",{className:"flex-vbox"},o().createElement(i.bs.VSplit,null,o().createElement(i.bs.VSplitPane,{className:"pr-1",width:300},o().createElement(c,{appContext:e,pageContext:t,observer:a,readOnly:!l})),o().createElement(i.bs.VSplitPane,null,o().createElement("h5",null,"Documents"),o().createElement(s.g,{key:`${this.viewId}-documents`,appContext:e,pageContext:t,documentSet:r,viewType:"DocumentSet",plugin:(new s.$).withDocumentSet(r.id),storage:n,onModifyBean:this.onModifyBean}))))}}},9242:(e,t,a)=>{a.d(t,{t:()=>l,u:()=>c});var n=a(5208),o=a.n(n),i=a(1369),r=a(523),s=a(5682);class l extends i.entity.DbEntityListPlugin{categoryId;constructor(){super([]),this.backend={context:"company",service:"DocumentService",searchMethod:"searchDocumentSets",deleteMethod:"deleteDocumentSets"},this.searchParams={filters:[...i.sql.createSearchFilter()],optionFilters:[i.sql.createStorageStateFilter([i.entity.StorageState.ACTIVE,i.entity.StorageState.ARCHIVED])],orderBy:{fields:["modifiedTime"],fieldLabels:["Modified Time"],selectFields:["modifiedTime"],sort:"DESC"},maxReturn:1e3}}loadData(e){let t={params:this.searchParams};this.createBackendSearch(e,t).call()}withScope(e){return this.addSearchParam("scope",e),this}withCategory(e){return this.categoryId=e,this.addSearchParam("documentCategoryId",e),this}backendDelete(e,t){let a={docSetIds:t};this.createBackendDelete(e,a,t).call()}}class c extends i.entity.DbEntityList{createVGridConfig(){let{pageContext:e}=this.props,t=e.hasUserWriteCapability(),a=e.hasUserModeratorCapability();return{title:(0,r.T)("Document Sets"),record:{fields:[...i.entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),i.entity.DbEntityListConfigTool.FIELD_INDEX(),i.entity.DbEntityListConfigTool.FIELD_ON_SELECT("name",(0,r.T)("Name"),250),{name:"path",label:(0,r.T)("Path"),width:200,state:{visible:!1}},{name:"type",label:(0,r.T)("Type"),width:150,filterable:!0,filterableType:"Options",fieldDataGetter:e=>({"tms-inv-summary":"Tms Inv Summary","inv-summary":"Inv Summary",other:"Other"}[e.type])},{name:"tags",label:(0,r.T)("Tags"),width:100,filterable:!0,filterableType:"Options"},{name:"description",label:(0,r.T)("Description"),width:200},...i.entity.DbEntityListConfigTool.FIELD_ENTITY]},toolbar:{actions:[...i.entity.DbEntityListConfigTool.TOOLBAR_ACTION_ADD(!t,"Add"),...i.entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!a,"Del")],filters:i.entity.DbEntityListConfigTool.TOOLBAR_FILTERS(!1)},footer:{},view:{currentViewName:"table",availables:{table:{viewMode:"table"},aggregation:{viewMode:"aggregation",treeWidth:150,createAggregationModel(e){let t=new i.grid.AggregationDisplayModel("All",!1);return t.addAggregation(new i.grid.ValueAggregation((0,r.T)("Document Set Type"),"type",!0)),t}}}}}}onAddDocumentSet=(e,t)=>{let{plugin:a}=this.props;t&&t.props.pageContext.back({reload:!0}),a.getListModel().addRecord(e),this.getVGridContext().getVGrid().forceUpdateView(!0)};onDefaultSelect(e){let{appContext:t,pageContext:a,storage:n}=this.props;t.createHttpBackendCall("DocumentService","getDocumentSet",{id:e.record.id}).withSuccessData(e=>{a.addPageContent("document-set",(0,r.T)("Document Set"),(t,a)=>o().createElement(s.C,{appContext:t,pageContext:a,storage:n,observer:new i.entity.BeanObserver(e)}))}).call()}onNewAction=()=>{let{pageContext:e,plugin:t,storage:a}=this.props;e.createPopupPage("document-set",(0,r.T)("Document Set"),(e,t)=>o().createElement(s.R,{appContext:e,pageContext:t,observer:new i.entity.BeanObserver({}),onPostCommit:this.onAddDocumentSet}),{backdrop:"static"})};filterByCategory(e){let{plugin:t}=this.props,a=e?e.id:null;t.withCategory(a),this.reloadData()}}},5343:(e,t,a)=>{a.d(t,{fI:()=>n}),a(5208);const n=new class{plugins={};getPluginMap(){return this.plugins}getPluginByName(e){return this.plugins[e]||null}register(e){let t=e.name;this.plugins[t]||(this.plugins[t]=e)}}},9931:(e,t,a)=>{a.d(t,{t:()=>l});var n=a(5208),o=a.n(n),i=a(523),r=a(1369);class s extends r.entity.AppDbEntity{render(){let{observer:e}=this.props,t=e.getMutableBean();return o().createElement("div",{className:"container-fluid"},o().createElement(r.input.BBStringField,{bean:t,field:"name",label:(0,i.T)("Name"),required:!0,inputObserver:e}),o().createElement(r.input.BBStringField,{bean:t,field:"label",label:(0,i.T)("Label")}),o().createElement(r.input.BBStringField,{bean:t,field:"description",label:(0,i.T)("Description")}))}}class l extends r.entity.AppDbEntityEditor{render(){let{observer:e,appContext:t,pageContext:a}=this.props,n=a.hasUserWriteCapability();return o().createElement("div",{className:"flex-vbox-grow-0"},o().createElement(s,{observer:e,appContext:t,pageContext:a,readOnly:!n}),o().createElement(r.bs.Toolbar,{className:"border"},o().createElement(r.entity.ButtonEntityCommit,{appContext:t,pageContext:a,readOnly:!n,observer:e,commit:{entityLabel:"Tag",context:"company",service:"DocumentService",commitMethod:"saveDocumentSetTag"},onPostCommit:e=>this.onPostCommit(e)}),o().createElement(r.entity.WButtonEntityReset,{appContext:t,pageContext:a,readOnly:!n,observer:e,onPostRollback:this.onPostRollback})))}}},4472:(e,t,a)=>{a.d(t,{K:()=>c,n:()=>l});var n=a(5208),o=a.n(n),i=a(523),r=a(1369),s=a(9931);class l extends r.entity.DbEntityListPlugin{constructor(){super([]),this.backend={context:"company",service:"DocumentService",searchMethod:"searchDocumentSetTags"},this.searchParams={filters:[...r.sql.createSearchFilter()],optionFilters:[r.sql.createStorageStateFilter([r.entity.StorageState.ACTIVE,r.entity.StorageState.ARCHIVED])],orderBy:{fields:["modifiedTime"],fieldLabels:["Modified Time"],selectFields:[],sort:"DESC"},maxReturn:1e3}}loadData(e){this.createBackendSearch(e,{params:this.searchParams}).call()}}class c extends r.entity.DbEntityList{createVGridConfig(){const{appContext:e,pageContext:t,type:a}=this.props;return{record:{fields:[...r.entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),r.entity.DbEntityListConfigTool.FIELD_INDEX(),r.entity.DbEntityListConfigTool.FIELD_ON_SELECT("label",(0,i.T)("Label"),200),{name:"name",label:(0,i.T)("Name"),width:250},...r.entity.DbEntityListConfigTool.FIELD_ENTITY]},view:{currentViewName:"table",availables:{table:{viewMode:"table"}}},toolbar:{filters:[...r.entity.DbEntityListConfigTool.TOOLBAR_FILTERS(!0)]},footer:{page:{hide:"page"!==a,render:a=>o().createElement(r.bs.Toolbar,{className:"border"},o().createElement(r.entity.WButtonEntityNew,{appContext:e,pageContext:t,hide:!1,label:(0,i.T)("New Set Flag"),onClick:()=>this.onNew()}))},selector:{hide:"selector"!==a,render:e=>o().createElement(r.bs.Toolbar,{className:"border"},o().createElement(r.bs.Button,{laf:"primary",onClick:()=>this.onMultiSelect()}," ",(0,i.T)("Select")," "))}}}}onNew(){let{pageContext:e,readOnly:t}=this.props,a=(e,t)=>{t?.props.pageContext.back()};e.createPopupPage("new-doc-set-tag",(0,i.T)("New DocSet Tag"),(e,n)=>o().createElement(s.t,{appContext:e,pageContext:n,observer:new r.entity.ComplexBeanObserver({entityState:"ACTIVE"}),readOnly:t,onPostCommit:a}),{size:"md",backdrop:"static"})}onDefaultSelect(e){let{appContext:t,pageContext:a,readOnly:n}=this.props;t.createBackendCall("DocumentService","getDocumentSetTag",{id:e.record.id}).withSuccessData(t=>{let l=(e,t)=>{t?.props.pageContext.back()};a.createPopupPage("edit-doc-set-tag",(0,i.T)("Edit {{type}} Tag",{type:e.record.name}),(e,a)=>o().createElement(s.t,{appContext:e,pageContext:a,observer:new r.entity.BeanObserver(t),readOnly:n,onPostCommit:l}),{size:"md",backdrop:"static"})}).call()}}},2528:(e,t,a)=>{a.d(t,{k:()=>c});var n=a(5208),o=a.n(n),i=a(1369),r=a(523),s=a(6238);function l(e){let t=[];for(let a in e.statistics){let e={name:a,label:a,width:100,hint:a.split(":")[1]};t.push(e)}return t}class c extends i.app.AppComponent{ctx;constructor(e){super(e),this.markLoading(!0)}renderVGrid(e){let{report:t}=this.props,a=this.getReportData(e),n=new i.grid.ListModel(a),s=new i.grid.VGridContext(this);return s.init(function(e,t){let a=e.iereporters[t][0];return{record:{fields:[...i.entity.DbEntityListConfigTool.FIELD_SELECTOR(!0),i.entity.DbEntityListConfigTool.FIELD_INDEX(),{name:"name",label:(0,r.T)("Name"),width:250},...l(a)]},toolbar:{actions:[]},view:{currentViewName:"table",availables:{table:{viewMode:"table"}}}}}(t,e),n),o().createElement(i.grid.VGrid,{context:s})}createTabPanelConfig(e){if(e.iereporters){let t=[];for(let a in e.iereporters)t.includes(a)||t.push(a);return{tabs:t.map((e,t)=>({name:e,label:e,active:0==t,Icon:s.A,renderContent:a=>o().createElement("div",{className:"flex-vbox p-1",key:`${e}-${t}`},this.renderVGrid(e))}))}}}getReportData(e){let{report:t}=this.props,a=[];for(let n of t.iereporters[e]){let e=Object.fromEntries(Object.entries(n.statistics).map(([e,t])=>[e,t.count]));a.push({name:n.name,...e})}return a}componentDidMount(){let{report:e}=this.props;console.log(e),this.markLoading(!1),this.forceUpdate()}render(){let{report:e}=this.props;return this.isLoading()?this.renderLoading():o().createElement(i.bs.DefaultTabPane,{style:{height:600},config:this.createTabPanelConfig(e)})}}},6095:(e,t,a)=>{a.d(t,{$:()=>s});var n=a(5208),o=a.n(n),i=a(1369),r=a(4472);class s extends i.input.BBMultiLabelSelector{onMultiSelect(e){for(let t=0;t<e.length;t++){let{labelBeans:a}=this.props;for(let t=0;t<a.length;t++)if(a[t].id==e[t].id)return void this.forceUpdate();a.push(e[t])}this.dialogClose(),this.forceUpdate()}onCustomSelect(){let{appContext:e,pageContext:t,labelBeans:a}=this.props,n=o().createElement(r.K,{plugin:(new r.n).withExcludeRecords(a),appContext:e,pageContext:t,type:"selector",onSelect:(e,t,a)=>this.onMultiSelect([a]),readOnly:!0,onMultiSelect:(e,t,a)=>this.onMultiSelect(a)});this.dialogShow("Select Document Set Tag","lg",n)}}},262:(e,t,a)=>{a.d(t,{P:()=>l});var n=a(5208),o=a.n(n),i=a(1369),r=a(1529);class s extends i.app.AppComponent{data;params={};constructor(e){super(e);const{response:t}=e;this.data=t.result}onResource=e=>{const{response:t,appContext:a}=this.props;let n=a.getServerContext().getRestClient(),o={resourceName:e,...this.params},i={"DataTP-Authorization":t.authorization};n.postWithHeader("/resource",i,o,e=>{let t=e.data;this.data=t.result,this.forceUpdate()})};render(){return o().createElement("div",{className:"flex-vbox border",style:{height:"calc(100vh - 20px)"}},o().createElement("div",null,o().createElement(i.input.BBStringField,{bean:this.params,field:"search",label:"Search"})),o().createElement("h4",null,"Implement Screen"),o().createElement("div",{className:"flex-vbox"},o().createElement("div",null,"Raw Data"),o().createElement(i.component.LazyJsonView,{object:this.data})),o().createElement(i.bs.Toolbar,null,o().createElement(i.bs.Button,{laf:"primary",onClick:()=>this.onResource()},"Execute")))}}class l extends r.module.security.UIApiPlugin{constructor(){super("resource:custom-ie-api","custom-ie-api")}render(e,t,a){return o().createElement(s,{appContext:e,pageContext:t,response:a})}}i.app.AppComponent},523:(e,t,a)=>{a.d(t,{T:()=>n});const n=a(1369).i18n.getT(["document"])},4193:(e,t,a)=>{a.r(t),a.d(t,{init:()=>b});var n=a(5208),o=a.n(n),i=a(8667),r=a(3437),s=a(1369),l=a(1529),c=a(523),p=a(9242),d=a(2036),m=a(262),u=a(4472),g=s.app.space;class h extends g.SpacePlugin{constructor(){super("document","Document Navigation")}createUserScreens(){let e=s.app.host.DATATP_HOST.session.getAccountAcl().getCompanyAcl().companyCode;return[{id:"document-ie",label:(0,c.T)("Document IE"),icon:i.A,checkPermission:{feature:{module:"user",name:"my-space"},requiredCapability:s.app.READ},renderUI:(t,a)=>{let n={tabs:[{name:"document-set",label:"Document Set",active:!0,Icon:i.A,renderContent:n=>o().createElement(p.u,{appContext:t,pageContext:a,storage:new l.module.storage.CompanyStorage(e),plugin:(new p.t).withScope(s.app.AppDataScope.OWNER.scope)})},{name:"documents",label:"Documents",Icon:r.A,renderContent:n=>o().createElement(d.g,{appContext:t,pageContext:a,viewType:"DocumentSet",viewName:"aggregation",storage:new l.module.storage.CompanyStorage(e),plugin:(new d.$).withScope(s.app.AppDataScope.OWNER.scope)})}]};return o().createElement(s.bs.DefaultTabPane,{config:n})},screens:[]}]}createCompanyScreens(){let e=s.app.host.DATATP_HOST.session.getAccountAcl().getCompanyAcl().companyCode;return[{id:"document-ie",label:(0,c.T)("Document IE"),icon:i.A,checkPermission:{feature:{module:"document",name:"company-document"},requiredCapability:s.app.READ},renderUI:(t,a)=>{let n={tabs:[{name:"document-set",label:"Document Set",active:!0,Icon:i.A,renderContent:n=>o().createElement(p.u,{appContext:t,pageContext:a,storage:new l.module.storage.CompanyStorage(e),plugin:new p.t})},{name:"documents",label:"Documents",Icon:r.A,renderContent:n=>o().createElement(d.g,{key:"documents",appContext:t,pageContext:a,viewType:"DocumentSet",viewName:"aggregation",storage:new l.module.storage.CompanyStorage(e),plugin:new d.$})},{name:"document-set-tag",label:(0,c.T)("Document Set Tag"),renderContent:e=>o().createElement(u.K,{appContext:t,pageContext:a,type:"page",plugin:new u.n})}]};return o().createElement(s.bs.DefaultTabPane,{config:n})},screens:[]}]}}function b(){g.SpacePluginManager.register(new h),l.module.security.UIApiPluginManager.register(new m.P)}},5161:(e,t,a)=>{a.d(t,{P:()=>o,n:()=>n});const n=["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.ms-powerpoint"],o=["image/jpeg","image/png","image/gif","image/svg+xml","image/webp","application/pdf","application/json","application/xml"]}}]);
import { app, input } from '@datatp-ui/lib';
interface BBDocumentSetTagSelectorProps extends input.BBMultiLabelSelectorProps {
    appContext: app.AppContext;
    pageContext: app.PageContext;
}
export declare class BBDocumentSetTagSelector extends input.BBMultiLabelSelector<BBDocumentSetTagSelectorProps> {
    onMultiSelect(flags: Array<any>): void;
    onCustomSelect(): void;
}
export {};
//# sourceMappingURL=WDocumentSetTag.d.ts.map
import { ReactElement } from 'react';
import { entity, grid } from '@datatp-ui/lib';
interface DocAccountingChargeSummaryItemListEditorProps extends entity.VGridEntityListEditorProps {
    documentType: string;
}
export declare class DocAccountingChargeSummaryItemListEditor extends entity.VGridEntityListEditor<DocAccountingChargeSummaryItemListEditorProps> {
    onShowInvoice: (item: any) => void;
    showCheckMissingStatusToolTip(record: any): ReactElement;
    createVGridConfig(): grid.VGridConfig;
}
export {};
//# sourceMappingURL=DocAccountingChargeSummaryItemListEditor.d.ts.map
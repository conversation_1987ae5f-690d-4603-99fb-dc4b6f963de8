import { entity } from "@datatp-ui/lib";
interface BBRefNameFeeDescProps extends entity.BBRefEntityProps {
    type?: 'BUYING' | 'SELLING';
    groups?: ('FREIGHT' | 'LCCHARGE' | 'CUS' | 'OTHER' | 'TRUCK')[];
    beanIdField: string;
    beanLabelField: string;
}
export declare class BBRefNameFeeDesc extends entity.BBRefEntity<BBRefNameFeeDescProps> {
    protected createPlugin(): entity.BBRefEntityPlugin;
}
export {};
//# sourceMappingURL=BBRefNameFeeDesc.d.ts.map
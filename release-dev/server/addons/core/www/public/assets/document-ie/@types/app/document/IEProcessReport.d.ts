import React from 'react';
import { app, bs, grid } from '@datatp-ui/lib';
interface IEProcessReportProps extends app.AppComponentProps {
    report: any;
}
export declare class IEProcessReport extends app.AppComponent<IEProcessReportProps> {
    ctx: grid.VGridContext;
    constructor(props: IEProcessReportProps);
    renderVGrid(type: string): React.JSX.Element;
    createTabPanelConfig(report: any): bs.TabPaneConfig | undefined;
    getReportData(type: string): {
        name: any;
    }[];
    componentDidMount(): void;
    render(): React.JSX.Element;
}
export {};
//# sourceMappingURL=IEProcessReport.d.ts.map
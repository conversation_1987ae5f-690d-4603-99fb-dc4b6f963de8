!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("datatp_ui_document_ie",[],t):"object"==typeof exports?exports.datatp_ui_document_ie=t():e.datatp_ui_document_ie=t()}(self,(()=>(()=>{var e,t,r,n,o,a,u,i,l,d,f,s,p,c,h,m,v={825:()=>{console.log("Init Document IE")}},g={};function b(e){var t=g[e];if(void 0!==t)return t.exports;var r=g[e]={id:e,loaded:!1,exports:{}};return v[e].call(r.exports,r,r.exports,b),r.loaded=!0,r.exports}return b.m=v,b.c=g,b.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return b.d(t,{a:t}),t},b.d=(e,t)=>{for(var r in t)b.o(t,r)&&!b.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},b.f={},b.e=e=>Promise.all(Object.keys(b.f).reduce(((t,r)=>(b.f[r](e,t),t)),[])),b.u=e=>e+".js",b.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),b.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),e={},t="datatp_ui_document_ie:",b.l=(r,n,o,a)=>{if(e[r])e[r].push(n);else{var u,i;if(void 0!==o)for(var l=document.getElementsByTagName("script"),d=0;d<l.length;d++){var f=l[d];if(f.getAttribute("src")==r||f.getAttribute("data-webpack")==t+o){u=f;break}}u||(i=!0,(u=document.createElement("script")).charset="utf-8",u.timeout=120,b.nc&&u.setAttribute("nonce",b.nc),u.setAttribute("data-webpack",t+o),u.src=r),e[r]=[n];var s=(t,n)=>{u.onerror=u.onload=null,clearTimeout(p);var o=e[r];if(delete e[r],u.parentNode&&u.parentNode.removeChild(u),o&&o.forEach((e=>e(n))),t)return t(n)},p=setTimeout(s.bind(null,void 0,{type:"timeout",target:u}),12e4);u.onerror=s.bind(null,u.onerror),u.onload=s.bind(null,u.onload),i&&document.head.appendChild(u)}},b.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},b.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{b.S={};var e={},t={};b.I=(r,n)=>{n||(n=[]);var o=t[r];if(o||(o=t[r]={}),!(n.indexOf(o)>=0)){if(n.push(o),e[r])return e[r];b.o(b.S,r)||(b.S[r]={});var a=b.S[r],u="datatp_ui_document_ie",i=(e,t,r,n)=>{var o=a[e]=a[e]||{},i=o[t];(!i||!i.loaded&&(!n!=!i.eager?n:u>i.from))&&(o[t]={get:r,from:u,eager:!!n})},l=[];return"default"===r&&(i("@datatp-ui/lib","1.0.0",(()=>Promise.all([b.e(669),b.e(465)]).then((()=>()=>b(3669))))),i("react-dom","18.3.1",(()=>Promise.all([b.e(143),b.e(208)]).then((()=>()=>b(8143))))),i("react","18.3.1",(()=>b.e(758).then((()=>()=>b(758)))))),e[r]=l.length?Promise.all(l).then((()=>e[r]=1)):1}}})(),b.p="/assets/document-ie/",r=e=>{var t=e=>e.split(".").map((e=>+e==e?+e:e)),r=/^([^-+]+)?(?:-([^+]+))?(?:\+(.+))?$/.exec(e),n=r[1]?t(r[1]):[];return r[2]&&(n.length++,n.push.apply(n,t(r[2]))),r[3]&&(n.push([]),n.push.apply(n,t(r[3]))),n},n=(e,t)=>{e=r(e),t=r(t);for(var n=0;;){if(n>=e.length)return n<t.length&&"u"!=(typeof t[n])[0];var o=e[n],a=(typeof o)[0];if(n>=t.length)return"u"==a;var u=t[n],i=(typeof u)[0];if(a!=i)return"o"==a&&"n"==i||"s"==i||"u"==a;if("o"!=a&&"u"!=a&&o!=u)return o<u;n++}},o=e=>{var t=e[0],r="";if(1===e.length)return"*";if(t+.5){r+=0==t?">=":-1==t?"<":1==t?"^":2==t?"~":t>0?"=":"!=";for(var n=1,a=1;a<e.length;a++)n--,r+="u"==(typeof(i=e[a]))[0]?"-":(n>0?".":"")+(n=2,i);return r}var u=[];for(a=1;a<e.length;a++){var i=e[a];u.push(0===i?"not("+l()+")":1===i?"("+l()+" || "+l()+")":2===i?u.pop()+" "+u.pop():o(i))}return l();function l(){return u.pop().replace(/^\((.+)\)$/,"$1")}},a=(e,t)=>{if(0 in e){t=r(t);var n=e[0],o=n<0;o&&(n=-n-1);for(var u=0,i=1,l=!0;;i++,u++){var d,f,s=i<e.length?(typeof e[i])[0]:"";if(u>=t.length||"o"==(f=(typeof(d=t[u]))[0]))return!l||("u"==s?i>n&&!o:""==s!=o);if("u"==f){if(!l||"u"!=s)return!1}else if(l)if(s==f)if(i<=n){if(d!=e[i])return!1}else{if(o?d>e[i]:d<e[i])return!1;d!=e[i]&&(l=!1)}else if("s"!=s&&"n"!=s){if(o||i<=n)return!1;l=!1,i--}else{if(i<=n||f<s!=o)return!1;l=!1}else"s"!=s&&"n"!=s&&(l=!1,i--)}}var p=[],c=p.pop.bind(p);for(u=1;u<e.length;u++){var h=e[u];p.push(1==h?c()|c():2==h?c()&c():h?a(h,t):!c())}return!!c()},u=(e,t)=>{var r=e[t];return Object.keys(r).reduce(((e,t)=>!e||!r[e].loaded&&n(e,t)?t:e),0)},i=(e,t,r,n)=>"Unsatisfied version "+r+" from "+(r&&e[t][r].from)+" of shared singleton module "+t+" (required "+o(n)+")",l=(e,t,r,n)=>{var o=u(e,r);return a(n,o)||d(i(e,r,o,n)),f(e[r][o])},d=e=>{"undefined"!=typeof console&&console.warn&&console.warn(e)},f=e=>(e.loaded=1,e.get()),s=(e=>function(t,r,n,o){var a=b.I(t);return a&&a.then?a.then(e.bind(e,t,b.S[t],r,n,o)):e(0,b.S[t],r,n,o)})(((e,t,r,n,o)=>t&&b.o(t,r)?l(t,0,r,n):o())),p={},c={857:()=>s("default","react-dom",[4,18,3,1],(()=>Promise.all([b.e(143),b.e(208)]).then((()=>()=>b(8143))))),973:()=>s("default","react",[,[1,19,0,0],[1,18,0,0],[1,17,0,0],[1,16,8,0],1,1,1],(()=>b.e(758).then((()=>()=>b(758))))),5045:()=>s("default","react",[,[1,19,0,0],[1,18,0,0],[1,17,0,0],[1,16,0,0],1,1,1],(()=>b.e(758).then((()=>()=>b(758))))),6297:()=>s("default","react",[4,18,3,1],(()=>b.e(758).then((()=>()=>b(758))))),9329:()=>s("default","react",[,[1,19,0,0],[1,18,0,0],[1,17,0,0],[1,16,0,0],[1,15,0,0],1,1,1,1],(()=>b.e(758).then((()=>()=>b(758))))),5208:()=>s("default","react",[1,18,3,1],(()=>b.e(758).then((()=>()=>b(758)))))},h={208:[5208],465:[857,973,5045,6297,9329]},m={},b.f.consumes=(e,t)=>{b.o(h,e)&&h[e].forEach((e=>{if(b.o(p,e))return t.push(p[e]);if(!m[e]){var r=t=>{p[e]=0,b.m[e]=r=>{delete b.c[e],r.exports=t()}};m[e]=!0;var n=t=>{delete p[e],b.m[e]=r=>{throw delete b.c[e],t}};try{var o=c[e]();o.then?t.push(p[e]=o.then(r).catch(n)):r(o)}catch(e){n(e)}}}))},(()=>{var e={792:0};b.f.j=(t,r)=>{var n=b.o(e,t)?e[t]:void 0;if(0!==n)if(n)r.push(n[2]);else if(/^(208|465)$/.test(t))e[t]=0;else{var o=new Promise(((r,o)=>n=e[t]=[r,o]));r.push(n[2]=o);var a=b.p+b.u(t),u=new Error;b.l(a,(r=>{if(b.o(e,t)&&(0!==(n=e[t])&&(e[t]=void 0),n)){var o=r&&("load"===r.type?"missing":r.type),a=r&&r.target&&r.target.src;u.message="Loading chunk "+t+" failed.\n("+o+": "+a+")",u.name="ChunkLoadError",u.type=o,u.request=a,n[1](u)}}),"chunk-"+t,t)}};var t=(t,r)=>{var n,o,[a,u,i]=r,l=0;if(a.some((t=>0!==e[t]))){for(n in u)b.o(u,n)&&(b.m[n]=u[n]);i&&i(b)}for(t&&t(r);l<a.length;l++)o=a[l],b.o(e,o)&&e[o]&&e[o][0](),e[o]=0},r=self.webpackChunkdatatp_ui_document_ie=self.webpackChunkdatatp_ui_document_ie||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),b(825)})()));
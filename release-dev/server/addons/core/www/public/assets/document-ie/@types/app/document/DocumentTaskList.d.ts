import { grid, app, entity } from '@datatp-ui/lib';
export declare class DocumentTaskListPlugin extends entity.DbEntityListPlugin {
    constructor();
    loadData(uiList: entity.DbEntityList<any>): void;
    backendDelete(uiList: entity.DbEntityList<entity.DbEntityListProps>, targetIds: Array<number>): void;
}
export declare class DocumentTaskList extends entity.DbEntityList<entity.DbEntityListProps> {
    createVGridConfig(): grid.VGridConfig;
    onAddDocument: (entity: any, uiEditor?: app.AppComponent) => void;
    onDefaultSelect(dRecord: grid.DisplayRecord): void;
    onNewAction: () => void;
}
//# sourceMappingURL=DocumentTaskList.d.ts.map
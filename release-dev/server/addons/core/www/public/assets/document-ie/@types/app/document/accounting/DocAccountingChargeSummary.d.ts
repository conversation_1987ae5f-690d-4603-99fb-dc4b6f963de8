import React from 'react';
import { entity, app } from '@datatp-ui/lib';
import { UIDocumentPlugin } from "../DocumentPlugin";
interface DocAccountingChargeSummaryEditorProps extends entity.AppComplexEntityEditorProps {
    doc: any;
    documentSet: any;
}
export declare class DocAccountingChargeSummaryEditor extends entity.AppDbComplexEntityEditor<DocAccountingChargeSummaryEditorProps> {
    toAccountingTemplate: () => void;
    updateAccountingTemplate: () => void;
    onSave: () => void;
    render(): React.JSX.Element;
}
interface DocAccountingChargeSummaryLoadableProps extends app.AppComponentProps {
    data: any;
    onPostCommit?(entity: any): void;
}
export declare class DocAccountingChargeSummaryLoadable extends app.AppComponent<DocAccountingChargeSummaryLoadableProps> {
    entity: any;
    componentDidMount(): void;
    onPostCommit: (entity: any) => void;
    renderEntity(_entity: any): React.ReactNode;
    render(): React.ReactNode;
}
export declare class DocAccountingChargeSummaryPlugin extends UIDocumentPlugin {
    constructor();
    hasPreviewTab(): boolean;
    render(_appContext: app.AppContext, _pageContext: app.PageContext, data: any, onCommit: (bean: any) => void): React.JSX.Element;
}
export {};
//# sourceMappingURL=DocAccountingChargeSummary.d.ts.map
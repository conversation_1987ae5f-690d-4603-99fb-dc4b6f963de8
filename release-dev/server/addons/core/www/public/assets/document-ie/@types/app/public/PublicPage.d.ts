/// <reference types="react" />
import { Icon } from 'react-feather';
import { app } from '@datatp-ui/lib';
export interface PageConfig {
    id: string;
    label: string;
    icon?: Icon;
    renderUI: (appContext: app.AppContext, pageContext: app.PageContext) => JSX.Element;
}
export declare class PublicPageManager {
    static pageConfigs: Record<string, PageConfig>;
    static getPageConfigMap(): Record<string, PageConfig>;
    static register(pageConfig: PageConfig): void;
    static getPageConfig(id: string): PageConfig;
}
//# sourceMappingURL=PublicPage.d.ts.map
import React, { Component } from "react";
interface ForgotPasswordState {
    model: any;
    loading: boolean;
}
export declare class UIForgotPassword extends Component<{}, ForgotPasswordState> {
    constructor(props: {});
    setLoading: (loading: boolean) => void;
    onResetPassword(): void;
    onUpdateNewPassword: () => void;
    onHide: () => void;
    renderEmailStep(): React.JSX.Element;
    renderPasswordStep(): React.JSX.Element;
    render(): React.JSX.Element;
}
export {};
//# sourceMappingURL=forgot.d.ts.map
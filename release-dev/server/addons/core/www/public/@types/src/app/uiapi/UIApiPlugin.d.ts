import { app } from "@datatp-ui/lib";
import * as erp from "@datatp-ui/erp";
interface UIApiPluginProps extends app.AppComponentProps {
    token: string;
}
export declare class UIApiPlugin extends app.AppComponent<UIApiPluginProps> {
    apiResponse: erp.module.security.ApiResponse;
    componentDidMount(): void;
    componentWillUnmount(): void;
    _updatePageTitle(title: string): void;
    render(): import("react").JSX.Element;
}
export {};
//# sourceMappingURL=UIApiPlugin.d.ts.map
{"version": 3, "file": "HNavbarSpaceSubmenu.d.ts", "sourceRoot": "", "sources": ["../../../../../../src/app/host/navigation/HNavbarSpaceSubmenu.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAO,OAAO,CAAC;AAE3B,OAAO,EAAM,GAAG,EAAE,MAAM,gBAAgB,CAAA;AAExC,OAAO,EAAE,WAAW,EAAE,MAAM,UAAU,CAAC;AAEvC,qBAAa,mBAAoB,SAAQ,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC;IACnE,WAAW,EAAE,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC;IAEnC,iBAAiB,IAAI,IAAI;IAoBzB,cAAc,WAAY,GAAG,CAAC,KAAK,CAAC,YAAY,UAM/C;IAED,cAAc,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,YAAY,EAAE,GAAG,GAAE,MAAM,GAAC,WAAyB;IAqBpF,cAAc,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,YAAY;IAkB7C,kBAAkB,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,YAAY;IAsBjD,iBAAiB,CAAC,OAAO,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,YAAY,EAAE;IAWpD,YAAY,CAAC,kBAAkB,EAAE,GAAG,CAAC,KAAK,CAAC,YAAY;IAevD,aAAa,CAAC,kBAAkB,EAAE,GAAG,CAAC,KAAK,CAAC,YAAY;IA8BxD,MAAM;CAUP"}
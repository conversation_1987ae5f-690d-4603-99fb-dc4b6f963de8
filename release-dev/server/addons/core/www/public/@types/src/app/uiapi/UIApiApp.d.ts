import React from 'react';
import { app } from "@datatp-ui/lib";
export declare class UIApiFeatureRegistry extends app.BaseAppFeature {
    module: string;
    name: string;
    label: string;
    description: string;
    requiredAppCapability: app.AppCapability;
    createUI(_ctx: app.HostAppContext): React.JSX.Element;
}
interface UIApiHostProps {
    token: string;
}
export declare class UIApiUIHost extends React.Component<UIApiHostProps> implements app.IHostApp {
    appContext: app.AppContext;
    hostContext: app.HostAppContext;
    constructor(props: any);
    getHostAppContext(): app.HostAppContext;
    handle(event: app.HostAppEvent): void;
    render(): React.JSX.Element;
}
export declare const UIApiHostWrapper: (props: any) => React.JSX.Element;
export {};
//# sourceMappingURL=UIApiApp.d.ts.map
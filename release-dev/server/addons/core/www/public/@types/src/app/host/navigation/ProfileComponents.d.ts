import React from "react";
import { app } from '@datatp-ui/lib';
import { UIHostAppContext } from '../UIHostAppContext';
type ICompanyAclModel = app.host.ICompanyAclModel;
interface HostAppProps {
    uiHostAppContext: UIHostAppContext;
}
declare class CompanyAclNode {
    companyAcl: ICompanyAclModel;
    children: Array<CompanyAclNode> | null;
    constructor(companyAcl: ICompanyAclModel);
    addChild(node: CompanyAclNode): void;
}
export declare class CompanySelector extends React.Component<HostAppProps> {
    onInputChange: (bean: any, field: string, oldVal: any, newVal: any) => void;
    buildOptAndLabels(opts: Array<string>, labels: Array<React.ReactElement>, node: CompanyAclNode, deep: number): void;
    render(): React.JSX.Element;
}
export declare class UserPreferences extends React.Component<HostAppProps> {
    onChangeLanguage: (bean: any, field: string, oldVal: any, newVal: any) => void;
    renderLanguagePref(): React.JSX.Element;
    render(): React.JSX.Element;
}
export {};
//# sourceMappingURL=ProfileComponents.d.ts.map
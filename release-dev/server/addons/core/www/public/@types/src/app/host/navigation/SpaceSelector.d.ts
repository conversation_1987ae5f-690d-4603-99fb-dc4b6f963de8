import { bs } from '@datatp-ui/lib';
import { UIHostAppContext } from '../UIHostAppContext';
export interface AppSpaceSelectorProps extends bs.BaseTabPaneProps {
    uiHostAppContext: UIHostAppContext;
}
export declare class AppSpaceSelector extends bs.ButtonTabPane<AppSpaceSelectorProps> {
    onSelect(name: string): void;
    createConfig(): bs.TabPaneConfig;
}
//# sourceMappingURL=SpaceSelector.d.ts.map
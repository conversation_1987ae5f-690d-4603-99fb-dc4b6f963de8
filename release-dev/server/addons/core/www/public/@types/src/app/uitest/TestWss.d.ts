import React from 'react';
import { server } from "@datatp-ui/lib";
export declare class WebSocketManager {
    online: boolean;
    wsUrl: string | null;
    websocket: WebSocket | null;
    isOpen(): boolean;
    open(wsUrl: string): void;
    close(): void;
    send(message: server.BackendRequest): void;
    getJSessionId(): string;
}
interface TestWssProps {
}
export declare class TestWss extends React.Component<TestWssProps> {
    onOpenWss: () => void;
    onCloseWss: () => void;
    onSend: () => void;
    render(): React.JSX.Element;
}
export {};
//# sourceMappingURL=TestWss.d.ts.map
import React from "react";
import { app } from '@datatp-ui/lib';
import { NavbarProps } from './Navbar';
export declare class HNavbarSpaceSubmenu extends React.Component<NavbarProps> {
    spaceConfig: app.space.SpaceConfig;
    componentDidMount(): void;
    onSelectScreen: (screen: app.space.ScreenConfig) => void;
    renderMenuItem(screen: app.space.ScreenConfig, laf?: 'info' | 'secondary'): React.JSX.Element;
    renderMenuTree(screen: app.space.ScreenConfig): React.JSX.Element;
    renderDropdownMenu(screen: app.space.ScreenConfig): React.JSX.Element;
    getSelectedScreen(screens?: app.space.ScreenConfig[]): app.space.ScreenConfig;
    renderMobile(selSecondLevelMenu: app.space.ScreenConfig): React.JSX.Element;
    renderDesktop(selSecondLevelMenu: app.space.ScreenConfig): React.JSX.Element;
    render(): React.JSX.Element;
}
//# sourceMappingURL=HNavbarSpaceSubmenu.d.ts.map
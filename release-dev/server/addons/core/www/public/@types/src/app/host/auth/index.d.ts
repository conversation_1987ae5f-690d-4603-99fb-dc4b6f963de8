import React from "react";
export { UILogin } from './uiauth';
type PrivateRouteProps = {
    loginPath: string;
    path: any;
    component: any;
};
type PrivateRouteState = {
    tryAutoSignin: boolean;
};
export declare class PrivateRoute extends React.Component<PrivateRouteProps, PrivateRouteState> {
    state: {
        tryAutoSignin: boolean;
    };
    tryAutoSignin(): React.JSX.Element;
    render(): React.JSX.Element;
}
//# sourceMappingURL=index.d.ts.map
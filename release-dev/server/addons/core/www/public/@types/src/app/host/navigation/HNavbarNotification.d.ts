import React from "react";
import { bs, util } from '@datatp-ui/lib';
import { NavbarProps } from './Navbar';
export declare class HNavbarNotification extends React.Component<NavbarProps> {
    poller: util.common.Poller;
    componentDidMount(): void;
    componentWillUnmount(): void;
    updateUnreadMessages: () => void;
    onMarkAllAsRead: () => void;
    onDetail: () => void;
    render(): React.JSX.Element;
    renderMessage(msg: bs.NotificationMessage): React.JSX.Element;
}
//# sourceMappingURL=HNavbarNotification.d.ts.map
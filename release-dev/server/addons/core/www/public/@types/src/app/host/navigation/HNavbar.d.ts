import React from "react";
import { NavbarProps } from './Navbar';
interface HNavbarThemeSwitcherState {
    theme: 'light' | 'dark';
}
export declare class HNavbarThemeSwitcher extends React.Component<{}, HNavbarThemeSwitcherState> {
    constructor(props: any);
    onChangeTheme: (_event: any) => void;
    render(): React.JSX.Element;
}
export declare class HNavbar extends React.Component<NavbarProps> {
    render(): React.JSX.Element;
}
export {};
//# sourceMappingURL=HNavbar.d.ts.map
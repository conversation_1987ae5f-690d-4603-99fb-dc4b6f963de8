import React, { Component, KeyboardEvent } from "react";
import { input } from "@datatp-ui/lib";
type UILoginProps = {
    location: string;
};
type UILoginState = {
    redirectToReferrer: any;
};
export declare class UILogin extends Component<UILoginProps, UILoginState> {
    state: {
        redirectToReferrer: boolean;
    };
    onLogin(loginModel: any): void;
    onLoginByShortcutKey(winput: input.WInput, evt: KeyboardEvent, currInput: any, model: any): void;
    renderBanner(): React.JSX.Element;
    render(): React.JSX.Element;
}
export {};
//# sourceMappingURL=uiauth.d.ts.map
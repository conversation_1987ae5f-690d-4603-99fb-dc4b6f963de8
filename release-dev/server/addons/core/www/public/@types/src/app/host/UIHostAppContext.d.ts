import { app } from '@datatp-ui/lib';
import UserAppFeatureManager = app.host.UserAppFeatureManager;
export declare class UIHostAppContext {
    appFeatureManager: UserAppFeatureManager;
    hostAppContext: app.HostAppContext;
    constructor(uiOS: app.IHostApp);
    getAppFeatureManager(): app.host.UserAppFeatureManager;
    getAppSpaceManager(): app.space.AppSpaceManager;
    updateFeatureRegistryManager(): void;
}
//# sourceMappingURL=UIHostAppContext.d.ts.map
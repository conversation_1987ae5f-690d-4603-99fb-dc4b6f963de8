import React from 'react';
import { app } from "@datatp-ui/lib";
export declare class PublicAppFeature extends app.BaseAppFeature {
    module: string;
    name: string;
    label: string;
    description: string;
    requiredAppCapability: app.AppCapability;
    createUI(_ctx: app.HostAppContext): React.JSX.Element;
}
interface UIPublicHostProps {
    page: string;
}
export declare class UIPublicHost extends React.Component<UIPublicHostProps> implements app.IHostApp {
    appContext: app.AppContext;
    hostContext: app.HostAppContext;
    constructor(props: any);
    getHostAppContext(): app.HostAppContext;
    handle(_event: app.HostAppEvent): void;
    render(): React.JSX.Element;
}
export declare const UIPublicHostWrapper: (props: any) => React.JSX.Element;
export {};
//# sourceMappingURL=UIPublicApp.d.ts.map
import React from 'react';
import { app } from "@datatp-ui/lib";
interface OdooEmbeddedAppProps {
    appId: string;
    token: any;
}
export declare class OdooEmbeddedApp extends React.Component<OdooEmbeddedAppProps> implements app.IHostApp {
    componentDidMount(): void;
    componentWillUnmount(): void;
    getHostAppContext(): app.HostAppContext;
    handle(event: app.HostAppEvent): void;
    render(): React.JSX.Element;
}
export {};
//# sourceMappingURL=OdooEmbeddedApp.d.ts.map
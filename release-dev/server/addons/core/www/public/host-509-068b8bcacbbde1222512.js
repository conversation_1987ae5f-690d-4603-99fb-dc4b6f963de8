/*! For license information please see host-509-068b8bcacbbde1222512.js.LICENSE.txt */
"use strict";(self.webpackChunkdatatp_ui_host=self.webpackChunkdatatp_ui_host||[]).push([[509],{533:(e,t,r)=>{function n(){return n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},n.apply(this,arguments)}var o;r.d(t,{Gh:()=>T,HS:()=>z,Oi:()=>l,Rr:()=>h,pX:()=>M,pb:()=>P,rc:()=>o,tH:()=>N,ue:()=>f,yD:()=>L,zR:()=>i}),function(e){e.Pop="POP",e.Push="PUSH",e.<PERSON>lace="REPLACE"}(o||(o={}));const a="popstate";function i(e){return void 0===e&&(e={}),function(e,t,r,i){void 0===i&&(i={});let{window:s=document.defaultView,v5Compat:h=!1}=i,d=s.history,f=o.Pop,v=null,m=y();function y(){return(d.state||{idx:null}).idx}function g(){f=o.Pop;let e=y(),t=null==e?null:e-m;m=e,v&&v({action:f,location:w.location,delta:t})}function b(e){let t="null"!==s.location.origin?s.location.origin:s.location.href,r="string"==typeof e?e:p(e);return r=r.replace(/ $/,"%20"),l(t,"No window.location.(origin|href) available to create URL for href: "+r),new URL(r,t)}null==m&&(m=0,d.replaceState(n({},d.state,{idx:m}),""));let w={get action(){return f},get location(){return e(s,d)},listen(e){if(v)throw new Error("A history only accepts one active listener");return s.addEventListener(a,g),v=e,()=>{s.removeEventListener(a,g),v=null}},createHref:e=>t(s,e),createURL:b,encodeLocation(e){let t=b(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){f=o.Push;let n=u(w.location,e,t);r&&r(n,e),m=y()+1;let a=c(n,m),i=w.createHref(n);try{d.pushState(a,"",i)}catch(e){if(e instanceof DOMException&&"DataCloneError"===e.name)throw e;s.location.assign(i)}h&&v&&v({action:f,location:w.location,delta:1})},replace:function(e,t){f=o.Replace;let n=u(w.location,e,t);r&&r(n,e),m=y();let a=c(n,m),i=w.createHref(n);d.replaceState(a,"",i),h&&v&&v({action:f,location:w.location,delta:0})},go:e=>d.go(e)};return w}(function(e,t){let{pathname:r,search:n,hash:o}=e.location;return u("",{pathname:r,search:n,hash:o},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"==typeof t?t:p(t)},null,e)}function l(e,t){if(!1===e||null==e)throw new Error(t)}function s(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw new Error(t)}catch(e){}}}function c(e,t){return{usr:e.state,key:e.key,idx:t}}function u(e,t,r,o){return void 0===r&&(r=null),n({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?h(t):t,{state:r,key:t&&t.key||o||Math.random().toString(36).substr(2,8)})}function p(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&"?"!==r&&(t+="?"===r.charAt(0)?r:"?"+r),n&&"#"!==n&&(t+="#"===n.charAt(0)?n:"#"+n),t}function h(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}var d;function f(e,t,r){return void 0===r&&(r="/"),function(e,t,r,n){let o=P(("string"==typeof t?h(t):t).pathname||"/",r);if(null==o)return null;let a=v(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){return e.length===t.length&&e.slice(0,-1).every((e,r)=>e===t[r])?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(a);let i=null;for(let e=0;null==i&&e<a.length;++e){let t=C(o);i=j(a[e],t,n)}return i}(e,t,r,!1)}function v(e,t,r,n){void 0===t&&(t=[]),void 0===r&&(r=[]),void 0===n&&(n="");let o=(e,o,a)=>{let i={relativePath:void 0===a?e.path||"":a,caseSensitive:!0===e.caseSensitive,childrenIndex:o,route:e};i.relativePath.startsWith("/")&&(l(i.relativePath.startsWith(n),'Absolute route path "'+i.relativePath+'" nested under path "'+n+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),i.relativePath=i.relativePath.slice(n.length));let s=z([n,i.relativePath]),c=r.concat(i);e.children&&e.children.length>0&&(l(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+s+'".'),v(e.children,t,c,s)),(null!=e.path||e.index)&&t.push({path:s,score:S(s,e.index),routesMeta:c})};return e.forEach((e,t)=>{var r;if(""!==e.path&&null!=(r=e.path)&&r.includes("?"))for(let r of m(e.path))o(e,t,r);else o(e,t)}),t}function m(e){let t=e.split("/");if(0===t.length)return[];let[r,...n]=t,o=r.endsWith("?"),a=r.replace(/\?$/,"");if(0===n.length)return o?[a,""]:[a];let i=m(n.join("/")),l=[];return l.push(...i.map(e=>""===e?a:[a,e].join("/"))),o&&l.push(...i),l.map(t=>e.startsWith("/")&&""===t?"/":t)}!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(d||(d={})),new Set(["lazy","caseSensitive","path","id","index","children"]);const y=/^:[\w-]+$/,g=3,b=2,w=1,O=10,x=-2,E=e=>"*"===e;function S(e,t){let r=e.split("/"),n=r.length;return r.some(E)&&(n+=x),t&&(n+=b),r.filter(e=>!E(e)).reduce((e,t)=>e+(y.test(t)?g:""===t?w:O),n)}function j(e,t,r){void 0===r&&(r=!1);let{routesMeta:n}=e,o={},a="/",i=[];for(let e=0;e<n.length;++e){let l=n[e],s=e===n.length-1,c="/"===a?t:t.slice(a.length)||"/",u=k({path:l.relativePath,caseSensitive:l.caseSensitive,end:s},c),p=l.route;if(!u&&s&&r&&!n[n.length-1].route.index&&(u=k({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},c)),!u)return null;Object.assign(o,u.params),i.push({params:o,pathname:z([a,u.pathname]),pathnameBase:U(z([a,u.pathnameBase])),route:p}),"/"!==u.pathnameBase&&(a=z([a,u.pathnameBase]))}return i}function k(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=function(e,t,r){void 0===t&&(t=!1),void 0===r&&(r=!0),s("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let n=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,r)=>(n.push({paramName:t,isOptional:null!=r}),r?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),o+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?o+="\\/*$":""!==e&&"/"!==e&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),n]}(e.path,e.caseSensitive,e.end),o=t.match(r);if(!o)return null;let a=o[0],i=a.replace(/(.)\/+$/,"$1"),l=o.slice(1);return{params:n.reduce((e,t,r)=>{let{paramName:n,isOptional:o}=t;if("*"===n){let e=l[r]||"";i=a.slice(0,a.length-e.length).replace(/(.)\/+$/,"$1")}const s=l[r];return e[n]=o&&!s?void 0:(s||"").replace(/%2F/g,"/"),e},{}),pathname:a,pathnameBase:i,pattern:e}}function C(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return s(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function P(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&"/"!==n?null:e.slice(r)||"/"}function R(e,t,r,n){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(n)+"].  Please separate it out to the `to."+r+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function L(e,t){let r=function(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}(e);return t?r.map((e,t)=>t===r.length-1?e.pathname:e.pathnameBase):r.map(e=>e.pathnameBase)}function T(e,t,r,o){let a;void 0===o&&(o=!1),"string"==typeof e?a=h(e):(a=n({},e),l(!a.pathname||!a.pathname.includes("?"),R("?","pathname","search",a)),l(!a.pathname||!a.pathname.includes("#"),R("#","pathname","hash",a)),l(!a.search||!a.search.includes("#"),R("#","search","hash",a)));let i,s=""===e||""===a.pathname,c=s?"/":a.pathname;if(null==c)i=r;else{let e=t.length-1;if(!o&&c.startsWith("..")){let t=c.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}i=e>=0?t[e]:"/"}let u=function(e,t){void 0===t&&(t="/");let{pathname:r,search:n="",hash:o=""}="string"==typeof e?h(e):e,a=r?r.startsWith("/")?r:function(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?r.length>1&&r.pop():"."!==e&&r.push(e)}),r.length>1?r.join("/"):"/"}(r,t):t;return{pathname:a,search:B(n),hash:_(o)}}(a,i),p=c&&"/"!==c&&c.endsWith("/"),d=(s||"."===c)&&r.endsWith("/");return u.pathname.endsWith("/")||!p&&!d||(u.pathname+="/"),u}const z=e=>e.join("/").replace(/\/\/+/g,"/"),U=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),B=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",_=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";class N extends Error{}function M(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}const I=["post","put","patch","delete"],A=(new Set(I),["get",...I]);new Set(A),new Set([301,302,303,307,308]),new Set([307,308]),Symbol("deferred")},548:e=>{e.exports=function(e){var t=[];return t.toString=function(){return this.map(function(t){var r="",n=void 0!==t[5];return t[4]&&(r+="@supports (".concat(t[4],") {")),t[2]&&(r+="@media ".concat(t[2]," {")),n&&(r+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),r+=e(t),n&&(r+="}"),t[2]&&(r+="}"),t[4]&&(r+="}"),r}).join("")},t.i=function(e,r,n,o,a){"string"==typeof e&&(e=[[null,e,void 0]]);var i={};if(n)for(var l=0;l<this.length;l++){var s=this[l][0];null!=s&&(i[s]=!0)}for(var c=0;c<e.length;c++){var u=[].concat(e[c]);n&&i[u[0]]||(void 0!==a&&(void 0===u[5]||(u[1]="@layer".concat(u[5].length>0?" ".concat(u[5]):""," {").concat(u[1],"}")),u[5]=a),r&&(u[2]?(u[1]="@media ".concat(u[2]," {").concat(u[1],"}"),u[2]=r):u[2]=r),o&&(u[4]?(u[1]="@supports (".concat(u[4],") {").concat(u[1],"}"),u[4]=o):u[4]="".concat(o)),t.push(u))}},t}},2107:e=>{e.exports=function(e){return e[1]}},7069:(e,t,r)=>{var n=r(4532);t.H=n.createRoot,n.hydrateRoot},7330:(e,t,r)=>{r.d(t,{A:()=>c});var n=r(3516),o=r.n(n),a=r(1637),i=r.n(a);function l(){return l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},l.apply(this,arguments)}var s=(0,n.forwardRef)(function(e,t){var r=e.color,n=void 0===r?"currentColor":r,a=e.size,i=void 0===a?24:a,s=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,["color","size"]);return o().createElement("svg",l({ref:t,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},s),o().createElement("polyline",{points:"6 9 12 15 18 9"}))});s.propTypes={color:i().string,size:i().oneOfType([i().string,i().number])},s.displayName="ChevronDown";const c=s},5605:(e,t,r)=>{r.d(t,{A:()=>c});var n=r(3516),o=r.n(n),a=r(1637),i=r.n(a);function l(){return l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},l.apply(this,arguments)}var s=(0,n.forwardRef)(function(e,t){var r=e.color,n=void 0===r?"currentColor":r,a=e.size,i=void 0===a?24:a,s=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,["color","size"]);return o().createElement("svg",l({ref:t,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},s),o().createElement("path",{d:"M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"}))});s.propTypes={color:i().string,size:i().oneOfType([i().string,i().number])},s.displayName="Key";const c=s},2235:(e,t,r)=>{r.d(t,{A:()=>c});var n=r(3516),o=r.n(n),a=r(1637),i=r.n(a);function l(){return l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},l.apply(this,arguments)}var s=(0,n.forwardRef)(function(e,t){var r=e.color,n=void 0===r?"currentColor":r,a=e.size,i=void 0===a?24:a,s=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,["color","size"]);return o().createElement("svg",l({ref:t,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},s),o().createElement("rect",{x:"3",y:"11",width:"18",height:"11",rx:"2",ry:"2"}),o().createElement("path",{d:"M7 11V7a5 5 0 0 1 10 0v4"}))});s.propTypes={color:i().string,size:i().oneOfType([i().string,i().number])},s.displayName="Lock";const c=s},1649:(e,t,r)=>{r.d(t,{A:()=>c});var n=r(3516),o=r.n(n),a=r(1637),i=r.n(a);function l(){return l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},l.apply(this,arguments)}var s=(0,n.forwardRef)(function(e,t){var r=e.color,n=void 0===r?"currentColor":r,a=e.size,i=void 0===a?24:a,s=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,["color","size"]);return o().createElement("svg",l({ref:t,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},s),o().createElement("path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"}),o().createElement("polyline",{points:"16 17 21 12 16 7"}),o().createElement("line",{x1:"21",y1:"12",x2:"9",y2:"12"}))});s.propTypes={color:i().string,size:i().oneOfType([i().string,i().number])},s.displayName="LogOut";const c=s},2683:(e,t,r)=>{r.d(t,{A:()=>c});var n=r(3516),o=r.n(n),a=r(1637),i=r.n(a);function l(){return l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},l.apply(this,arguments)}var s=(0,n.forwardRef)(function(e,t){var r=e.color,n=void 0===r?"currentColor":r,a=e.size,i=void 0===a?24:a,s=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,["color","size"]);return o().createElement("svg",l({ref:t,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},s),o().createElement("path",{d:"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"}),o().createElement("polyline",{points:"22,6 12,13 2,6"}))});s.propTypes={color:i().string,size:i().oneOfType([i().string,i().number])},s.displayName="Mail";const c=s},7237:(e,t,r)=>{r.d(t,{A:()=>c});var n=r(3516),o=r.n(n),a=r(1637),i=r.n(a);function l(){return l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},l.apply(this,arguments)}var s=(0,n.forwardRef)(function(e,t){var r=e.color,n=void 0===r?"currentColor":r,a=e.size,i=void 0===a?24:a,s=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,["color","size"]);return o().createElement("svg",l({ref:t,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},s),o().createElement("path",{d:"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"}))});s.propTypes={color:i().string,size:i().oneOfType([i().string,i().number])},s.displayName="Moon";const c=s},8488:(e,t,r)=>{r.d(t,{A:()=>c});var n=r(3516),o=r.n(n),a=r(1637),i=r.n(a);function l(){return l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},l.apply(this,arguments)}var s=(0,n.forwardRef)(function(e,t){var r=e.color,n=void 0===r?"currentColor":r,a=e.size,i=void 0===a?24:a,s=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,["color","size"]);return o().createElement("svg",l({ref:t,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},s),o().createElement("circle",{cx:"12",cy:"12",r:"5"}),o().createElement("line",{x1:"12",y1:"1",x2:"12",y2:"3"}),o().createElement("line",{x1:"12",y1:"21",x2:"12",y2:"23"}),o().createElement("line",{x1:"4.22",y1:"4.22",x2:"5.64",y2:"5.64"}),o().createElement("line",{x1:"18.36",y1:"18.36",x2:"19.78",y2:"19.78"}),o().createElement("line",{x1:"1",y1:"12",x2:"3",y2:"12"}),o().createElement("line",{x1:"21",y1:"12",x2:"23",y2:"12"}),o().createElement("line",{x1:"4.22",y1:"19.78",x2:"5.64",y2:"18.36"}),o().createElement("line",{x1:"18.36",y1:"5.64",x2:"19.78",y2:"4.22"}))});s.propTypes={color:i().string,size:i().oneOfType([i().string,i().number])},s.displayName="Sun";const c=s},5082:(e,t,r)=>{r.d(t,{A:()=>c});var n=r(3516),o=r.n(n),a=r(1637),i=r.n(a);function l(){return l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},l.apply(this,arguments)}var s=(0,n.forwardRef)(function(e,t){var r=e.color,n=void 0===r?"currentColor":r,a=e.size,i=void 0===a?24:a,s=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,["color","size"]);return o().createElement("svg",l({ref:t,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},s),o().createElement("path",{d:"M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}),o().createElement("circle",{cx:"8.5",cy:"7",r:"4"}),o().createElement("polyline",{points:"17 11 19 13 23 9"}))});s.propTypes={color:i().string,size:i().oneOfType([i().string,i().number])},s.displayName="UserCheck";const c=s},8104:(e,t,r)=>{r.d(t,{A:()=>c});var n=r(3516),o=r.n(n),a=r(1637),i=r.n(a);function l(){return l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},l.apply(this,arguments)}var s=(0,n.forwardRef)(function(e,t){var r=e.color,n=void 0===r?"currentColor":r,a=e.size,i=void 0===a?24:a,s=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,["color","size"]);return o().createElement("svg",l({ref:t,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},s),o().createElement("rect",{x:"3",y:"3",width:"18",height:"18",rx:"2",ry:"2"}),o().createElement("line",{x1:"9",y1:"9",x2:"15",y2:"15"}),o().createElement("line",{x1:"15",y1:"9",x2:"9",y2:"15"}))});s.propTypes={color:i().string,size:i().oneOfType([i().string,i().number])},s.displayName="XSquare";const c=s},8951:(e,t,r)=>{r.d(t,{Kd:()=>s});var n=r(904),o=r(5838),a=r(2177),i=r(533);new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);try{window.__reactRouterVersion="6"}catch(e){}new Map;const l=n.startTransition;function s(e){let{basename:t,children:r,future:o,window:s}=e,c=n.useRef();null==c.current&&(c.current=(0,i.zR)({window:s,v5Compat:!0}));let u=c.current,[p,h]=n.useState({action:u.action,location:u.location}),{v7_startTransition:d}=o||{},f=n.useCallback(e=>{d&&l?l(()=>h(e)):h(e)},[h,d]);return n.useLayoutEffect(()=>u.listen(f),[u,f]),n.useEffect(()=>(0,a.V8)(o),[o]),n.createElement(a.Ix,{basename:t,children:r,location:p.location,navigationType:p.action,navigator:u,future:o})}var c,u;o.flushSync,n.useId,"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement,function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"}(c||(c={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(u||(u={}))},2177:(e,t,r)=>{r.d(t,{BV:()=>T,C5:()=>P,Ix:()=>L,V8:()=>C,Zp:()=>v,g:()=>m,qh:()=>R,zy:()=>d});var n=r(904),o=r(533);function a(){return a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},a.apply(this,arguments)}const i=n.createContext(null),l=n.createContext(null),s=n.createContext(null),c=n.createContext(null),u=n.createContext({outlet:null,matches:[],isDataRoute:!1}),p=n.createContext(null);function h(){return null!=n.useContext(c)}function d(){return h()||(0,o.Oi)(!1),n.useContext(c).location}function f(e){n.useContext(s).static||n.useLayoutEffect(e)}function v(){let{isDataRoute:e}=n.useContext(u);return e?function(){let{router:e}=function(){let e=n.useContext(i);return e||(0,o.Oi)(!1),e}(x.UseNavigateStable),t=S(E.UseNavigateStable),r=n.useRef(!1);return f(()=>{r.current=!0}),n.useCallback(function(n,o){void 0===o&&(o={}),r.current&&("number"==typeof n?e.navigate(n):e.navigate(n,a({fromRouteId:t},o)))},[e,t])}():function(){h()||(0,o.Oi)(!1);let e=n.useContext(i),{basename:t,future:r,navigator:a}=n.useContext(s),{matches:l}=n.useContext(u),{pathname:c}=d(),p=JSON.stringify((0,o.yD)(l,r.v7_relativeSplatPath)),v=n.useRef(!1);return f(()=>{v.current=!0}),n.useCallback(function(r,n){if(void 0===n&&(n={}),!v.current)return;if("number"==typeof r)return void a.go(r);let i=(0,o.Gh)(r,JSON.parse(p),c,"path"===n.relative);null==e&&"/"!==t&&(i.pathname="/"===i.pathname?t:(0,o.HS)([t,i.pathname])),(n.replace?a.replace:a.push)(i,n.state,n)},[t,a,p,c,e])}()}function m(){let{matches:e}=n.useContext(u),t=e[e.length-1];return t?t.params:{}}function y(e,t,r,i){h()||(0,o.Oi)(!1);let{navigator:l,static:p}=n.useContext(s),{matches:f}=n.useContext(u),v=f[f.length-1],m=v?v.params:{},y=(v&&v.pathname,v?v.pathnameBase:"/");v&&v.route;let g,x=d();if(t){var E;let e="string"==typeof t?(0,o.Rr)(t):t;"/"===y||(null==(E=e.pathname)?void 0:E.startsWith(y))||(0,o.Oi)(!1),g=e}else g=x;let S=g.pathname||"/",k=S;if("/"!==y){let e=y.replace(/^\//,"").split("/");k="/"+S.replace(/^\//,"").split("/").slice(e.length).join("/")}let C=!p&&r&&r.matches&&r.matches.length>0?r.matches:(0,o.ue)(e,{pathname:k}),P=function(e,t,r,a){var i;if(void 0===t&&(t=[]),void 0===r&&(r=null),void 0===a&&(a=null),null==e){var l;if(!r)return null;if(r.errors)e=r.matches;else{if(!(null!=(l=a)&&l.v7_partialHydration&&0===t.length&&!r.initialized&&r.matches.length>0))return null;e=r.matches}}let s=e,c=null==(i=r)?void 0:i.errors;if(null!=c){let e=s.findIndex(e=>e.route.id&&void 0!==(null==c?void 0:c[e.route.id]));e>=0||(0,o.Oi)(!1),s=s.slice(0,Math.min(s.length,e+1))}let u=!1,p=-1;if(r&&a&&a.v7_partialHydration)for(let e=0;e<s.length;e++){let t=s[e];if((t.route.HydrateFallback||t.route.hydrateFallbackElement)&&(p=e),t.route.id){let{loaderData:e,errors:n}=r,o=t.route.loader&&void 0===e[t.route.id]&&(!n||void 0===n[t.route.id]);if(t.route.lazy||o){u=!0,s=p>=0?s.slice(0,p+1):[s[0]];break}}}return s.reduceRight((e,o,a)=>{let i,l=!1,h=null,d=null;var f;r&&(i=c&&o.route.id?c[o.route.id]:void 0,h=o.route.errorElement||b,u&&(p<0&&0===a?(j[f="route-fallback"]||(j[f]=!0),l=!0,d=null):p===a&&(l=!0,d=o.route.hydrateFallbackElement||null)));let v=t.concat(s.slice(0,a+1)),m=()=>{let t;return t=i?h:l?d:o.route.Component?n.createElement(o.route.Component,null):o.route.element?o.route.element:e,n.createElement(O,{match:o,routeContext:{outlet:e,matches:v,isDataRoute:null!=r},children:t})};return r&&(o.route.ErrorBoundary||o.route.errorElement||0===a)?n.createElement(w,{location:r.location,revalidation:r.revalidation,component:h,error:i,children:m(),routeContext:{outlet:null,matches:v,isDataRoute:!0}}):m()},null)}(C&&C.map(e=>Object.assign({},e,{params:Object.assign({},m,e.params),pathname:(0,o.HS)([y,l.encodeLocation?l.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?y:(0,o.HS)([y,l.encodeLocation?l.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),f,r,i);return t&&P?n.createElement(c.Provider,{value:{location:a({pathname:"/",search:"",hash:"",state:null,key:"default"},g),navigationType:o.rc.Pop}},P):P}function g(){let e=function(){var e;let t=n.useContext(p),r=function(){let e=n.useContext(l);return e||(0,o.Oi)(!1),e}(E.UseRouteError),a=S(E.UseRouteError);return void 0!==t?t:null==(e=r.errors)?void 0:e[a]}(),t=(0,o.pX)(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return n.createElement(n.Fragment,null,n.createElement("h2",null,"Unexpected Application Error!"),n.createElement("h3",{style:{fontStyle:"italic"}},t),r?n.createElement("pre",{style:a},r):null,null)}const b=n.createElement(g,null);class w extends n.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?n.createElement(u.Provider,{value:this.props.routeContext},n.createElement(p.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function O(e){let{routeContext:t,match:r,children:o}=e,a=n.useContext(i);return a&&a.static&&a.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=r.route.id),n.createElement(u.Provider,{value:t},o)}var x=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(x||{}),E=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(E||{});function S(e){let t=function(){let e=n.useContext(u);return e||(0,o.Oi)(!1),e}(),r=t.matches[t.matches.length-1];return r.route.id||(0,o.Oi)(!1),r.route.id}const j={},k=(e,t,r)=>{};function C(e,t){void 0===(null==e?void 0:e.v7_startTransition)&&k("v7_startTransition","React Router will begin wrapping state updates in `React.startTransition` in v7","https://reactrouter.com/v6/upgrading/future#v7_starttransition"),void 0!==(null==e?void 0:e.v7_relativeSplatPath)||t&&t.v7_relativeSplatPath||k("v7_relativeSplatPath","Relative route resolution within Splat routes is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath"),t&&(void 0===t.v7_fetcherPersist&&k("v7_fetcherPersist","The persistence behavior of fetchers is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_fetcherpersist"),void 0===t.v7_normalizeFormMethod&&k("v7_normalizeFormMethod","Casing of `formMethod` fields is being normalized to uppercase in v7","https://reactrouter.com/v6/upgrading/future#v7_normalizeformmethod"),void 0===t.v7_partialHydration&&k("v7_partialHydration","`RouterProvider` hydration behavior is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_partialhydration"),void 0===t.v7_skipActionErrorRevalidation&&k("v7_skipActionErrorRevalidation","The revalidation behavior after 4xx/5xx `action` responses is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_skipactionerrorrevalidation"))}function P(e){let{to:t,replace:r,state:a,relative:i}=e;h()||(0,o.Oi)(!1);let{future:l,static:c}=n.useContext(s),{matches:p}=n.useContext(u),{pathname:f}=d(),m=v(),y=(0,o.Gh)(t,(0,o.yD)(p,l.v7_relativeSplatPath),f,"path"===i),g=JSON.stringify(y);return n.useEffect(()=>m(JSON.parse(g),{replace:r,state:a,relative:i}),[m,g,i,r,a]),null}function R(e){(0,o.Oi)(!1)}function L(e){let{basename:t="/",children:r=null,location:i,navigationType:l=o.rc.Pop,navigator:u,static:p=!1,future:d}=e;h()&&(0,o.Oi)(!1);let f=t.replace(/^\/*/,"/"),v=n.useMemo(()=>({basename:f,navigator:u,static:p,future:a({v7_relativeSplatPath:!1},d)}),[f,d,u,p]);"string"==typeof i&&(i=(0,o.Rr)(i));let{pathname:m="/",search:y="",hash:g="",state:b=null,key:w="default"}=i,O=n.useMemo(()=>{let e=(0,o.pb)(m,f);return null==e?null:{location:{pathname:e,search:y,hash:g,state:b,key:w},navigationType:l}},[f,m,y,g,b,w,l]);return null==O?null:n.createElement(s.Provider,{value:v},n.createElement(c.Provider,{children:r,value:O}))}function T(e){let{children:t,location:r}=e;return y(z(t),r)}function z(e,t){void 0===t&&(t=[]);let r=[];return n.Children.forEach(e,(e,a)=>{if(!n.isValidElement(e))return;let i=[...t,a];if(e.type===n.Fragment)return void r.push.apply(r,z(e.props.children,i));e.type!==R&&(0,o.Oi)(!1),e.props.index&&e.props.children&&(0,o.Oi)(!1);let l={id:e.props.id||i.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(l.children=z(e.props.children,i)),r.push(l)}),r}n.startTransition,new Promise(()=>{}),n.Component},3972:e=>{var t=[];function r(e){for(var r=-1,n=0;n<t.length;n++)if(t[n].identifier===e){r=n;break}return r}function n(e,n){for(var a={},i=[],l=0;l<e.length;l++){var s=e[l],c=n.base?s[0]+n.base:s[0],u=a[c]||0,p="".concat(c," ").concat(u);a[c]=u+1;var h=r(p),d={css:s[1],media:s[2],sourceMap:s[3],supports:s[4],layer:s[5]};if(-1!==h)t[h].references++,t[h].updater(d);else{var f=o(d,n);n.byIndex=l,t.splice(l,0,{identifier:p,updater:f,references:1})}i.push(p)}return i}function o(e,t){var r=t.domAPI(t);return r.update(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;r.update(e=t)}else r.remove()}}e.exports=function(e,o){var a=n(e=e||[],o=o||{});return function(e){e=e||[];for(var i=0;i<a.length;i++){var l=r(a[i]);t[l].references--}for(var s=n(e,o),c=0;c<a.length;c++){var u=r(a[c]);0===t[u].references&&(t[u].updater(),t.splice(u,1))}a=s}}},6511:e=>{var t={};e.exports=function(e,r){var n=function(e){if(void 0===t[e]){var r=document.querySelector(e);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(e){r=null}t[e]=r}return t[e]}(e);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(r)}},7800:e=>{e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},8300:(e,t,r)=>{e.exports=function(e){var t=r.nc;t&&e.setAttribute("nonce",t)}},5005:e=>{e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=e.insertStyleElement(e);return{update:function(r){!function(e,t,r){var n="";r.supports&&(n+="@supports (".concat(r.supports,") {")),r.media&&(n+="@media ".concat(r.media," {"));var o=void 0!==r.layer;o&&(n+="@layer".concat(r.layer.length>0?" ".concat(r.layer):""," {")),n+=r.css,o&&(n+="}"),r.media&&(n+="}"),r.supports&&(n+="}");var a=r.sourceMap;a&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(a))))," */")),t.styleTagTransform(n,e,t.options)}(t,e,r)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},8053:e=>{e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}}}]);
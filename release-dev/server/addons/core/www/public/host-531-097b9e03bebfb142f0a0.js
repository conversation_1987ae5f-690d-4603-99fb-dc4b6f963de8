"use strict";(self.webpackChunkdatatp_ui_host=self.webpackChunkdatatp_ui_host||[]).push([[531],{7069:(t,e,o)=>{var n=o(9236);e.H=n.createRoot,n.hydrateRoot},6531:(t,e,o)=>{o.r(e),o.d(e,{configureEmbeddedRoute:()=>c});var n=o(1027),r=o(7069),s=o(4687);const a=s.app.host.DATATP_SESSION;class p extends s.app.HostAppContext{broadcast(t){this.event=t}}class d extends n.Component{componentDidMount(){if(!a.isAuthenticated()){let{token:t}=this.props,e=t=>{this.forceUpdate()};console.log("Signin with odoo token"),a.signin("odoo",t,e)}}componentWillUnmount(){}getHostAppContext(){throw new Error("Method not implemented.")}handle(t){}render(){if(!a.isAuthenticated())return n.createElement("div",null," Odoo Embedded App");let{appId:t}=this.props;const e=s.app.host.CONFIG;let o=(new s.app.host.UserAppFeatureManager).get(t,!1),r=e.createServerContext(),d=new p(this,r);return n.createElement("div",{className:"light-theme flex-vbox"},o.createUI(d))}}class i{root;renderApp(t,e){const o=document.getElementById("datatp-ui");if(!o)throw new Error("Cannot find block with id datatp-ui");{const s=(0,r.H)(o);s.render(n.createElement(d,{appId:t,token:e})),this.root=s}}destroyApp(){this.root&&(this.root.unmount(),this.root=null)}}function c(){window.DATATP_UI.factory||(window.DATATP_UI.factory=new i)}}}]);
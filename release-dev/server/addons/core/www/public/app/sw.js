const SW_SESSION = { }

self.addEventListener('fetch', function (event) {
  /*
  event.respondWith(async function () {
    let url = event.request.url;
    //console.log('Fetch URL: ' + url);
    if(url.indexOf('/get/private') >= 0) {
      //console.log(' . Intercept URL: ' + url);
      const headers = new Headers(event.request.headers);
      let authorization = 'test datatp authorization';
      if(SW_SESSION['authorization']) {
        authorization = SW_SESSION['authorization'].authorization;
      }
      headers.append("DataTP-Authorization", authorization)
      const newRequest = new Request(event.request, {
        mode: 'cors', credentials: 'omit', headers: headers
      })
      return fetch(newRequest);
    }
    return fetch(event.request);
  }());
  */
});

self.addEventListener('message', function(event) {
  let eventData = event.data;
  if(eventData.action == 'notification:show') {

  } 
  /*
  if(eventData.action == 'update-authorization') {
    SW_SESSION['authorization'] = eventData['authorization'];
    //console.log("ServiceWorker: update authorization.....");
  }
  */
})
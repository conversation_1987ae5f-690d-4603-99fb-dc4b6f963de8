<div style="margin-bottom: 12px"></div>
<div class="text-bold">SHIPMENT INFO./ THÔNG TIN LÔ HÀNG</div>
{{#if bill.tmsBillForwarderTransport.isDomestic}}
    <div style="line-height: 15px; clear: both;">
        <table>
            <tr style="font-size: 10px;">
                <th colspan="4" class="text-center">DATE &amp; TIME</th>
                <th colspan="4" class="text-center">JOB ID</th>
                <th colspan="4" class="text-center">BILL</th>
                <th colspan="7" class="text-center">DETAILS</th>
            </tr>
            {{#each bills}}
                <tr style="font-size: 9px; page-break-inside: avoid; margin-bottom: 20px;">
                    <td colspan="4" class="text-center">
                        {{dateFormat deliveryTime format="dd/MM/yyyy"}} {{time}}
                    </td>
                    <td colspan="4" class="text-center">{{label}}</td>
                    <td colspan="4" class="text-center">
                        {{tmsBillForwarderTransport.bookingCode}}
                    </td>
                    <td colspan="7" class="text-left p-1" style="white-space: pre-line;">
                        {{shipmentInfo}}
                    </td>
                </tr>
            {{/each}}
        </table>
    </div>
{{else}}
    <div style="line-height: 15px; clear: both;">
        <table>
            {{#if bill}}
                <tr style="font-size: 10px;">
                    <th colspan="4" class="text-center">DATE &amp; TIME</th>
                    <th colspan="4" class="text-center">JOB ID</th>
                    <th colspan="4" class="text-center">{{#if bill.tmsBillForwarderTransport.isExport}}BOOKING{{else}}BILL{{/if}}</th>
                    <th colspan="7" class="text-center">DETAILS</th>
                    <th colspan="3" class="text-center">
                        {{#if bill.tmsBillForwarderTransport.isFcl}}
                            CY
                        {{else}}
                            CFS
                        {{/if}}
                    </th>
                    <th colspan="2" class="text-center">
                        {{#if bill.tmsBillForwarderTransport.isImport}}
                            ETA
                        {{else}}
                            CUT OFF
                        {{/if}}
                    </th>
                </tr>
                {{#each bills}}
                    <tr style="font-size: 9px; page-break-inside: avoid; margin-bottom: 20px;">
                        <td colspan="4" class="text-center">
                            {{dateFormat deliveryTime format="dd/MM/yyyy"}} {{time}}
                        </td>
                        <td colspan="4" class="text-center">{{label}}</td>
                        <td colspan="4" class="text-center">
                            {{tmsBillForwarderTransport.bookingCode}}
                        </td>
                        <td colspan="7" class="text-left p-1" style="white-space: pre-line;">
                            {{shipmentInfo}}
                        </td>
                        <td colspan="3" class="text-center">
                          {{#if tmsBillForwarderTransport.isExport}}
                            {{receiver.receiverLocation.shortLabel}} {{receiver.receiverContact}}
                          {{else}}
                            {{#if tmsBillForwarderTransport.isImport}}
                             {{sender.senderLocation.shortLabel}} {{sender.senderContact}}
                            {{/if}}
                          {{/if}}
                          </td>
                        <td colspan="2" class="text-center">{{tmsBillForwarderTransport.etaCutOffTime}}</td>
                    </tr>
                {{/each}}
            {{else}}
                <tr style="font-size: 10px;">
                    <th colspan="4" class="text-center">DATE &amp; TIME</th>
                    <th colspan="4" class="text-center">JOB ID</th>
                    <th colspan="4" class="text-center">BOOKING/BILL</th>
                    <th colspan="6" class="text-center">DETAILS</th>
                    <th colspan="3" class="text-center">CY/CFS</th>
                    <th colspan="3" class="text-center">ETA/CUT OFF</th>
                </tr>
                <tr style="font-size: 9px; height: 150px;">
                    <td colspan="4" class="text-left"></td>
                    <td colspan="4" class="text-left"></td>
                    <td colspan="4" class="text-center"></td>
                    <td colspan="6" class="text-center"></td>
                    <td colspan="3" class="text-center"></td>
                    <td colspan="3" class="text-center"></td>
                </tr>
            {{/if}}
        </table>
    </div>
{{/if}}
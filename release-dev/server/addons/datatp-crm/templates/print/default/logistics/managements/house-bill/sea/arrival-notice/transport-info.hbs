<table class="table-no-border">
  <tr class="iblock-valign-top">
    <td colspan="2" style="padding-left: 0; white-space: nowrap">
      Ng<PERSON><PERSON><PERSON> g<PERSON>i <i>(Shipper)</i> <span style="padding-left: 10px;">:</span>
    </td>
    <td colspan="9">
      <div style="padding-left: 9px; font-weight: bold">
        <pre style="white-space: pre-line; margin-top: -15px; margin-bottom: -10px;">
          {{stripTags houseBill.customField.customPrintShipper}}
        </pre>
      </div>
    </td>
  </tr>
  <tr class="iblock-valign-top">
    <td colspan="2" style="padding-left: 0; white-space: nowrap; text-align: right;">
      <i>(Consignee)</i> <span style="padding-left: 10px;">:</span>
    </td>
    <td colspan="9">
      <div style="padding-left: 9px; font-weight: bold">
        <pre style="white-space: pre-line; margin-top: -15px; margin-bottom: 5px;">
          {{stripTags houseBill.customField.customPrintConsignee}}
        </pre>
      </div>
    </td>
  </tr>
  <tr class="iblock-valign-top">
    <td colspan="2" style="padding-left: 0;">Tàu <i>(Vessel)</i></td>
    <td colspan="3">:
      {{#each orderTransportPlan.routes}}
        {{#if @last}}
          {{transportMethodLabel}}
        {{/if}}
      {{/each}}
    </td>
    <td colspan="2">Chuyến <i>(Voy.)</i></td>
    <td colspan="4">:
      {{#each orderTransportPlan.routes}}
        {{#if @last}}
          {{transportNo}}
        {{/if}}
      {{/each}}
    </td>
  </tr>
  <tr class="iblock-valign-top">
    <td colspan="2" style="padding-left: 0;">Từ <i>(From)</i></td>
    <td colspan="3">: {{houseBill.customField.customPrintPortOfLoading}}</td>
    <td colspan="2">Đến cảng <i>(Terminal)</i></td>
    <td colspan="4">:
      {{houseBill.customField.customPrintPortOfDischarge}}
    </td>
  </tr>
  <tr class="iblock-valign-top">
    <td colspan="2" style="font-size: 10px; padding-left: 0;">Ngày đến <i>(Arrived on)</i></td>
    <td colspan="3">:
        {{#each orderTransportPlan.routes}}
          {{#if @last }}
            {{#if arrivalTime}}{{dateFormat arrivalTime format="dd/MM/yyyy"}}{{/if}}
          {{/if}}
        {{/each}}
    </td>
    <td colspan="2" style="white-space: nowrap">Vận đơn chính <i>(M-B/L)</i></td>
    <td colspan="4">: {{masterBill.masterBillNo}}</td>
  </tr>
  <tr class="iblock-valign-top">
    <td colspan="2" style="padding-left: 0;">Kho <i>(At)</i></td>
    <td colspan="3">
      <div>: {{houseBill.warehouseLocationConfigLabel}}</div>
      </td>
    <td colspan="2">Vận đơn phụ <i>(H-B/L)</i></td>
    <td colspan="4">: <span style="font-weight: bold">{{houseBill.code}}</span></td>
  </tr>
</table>

<table class="my-2">
  <tr class="text-center">
    <th colspan="5">
      SỐ CONTAINER
      <div class="">Container No.</div>
    </th>
    <th colspan="2">
      SỐ LƯỢNG
      <div class="text-italic">Quantity</div>
    </th>
    <th colspan="4" class="text-italic">
      TÊN HÀNG HÓA
      <div>Description of Good</div>
    </th>
    <th colspan="3" class="text-italic">
      TRỌNG/KHỐI LƯỢNG
      <div>Weight/Measurement</div>
    </th>
  </tr>
  <tr class="iblock-valign-top">
    <td colspan="5" style="white-space: nowrap">
      {{#text 'eq' houseBill.mode 'SEA_LCL'}}
        <div>PART OF CONTAINER.</div>
        {{#each goods.containers}}
          <div>{{label}}{{#if containerType}}/{{containerType}}{{/if}}/{{sealNo}}</div>
        {{/each}}
      {{/text}}

      {{#text 'eq' houseBill.mode 'SEA_FCL'}}
        {{#each goods.containerGroups}}
          {{#if containerType}}
            {{#text 'eq' containerType 'unknown'}}
            {{else}}
              <div class="my-1">
                <div>{{containers.size}} x {{containerType}}</div>
                {{#each containers}}
                  <div class="my-1">
                    <div class="pl-1">{{container.label}}{{#if container.containerType}}/{{container.containerType}}{{/if}}/{{container.sealNo}}</div>
                  </div>
                {{/each}}
              </div>
            {{/text}}
          {{else}}
            <div class="my-1">
              <div>{{containers.size}} x unknown</div>
                {{#each containers}}
                  <div class="my-1">
                    <div class="pl-1">{{container.label}}{{#if container.containerType}}/{{container.containerType}}{{/if}}/{{container.sealNo}}</div>
                  </div>
                {{/each}}
            </div>
          {{/if}}
        {{/each}}
      {{/text}}
    </td>
    <td colspan="2">
      <div class="my-3">
        <div>{{houseBill.packageQty}} {{houseBill.packagingType}}</div>
      </div>
    </td>
    <td colspan="4" class="iblock-valign-middle">
      <div style="white-space: pre-line; margin-top: -30px; margin-bottom: -15px;">
        {{#if showDescriptionOfGoods}}
         {{houseBill.descOfGoods}}
        {{else}}
          AS PER BILL
        {{/if}}
      </div>
    </td>
    <td colspan="3" class="iblock-valign-middle" style="text-align: right;">
      <div>{{numberFormat houseBill.cargoGrossWeight minimumFractionDigits=3}} {{houseBill.cargoWeightUnit}}</div>
      <div>{{numberFormat houseBill.cargoVolume minimumFractionDigits=3}} {{houseBill.cargoVolumeUnit}}</div>
    </td>
  </tr>
</table>
<table class="table-no-border">
  <tr>
    <th colspan="4" class="text-center;" style="padding-left: 0;">GIAO HÀNG THEO: {{houseBill.source}}</th>
    <td colspan="1"></td>
    <td colspan="7">Ghi chú (<i>Note</i>) : {{houseBill.note}}</td>
  </tr>
</table>
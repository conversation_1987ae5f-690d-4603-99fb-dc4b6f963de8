{{#*inline 'renderRow'}}
<tr class="{{class}} ">
  <th class="header" colspan="10">{{fieldLabel}}</th>
  <td class="row" colspan="1" style="text-align: right; padding-right: 0;">:</td>
  <td class="border-none" colspan="27" style="padding-top: 15px">{{value}}</td>
</tr>
{{/inline}}

{{#with booking}}
<table>
  <tr>
    <th class="header" colspan="10">1. Shipper (name &amp; add)</th>
    <td class="row" colspan="1" style="text-align: right; padding-right: 0">:</td>
    <td class="row" colspan="27">{{inquiry.clientLabel}}</td>
  </tr>
  {{>renderRow fieldLabel='2. CNEE' value=customerCharge.consigneeLabel class='text-bold'}}
  <tr class="">
    <th class="header" colspan="10">3. Description of good</th>
    <td class="row" colspan="1" style="text-align: right; padding-right: 0;">:</td>
    <td class="row" colspan="27">
      <div style="white-space: pre-line; margin-top: -40px; margin-bottom: -40px ">
        {{#each inquiry.commodities}}
          {{#if description}}
            {{description}};
          {{/if}}
        {{/each}}
      </div>
    </td>
  </tr>
  {{>renderRow fieldLabel='4. Volume' value=(join inquiry.volume inquiry.volumeUnit ' ')}}
  {{>renderRow fieldLabel='5. Vessel'
    value=(join customerCharge.planTransportMethodLabel customerCharge.planTransportNo ' ') class='text-bold'}}
  {{>renderRow fieldLabel='6. ETD/ETA'
    value=(join (defaultIfEmpty (dateFormat customerCharge.planTimeDeparture format="MMM dd") ' ')
                (defaultIfEmpty (dateFormat customerCharge.planTimeArrival format="MMM dd") ' ')
                '/'
    )}}
  {{>renderRow fieldLabel='7. Port of loading' value=inquiry.fromLocationLabel}}
  {{>renderRow fieldLabel='8. Port of delivery' value=inquiry.toLocationLabel}}
  <tr style="color: red;">
    <th class="header" colspan="10">9. Freight and condition</th>
    <td class="row" colspan="1" style="text-align: right; padding-right: 0">:</td>
    <td class="row" colspan="7">{{paymentTerm}}</td>
    <td class="row" colspan="20">Service term: {{serviceTerm}}</td>
  </tr>
  <tr>
    <th class="header" colspan="10">10. Other items and conditions</th>
    <td class="row" colspan="1" style="text-align: right; padding-right: 0">:</td>
    <td class="row" colspan="27" style="color: red">{{otherItemsAndConditions}}</td>
  </tr>
</table>
{{/with}}
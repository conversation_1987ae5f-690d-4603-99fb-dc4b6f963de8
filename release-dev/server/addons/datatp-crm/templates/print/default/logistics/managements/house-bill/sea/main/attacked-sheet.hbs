{{#if hblAttackSheet}}
  <table class="table-no-border" style="margin-top: 20px; page-break-inside: avoid">
    <tr class="iblock-valign-top">
      <td colspan="6" style="height: 337px">
        {{#text "eq" houseBill.mode "SEA_FCL"}}
          <div class="my-2">FCL/FCL-CY/CY</div>
        {{/text}}
        {{#text "eq" houseBill.mode "SEA_LCL"}}
          <div class="my-2">LCL/LCL-CFS/CFS</div>
        {{/text}}
        <div>
          {{#text 'eq' houseBill.mode 'SEA_LCL'}}
            {{#if goods.containers}}PART OF CONTAINER{{/if}}
          {{else}}
            {{#each goods.containerGroups}}
              {{#if containerType}}
                {{#text 'eq' containerType 'unknown'}}
                  {{#each containers}}
                    <div class="my-1">
                      <div>{{container.containerType}}</div>
                    </div>
                  {{/each}}
                {{/text}}
              {{/if}}
            {{/each}}
            {{#each goods.containerGroups}}
              {{#if containerType}}
                {{#text 'eq' containerType 'unknown'}}
                {{else}}
                  <div class="my-1">
                    {{#each containers}}
                      <div class="my-1">
                        <div style="width: 150px; word-break: break-all;">
                          {{container.label}}/{{container.containerType}}/{{container.sealNo}}
                        </div>
                      </div>
                    {{/each}}
                  </div>
                {{/text}}
              {{/if}}
            {{/each}}
          {{/text}}
        </div>
        <div class="my-2" style="font-size: 9px;">
          <pre style="white-space: pre-line; margin-top: -12px;">
            {{#if houseBill.customField.customPrintShippingMarks}}
              Shipping marks:
              <div>
                {{houseBill.customField.customPrintShippingMarks}}
              </div>
            {{/if}}
          </pre>
        </div>
      </td>
      <td colspan="3" style="white-space: nowrap;">
        <div class="font-size-smaller">
          <div class="my-2" style="margin-left: -5px;">
            {{#if houseBill.packageQty}}
              {{houseBill.packageQty}} {{houseBill.packagingType}}
            {{/if}}
          </div>
        </div>
      </td>
      <td colspan="13">
        <div style="margin-top: 10px; margin-left: 5px;">
          {{#text 'eq' houseBill.mode 'SEA_LCL'}}
            <div>PART OF CONTAINER S.T.C.:</div>
          {{else}}
            <div>
              {{#if goods.containerGroups}}
                {{#each goods.containerGroups.values}}
                  {{containers.length}}x{{x}}
                {{/each}}
              {{/if}}
              CONTAINER(S) S.T.C.:
            </div>
          {{/text}}
          <div class="my-2">
            <pre style="white-space: pre-line; margin-top: -30px; margin-bottom: -20px;">
              {{#if houseBill.descOfGoods}}
                {{stripTags houseBill.descOfGoods}}
              {{/if}}
            </pre>
          </div>
        </div>
      </td>
      <td colspan="3" style="white-space: nowrap; min-height: 250px; position: relative;">
        <div style="text-align: center; margin-top: 10px;">
          {{#each cargos}}
            <div>
              <div>{{totalGrossWeight}} {{grossWeightUnit}}</div>
            </div>
          {{/each}}
          <div style="white-space: nowrap;">
            {{#if cargos}}<hr /> {{/if}}
            {{numberFormat houseBill.cargoGrossWeight minimumFractionDigits=3}} {{houseBill.cargoWeightUnit}}
          </div>
        </div>
      </td>
      <td colspan="3" style="text-align: center;">
        {{#each cargos}}
          <div class="my-2">
            <div>{{totalVolume}} {{grossVolumeUnit}}</div>
          </div>
        {{/each}}
        <div class="my-2" style="white-space: nowrap;">
          {{#if cargos}}<hr /> {{/if}}
          {{numberFormat houseBill.cargoVolume minimumFractionDigits=3}} {{houseBill.cargoVolumeUnit}}
        </div>
      </td>
    </tr>
  </table>
{{/if}}




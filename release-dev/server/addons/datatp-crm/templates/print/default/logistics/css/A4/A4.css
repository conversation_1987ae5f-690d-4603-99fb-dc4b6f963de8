.page-9px {
  margin: 10mm 10mm 0mm 10mm;
  font-size: 9px;
}
.page-8px {
  margin: 10mm 10mm 0mm 10mm;
  font-size: 8px;
}

.sea-hawb-page {
  margin: 2mm 4mm 0mm 5mm;
  font-size: 10px;
}

.sea-hawb-page th,
.sea-hawb-page td {
  padding-left: 10px;
}

.bg-trapezoid {
  background-image: url({{resLocal}}/air-trapezoid.png);
  background-repeat: no-repeat;
  background-position-y: top;
  background-size: 100% 98%;
  font-size: 6px;
}

.bg-diagonal {
  background-image: url({{resLocal}}/air-diagonal.png);
  background-size: contain;
  background-repeat: no-repeat;
}

.sea-hawb-footer-background-img {
  position: fixed;
  width: 100%;
  height: 50px;
  bottom: 0;
  background-size: cover;
  background-repeat: no-repeat;
}

.background-gray {
  background-color: rgb(196, 194, 194);
}
{{#*inline 'renderCargos'}}
  {{#each cargos}}
    {{#if @first}}
      <div style="margin: -13px 0 0 174px">
        {{text "ft:number" quantity}} {{packagingType}}
      </div>
    {{else}}
      <div style="margin-left: 174px">
        {{text "ft:number" quantity}} {{packagingType}}
      </div>
    {{/if}}
  {{/each}}
{{/inline}}

 <table>
    <thead>
      <tr class="text-center" style="font-size: 1.3em">
        <th colspan="18">PARTICULARS PROVIDE BY SHIPPER</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <th colspan="4" class="border-right-none border-bottom-none iblock-valign-top">
          Marks and Numbers
        </th>
        <th colspan="3" class="border-left-none border-right-none border-bottom-none iblock-valign-top">
          Number and Types of Package
        </th>
        <th colspan="5" class="border-left-none border-right-none border-bottom-none iblock-valign-top">
          <div style="margin-left: 10px">
            Description of goods
          </div>
        </th>
        <th colspan="2" class="border-left-none border-right-none border-bottom-none iblock-valign-top">
          Gross Weight (KG)
        </th>
        <th colspan="4" class="border-left-none border-bottom-none iblock-valign-top">
          MEASUREMENT(CBM)
        </th>
      </tr>
      <tr>
        <td colspan="4" class="value border-right-none border-top-none iblock-valign-top" style="white-space:nowrap">
          <div class="my-2" style="font-size: 9px;">
            <pre style="white-space: pre-line; margin-top: -15px; margin-bottom: -15px;">
              {{#if houseBill.customField.customPrintShippingMarks}}
                Shipping marks:
                <div>
                  {{stripTags houseBill.customField.customPrintShippingMarks}}
                </div>
              {{/if}}
            </pre>
          </div>
        </td>
        <td colspan="3" class="value text-center border-left-none border-top-none border-right-none iblock-valign-top">
            {{#if houseBill.packageQty}}
              {{houseBill.packageQty}} {{houseBill.packagingType}}
            {{/if}}
        </td>
        <td colspan="5" class="value border-left-none border-top-none border-right-none iblock-valign-top">
          <div style="margin-left: 10px">
            <div>
              <pre style="white-space: pre-line; margin-top: -12px;">
                {{#if houseBill.descOfGoods}}
                  {{stripTags houseBill.descOfGoods}}
                {{/if}}
              </pre>
            </div>
          </div>
        </td>
        <td colspan="2" class="value border-left-none border-top-none border-right-none iblock-valign-top">
          {{#if houseBill.cargoGrossWeight}}
            {{numberFormat houseBill.cargoGrossWeight minimumFractionDigits=3}} {{houseBill.cargoWeightUnit}}
          {{/if}}
        </td>
        <td colspan="4" class="value border-left-none border-top-none iblock-valign-top">
          {{#if houseBill.cargoVolume}}
            {{numberFormat houseBill.cargoVolume minimumFractionDigits=3}} {{houseBill.cargoVolumeUnit}}
            {{#each goods.cargos}}
              <div style="white-space: nowrap;">{{notes}}</div>
            {{/each}}
          {{/if}}
        </td>
      </tr>
      <tr class="text-center">
        <td colspan="4">
          {{#text 'eq' houseBill.mode 'TRUCK_REGULAR'}}
            Truck No.
          {{else}}
            Container
          {{/text}}
        </td>
        <td colspan="2">Departure date</td>
        <td colspan="4">Depart from</td>
        <td colspan="4">On carriage to</td>
        <td colspan="2">Delivery date</td>
        <td colspan="2">Received by</td>
      </tr>
      {{#each orderTransportPlan.routes}}
        <tr class="text-center">
          <td colspan="4" class="value" style="padding-left: 2px">
            {{#text 'eq' houseBill.mode 'TRUCK_REGULAR'}}
              {{transportNo}}
            {{else}}
              {{#each goods.containerGroups}}
                {{#if containerType}}
                  {{#text 'eq' containerType 'unknown'}}
                  {{else}}
                    {{#each containers}}
                      <div class="my-1" style="width: 170px; word-break: break-all; text-align: left;">
                        {{container.label}}/{{container.containerType}}/{{container.sealNo}}
                      </div>
                    {{/each}}
                  {{/text}}
                {{/if}}
              {{/each}}
            {{/text}}
          </td>
          <td colspan="2" class="value">{{#if departTime}}{{dateFormat departTime "dd-MMM"}}{{/if}}</td>
          <td colspan="4" class="value">{{fromLocationLabel}}</td>
          <td colspan="4" class="value">{{toLocationLabel}}</td>
          <td colspan="2" class="value">{{#if arrivalTime}}{{dateFormat arrivalTime "dd-MMM"}}{{/if}}</td>
          <td colspan="2" class="value"></td>
        </tr>
      {{/each}}
    </tbody>
  </table>

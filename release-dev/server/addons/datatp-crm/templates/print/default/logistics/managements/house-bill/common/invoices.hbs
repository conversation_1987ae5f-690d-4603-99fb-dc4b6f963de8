{{#each invoiceModels}}
  {{#with invoice}}
    {{#text 'eq' cashflow 'Inbound'}}
      {{#if invoiceItems}}
        <div style="margin-top: 10px; margin-bottom: 10px; page-break-inside: avoid">
          <table class="table-no-border">
            {{#each invoiceItems}}
              <tr>
                <td colspan="4" style="border-bottom: dotted 0.5px;">{{this.label}}</td>
                <td colspan="1" style="border-bottom: dotted 0.5px;">{{text "ft:number" this.quantity}}</td>
                <td colspan="1" style="border-bottom: dotted 0.5px; white-space: nowrap">{{this.unit}}</td>
                <td colspan="2" class="text-right pr-3" style="border-bottom: dotted 0.5px;">
                  {{#text "eq" currency "VND"}}
                    {{numberFormat this.unitPrice "integer"}}
                  {{else}}
                    {{text "ft:currency" this.unitPrice}}
                  {{/text}}
                </td>
                <td colspan="1" class="text-right" style="border-bottom: dotted 0.5px; white-space: nowrap">
                  {{#if this.taxRate}}
                    {{text "ft:percent" this.taxRate}} {{this.taxType}}
                  {{/if}}
                </td>
                <td colspan="3" class="text-right" style="border-bottom: dotted 0.5px;">
                  {{#text "eq" currency "VND"}}
                    {{numberFormat finalCharge "integer"}}
                  {{else}}
                    {{text "ft:currency" finalCharge}}
                  {{/text}}
                  ({{currency}})
                </td>
              </tr>
            {{/each}}
          </table>
          <div class="text-right font-size-larger text-bold" style="border-top: solid 1px">
            <span class="iblock w-100px"> Total Charges </span>
            <span class="iblock" style="width: 400px;">
              {{#text "eq" currency "VND"}}
                {{numberFormat finalCharge "integer"}}
              {{else}}
                {{text "ft:currency" finalCharge}}
              {{/text}}
              ({{currency}})
            </span>
          </div>
        </div>
      {{/if}}
    {{/text}}
  {{/with}}
{{/each}}

{{#*inline "trCharge"}}
  {{#if value}}
    <tr>
      <td colspan="6">{{label}}</td>
      <td colspan="6">{{text 'ft:currency' value}} {{currency}}</td>
    </tr>
  {{/if}}
{{/inline}}

{{#if allow}}
  <div class="my-3">
    {{#with quotation}}
      {{#if customerSeaTransportCharges}}
        {{#list 'has' customerSeaTransportCharges 'type' 'FCL'}}
          <div class="p-1 bg-gray-3 text-bold" style="border: solid 0.5px;">
            Ocean Freight (FCL)
          </div>
        {{/list}}

        {{#each customerSeaTransportCharges}}
          {{#text "eq" type "FCL"}}
            <table>
              {{>db:print:logistics/sales/quotation/common/transportation-charge-info.hbs}}
            </table>

            <table>
              <tr class="bg-gray-1">
                <th colspan="6">Charges</th>
                <th colspan="6">Price</th>
              </tr>
              {{> trCharge label = 'Dry 20\'' value = dry20Price}}
              {{> trCharge label = 'Dry 40\'' value = dry40Price}}
              {{> trCharge label = 'Dry 45\'' value = dry45Price}}
              {{> trCharge label = 'High Cube 40\'' value = highCube40Price}}
              {{> trCharge label = 'High Cube 45\'' value = highCube45Price}}
              {{> trCharge label = 'Reefer 20\'' value = reefer20Price}}
              {{> trCharge label = 'Reefer 40\'' value = reefer40Price}}
              {{> trCharge label = 'Reefer High Cube 40\'' value = reeferHighCube40Price}}
              {{> trCharge label = 'Flat Rack 20\' (Fixed Ends)' value = flatRack20FixedEndsPrice}}
              {{> trCharge label = 'Flat Rack 40\' (Fixed Ends)' value = flatRack40FixedEndsPrice}}
              {{> trCharge label = 'Flat Rack 20\' (Folding Ends)' value = flatRack20FoldingEndsPrice}}
              {{> trCharge label = 'Flat Rack 40\' (Folding Ends)' value = flatRack40FoldingEndsPrice}}
              {{> trCharge label = 'Open Top 20\'' value = openTop20Price}}
              {{> trCharge label = 'Open Top 40\'' value = openTop40Price}}
              {{> trCharge label = 'ISO Tank 20\'' value = isoTank20Price}}
            </table>

            <table>
              {{>db:print:logistics/sales/quotation/common/additional-charges.hbs}}
            </table>
          {{/text}}
        {{/each}}
      {{/if}}
    {{/with}}
  </div>
{{/if}}
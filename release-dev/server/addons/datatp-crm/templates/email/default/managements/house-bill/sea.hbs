<pre>
Dear Sir/Madam
Thank you for choosing our service. We would like to update cargo status as below:

Vessel: {{orderTransportPlan.routes.0.transportNo}}
MBL No.: {{houseBill.masterBillNo}}
  {{#text:tbl }}
    {{~text:tbl:header 'Shipment Info' ''}}
    {{~text:tbl:row    'PO#' houseBill.purchaseOrderCode}}
    {{~text:tbl:row    'HAWB/H-B/L' houseBill.code}}
    {{~text:tbl:row    'Agent' houseBill.handlingAgentLabel}}
    {{~text:tbl:row    'Carrier' masterBill.carrierLabel }}
    {{~text:tbl:row    'Shipper' houseBill.shipperLabel}}
    {{~text:tbl:row    'Consignee' houseBill.consigneeLabel}}
    {{~text:tbl:row    'AOL/POL' orderTransportPlan.fromLocationLabel}}
    {{~text:tbl:row    'AOD/POD' orderTransportPlan.toLocationLabel}}
    {{~text:tbl:row    'ETD' orderTransportPlan.departTime}}
    {{~text:tbl:row    'ETA' orderTransportPlan.arrivalTime}}
    {{~text:tbl:row    'G.W' (join houseBill.cargoGrossWeight houseBill.cargoWeightUnit ' ')}}
    {{~text:tbl:row    'CBM' (join houseBill.cargoVolume houseBill.cargoVolumeUnit ' ')}}
  {{~/text:tbl}}
  {{#text:tbl }}
    {{~text:tbl:header 'Track & Trace' '' '' '' '' '' ''}}
    {{~text:tbl:row 'Carrier' 'Vessel' 'Date' 'Location' 'Volume' 'Weight' 'Status'}}
    {{#each orderTransportPlan.routes}}
      {{~text:tbl:row
        carrierLabel transportNo arrivalTime fromLocationLabel
        (join houseBill.cargoGrossWeight houseBill.cargoWeightUnit ' ')
        (join houseBill.cargoVolume houseBill.cargoVolumeUnit ' ')
        ''
      }}
    {{/each}}
  {{~/text:tbl}}
</pre>
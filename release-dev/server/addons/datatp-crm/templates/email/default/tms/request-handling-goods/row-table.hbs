<tr>
  <td class="table-tableCell" style="border: 1px solid black; width: 150px; vertical-align: top; text-align: center;">
    <p class="editor-paragraph">
      <span> {{dateFormat bill.deliveryTime format="dd/MM/yyyy"}} {{bill.time}}</span>
    </p>
  </td>
  <td class="table-tableCell" style="border: 1px solid black; width: 150px; vertical-align: top; text-align: center; ">
    <p class="editor-paragraph">
      <span style="white-space: pre-wrap;">{{bill.label}}</span>
    </p>
  </td>
  <td class="table-tableCell" style="border: 1px solid black; width: 140px; vertical-align: top; text-align: center;">
    <p class="editor-paragraph">
      <span style="white-space: pre-wrap;">{{bill.tmsBillForwarderTransport.bookingCode}}</span>
    </p>
  </td>
  <td class="table-tableCell" style="border: 1px solid black; width: 235px; vertical-align: top; text-align: center;">
      <p class="editor-paragraph">
        <span style="white-space: pre-wrap;">{{bill.shipmentInfo}}</span>
      </p>
  </td>
  <td class="table-tableCell" style="border: 1px solid black; width: 140px; vertical-align: top; text-align: center;">
    <p class="editor-paragraph">
      <span style="white-space: pre-wrap;">{{#if tmsBillForwarderTransport.isExport}} {{receiver.receiverLocation.shortLabel}} {{receiver.receiverContact}} {{else
      if tmsBillForwarderTransport.isImport}} {{sender.senderLocation.shortLabel}} {{sender.senderContact}}{{/if}}</span>
    </p>
  </td>
  <td class="table-tableCell" style="border: 1px solid black; width: 90px; vertical-align: top; text-align: center;">
    <p class="editor-paragraph">
      <span style="white-space: pre-wrap;">{{bill.tmsBillForwarderTransport.etaCutOffTime}}</span>
    </p>
  </td>
  <td class="table-tableCell" style="border: 1px solid black; width: 90px; vertical-align: top; text-align: center;">
    <p class="editor-paragraph">
      <span style="white-space: pre-wrap;">{{bill.description}}</span>
    </p>
  </td>
</tr>
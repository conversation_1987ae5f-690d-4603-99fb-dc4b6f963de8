
package lgc.forwarder.mngt.general

/**
 * Build an SQL query to calculate revenue, expenses, and profit based on a case reference from
 * accounting_invoice table.
 *
 * @param {@code domain} The conditions, filters of the query.
 * @return A string containing the SQL query.
 */

def buildQuery() {
  return """
    SELECT
      i.case_reference as case_reference,
      SUM(
          CASE
              WHEN i.payer_partner_id IN (:company_partner_id) OR i.payee_partner_id IN (:company_partner_id)
                  THEN CASE i.cash_flow WHEN 'Inbound' THEN i.domestic_total END
              ELSE 0
          END
      ) AS est_total_revenue,
      SUM(
          CASE
              WHEN i.payer_partner_id IN (:company_partner_id) OR i.payee_partner_id IN (:company_partner_id)
                  THEN CASE i.cash_flow WHEN 'Outbound' THEN i.domestic_total END
              ELSE 0
          END
      ) AS est_total_costing,
      SUM(
          CASE
              WHEN i.payer_partner_id IN (:company_partner_id) OR i.payee_partner_id IN (:company_partner_id) THEN
                  CASE
                      WHEN i.cash_flow = 'Inbound' OR (i.cash_flow = 'Internal' AND i.payee_partner_id IN (:company_partner_id))
                          THEN i.domestic_total
                      WHEN i.cash_flow = 'Outbound' OR (i.cash_flow = 'Internal' AND i.payer_partner_id IN (:company_partner_id))
                          THEN -i.domestic_total
                      ELSE 0
                  END
              ELSE 0
          END
      ) AS est_gross_profit,
      SUM(
          CASE
             WHEN i.payer_partner_id NOT IN (:company_partner_id) AND i.payee_partner_id NOT IN (:company_partner_id)
                THEN i.domestic_final_charge ELSE 0
          END
      ) AS est_on_behalf
    FROM accounting_invoice i
      WHERE i.company_id = :company_id
    GROUP BY i.case_reference
  """
}
buildQuery()
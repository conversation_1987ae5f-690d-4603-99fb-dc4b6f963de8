package lgc.forwarder.mngt.report

def buildQuery() {
  return """
    WITH container_counts AS (
      SELECT
        b.id as booking_process_id,
        t.container_type,
        COUNT(*) as count,
        CASE
          WHEN t.container_type LIKE '2%' THEN COUNT(t.id)
          WHEN t.container_type LIKE '4%' THEN COUNT(t.id) * 2
          ELSE NULL
        END AS tues
      FROM lgc_mgmt_booking_process as b
        LEFT JOIN lgc_mgmt_trackable_container as t
          ON b.id = t.booking_process_id
      GROUP BY b.id, t.container_type
    )
    SELECT
      h.booking_process_id as booking_process_id,
      temp.mawb_no as mawb_no,
      temp.bill_id as master_bill_id,
      temp.master_case_reference as master_case_reference,
      temp.close_date as close_date,
      STRING_AGG(container_counts.count::text || 'x' || container_counts.container_type, ', ') AS container,
      SUM(container_counts.tues) as tues
    FROM ${table_name} h
      INNER JOIN master_bill_temp temp
        ON h.master_bill_id = temp.bill_id AND '${master_bill_table_name}' = temp.table_name
      LEFT JOIN container_counts ON h.booking_process_id  = container_counts.booking_process_id
    GROUP BY h.booking_process_id, temp.mawb_no, temp.bill_id, temp.master_case_reference, temp.close_date
    """
}

buildQuery()
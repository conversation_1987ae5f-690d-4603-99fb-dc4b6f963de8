
package lgc.forwarder.mngt.report

def buildMasterBillSqlQuery() {
  return """
    SELECT
      'lgc_mgmt_air_master_bill' as table_name,
      amb.id as bill_id,
      amb.code as master_case_reference,
      amb.master_bill_no as mawb_no,
      amb.close_date as close_date
    FROM lgc_mgmt_air_master_bill amb
    WHERE amb.company_id IN (:company_ids)
        AND amb.issued_date >= COALESCE(:issued_date_from, amb.issued_date)
        AND amb.issued_date <= COALESCE(:issued_date_to, amb.issued_date)
        AND amb.close_date >= COALESCE(:closed_date_from, amb.close_date)
        AND amb.close_date <= COALESCE(:closed_date_to, amb.close_date)
    UNION ALL
    SELECT
      'lgc_mgmt_sea_master_bill' as table_name,
      smb.id as bill_id,
      smb.code as master_case_reference,
      smb.master_bill_no as mawb_no,
      smb.close_date as close_date
    FROM lgc_mgmt_sea_master_bill smb
    WHERE smb.company_id IN (:company_ids)
        AND smb.issued_date >= COALESCE(:issued_date_from, smb.issued_date)
        AND smb.issued_date <= COALESCE(:issued_date_to, smb.issued_date)
        AND smb.close_date >= COALESCE(:closed_date_from, smb.close_date)
        AND smb.close_date <= COALESCE(:closed_date_to, smb.close_date)
    UNION ALL
    SELECT
      'lgc_mgmt_rail_master_bill' as table_name,
      rmb.id as bill_id,
      rmb.code as master_case_reference,
      rmb.master_bill_no as mawb_no,
      rmb.close_date as close_date
    FROM lgc_mgmt_rail_master_bill rmb
    WHERE rmb.company_id IN (:company_ids)
        AND rmb.issued_date >= COALESCE(:issued_date_from, rmb.issued_date)
        AND rmb.issued_date <= COALESCE(:issued_date_to, rmb.issued_date)
        AND rmb.close_date >= COALESCE(:closed_date_from, rmb.close_date)
        AND rmb.close_date <= COALESCE(:closed_date_to, rmb.close_date)
  """
}
buildMasterBillSqlQuery()
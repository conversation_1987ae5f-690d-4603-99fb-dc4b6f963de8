
package lgc.forwarder.mngt.general

/**
 * Build an SQL query to calculate revenue, expenses, and profit based on a case reference from
 * accounting_journal_entry_line table.
 *
 * @param {@code domain} The conditions, filters of the query.
 * @return A string containing the SQL query.
 */

def buildQuery() {
  return """
    SELECT
        li.case_reference as case_reference,
        SUM(
            CASE WHEN li.type = 'out_invoice' THEN li.domestic_total_amount ELSE 0 END) AS total_revenue,
        SUM(
            CASE WHEN li.type = 'in_invoice' THEN li.domestic_total_amount ELSE 0 END) AS total_costing,
        SUM(
            CASE
                WHEN li.type = 'out_invoice' THEN li.domestic_total_amount
                WHEN li.type = 'in_invoice' THEN -li.domestic_total_amount
                ELSE 0
            END) AS gross_profit,
        SUM(
            CASE WHEN li.type = 'ohb_out_invoice' THEN li.domestic_total_amount ELSE 0 END) AS on_behalf
    FROM accounting_journal_entry_line AS li
        INNER JOIN accounting_journal_entry en ON en.id = li.entry_id
    WHERE en.company_id = :company_id
        AND li.storage_state = 'ACTIVE'
        AND en.state = 'posted'
        AND li.case_reference IN (:case_references)
    GROUP BY li.case_reference
  """
}
buildQuery()
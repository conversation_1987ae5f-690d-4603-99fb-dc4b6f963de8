
package lgc.forwarder.mngt.general

/**
 * Build an SQL query to retrieve data for the shipment list screen.
 * For trucking and logistics, fetch from the booking process table.
 * For sea, air, and rail shipments, gather information from the master bill table.
 *
 * @param {@code primary_table}   Master Bill table name.
 * @param {@code secondary_table} House Bill table name.
 * @param {@code domain}          The conditions, filters of the query.
 *
 * @return A string containing the SQL query.
 */

def buildQuery() {
  return """
    SELECT
        '${primary_table}' as table_name,
        mb.id as bill_id,
        mb.code as case_reference,
        mb.transportation_mode as mode,
        NULL as type,
        mb.master_bill_no as mawb_no,
        COALESCE(hbs.hawb_no, '') AS hawb_no,
        mb.issued_date as issued_date,
        mb.close_date as closed_date,
        mb.purpose as purpose,
        mb.shipment_type as shipment_type,
        mb.carrier_partner_id as carrier_partner_id,
        mb.carrier_label as carrier_label,
        mb.assignee_employee_id as assignee_employee_id,
        mb.assignee_label as assignee_label,
        mb.package_quantity as package_quantity,
        mb.packaging_type as packaging_type,
        mb.gross_weight_in_kgs as total_gross_weight,
        mb.chargeable_weight_in_kgs as total_chargeable_weight,
        mb.volume_in_cbm as total_volume,
        mb.edit_mode as edit_mode,
        mb.state as state,
        mb.input_source as input_source,
        mb.note as note,
        mb.created_by as created_by,
        mb.created_time as created_time,
        mb.modified_by as modified_by,
        mb.modified_time as modified_time
    FROM ${primary_table} mb
        -- Mã hawb, concatenated by comma, grouped theo master bill
        FULL JOIN (
            SELECT
                hb.company_id,
                hb.master_bill_id,
                STRING_AGG(hb.code, ', ') AS hawb_no
            FROM ${secondary_table} hb
            GROUP BY hb.master_bill_id, hb.company_id
        ) as hbs
            ON hbs.master_bill_id = mb.id
            AND hbs.company_id = mb.company_id
    WHERE mb.company_id = :company_id
        ${domain}
  """
}
buildQuery()
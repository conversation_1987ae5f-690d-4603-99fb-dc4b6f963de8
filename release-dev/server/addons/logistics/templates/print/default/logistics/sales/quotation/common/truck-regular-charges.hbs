{{#*inline "trCharge"}}
  {{#if value}}
    <tr>
      <td colspan="6">{{label}}</td>
      <td colspan="6">{{text 'ft:currency' value}} {{currency}}</td>
    </tr>
  {{/if}}
{{/inline}}

{{#if allow}}
  <div class="my-3">
    {{#with quotation}}
      {{#list 'has' customerTruckTransportCharges 'truckType' 'REGULAR'}}
        <div class="p-1 bg-gray-3 text-bold" style="border: solid 0.5px;">
          Truck Regular Freight
        </div>
      {{/list}}

      {{#each customerTruckTransportCharges}}
        {{#text 'eq' truckType "REGULAR"}}
          <table>
            {{>db:print:logistics/sales/quotation/common/transportation-charge-info.hbs
              transportationCharge = truckRegularTransportCharge}}
          </table>

          <table>
            <tr class="bg-gray-1">
              <th colspan="6">Charges</th>
              <th colspan="6">Price</th>
            </tr>
            {{> trCharge label = 'Truck 500kgs' value = truck500kgsPrice }}
            {{> trCharge label = 'Truck 1 Ton' value = truckTonPrice }}
            {{> trCharge label = 'Truck 2 Ton' value = truck2TonPrice }}
            {{> trCharge label = 'Truck 3.5 Ton' value = truck3Ton5Price }}
            {{> trCharge label = 'Truck 5 Ton' value = truck5TonPrice }}
            {{> trCharge label = 'Truck 8 Ton' value = truck8TonPrice }}
            {{> trCharge label = 'Truck 10 Ton' value = truck10TonPrice }}
            {{> trCharge label = 'Truck 15 Ton' value = truck15TonPrice }}
            {{> trCharge label = 'Truck 20 Ton' value = truck20TonPrice }}
            {{> trCharge label = 'Truck 25 Ton' value = truck25TonPrice }}
            {{> trCharge label = 'Truck 30 Ton' value = truck30TonPrice }}
          </table>

          <table>
            {{>db:print:logistics/sales/quotation/common/additional-charges.hbs}}
          </table>
        {{/text}}
      {{/each}}
    {{/with}}
  </div>
{{/if}}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
  <meta http-equiv='Content-Type' content='text/xhtml; charset=UTF-8' />
  <title>Booking Note</title>
  <style>
    {{> file:print:css/A4/A4.css }}
  </style>
    <style>

      h1 {
        font-family: Arial, Helvetica, sans-serif;
        font-size: 24px
      }

      span {
        font-size: 13px;
      }
      .row {
        border: none;
        vertical-align: top;
        padding-top: 15px;
      }
      .header {
        border: none;
        text-align: left;
        font-weight: bold;
        font-style: italic;
        padding-top: 15px;
        padding-left: 0;
        vertical-align: top
      }
    </style>
</head>
<body class="page" style="margin-top: 15px">
  <div>
    {{> companydb:print:common/banner.hbs resStorage=model.resStorage}}
  </div>

  <div class="my-3">
    <h1 class="text-center" style="color:#00CCFF;">BOOKING NOTE</h1>
  </div>

  <div class="background-gray" style="clear: both; float: right; text-align: left; margin-bottom: 30px;">
    <div style="white-space: nowrap;">
      <span class="text-bold text-italic" style="display: inline-block; width: 80px;">Booking no.</span>
      {{booking.code}}
    </div>
    <div style="white-space: nowrap;">
      <span class="text-bold text-italic" style="display: inline-block; width: 80px;">Date:</span>
      {{#if booking.bookingDate}}{{dateFormat booking.bookingDate ["medium"]}}{{/if}}
    </div>
  </div>

  <div class="my-1" style="clear: both;">
    <span class="text-bold text-italic">It is this day mutually agreed between: </span>
    <span>{{model.company.fullName}}</span>
  </div>
  <div class="my-1">
    <span class="iblock w-200px text-left text-bold text-italic">And shipper: </span>
    <span style="padding-left: 59px;">{{booking.inquiry.clientLabel}}</span>
  </div>

  <div style="margin-top: 10px; margin-bottom: 5px">
    That the carrier will server space for loading the undermentioned consignment offered by shipper on the following
    terms and conditions:
  </div>

  {{#text 'eq' booking.inquiry.transportationMode 'AIR'}}
    {{> db:print:logistics/sales/booking/air/shipment-info.hbs customerCharge=booking.customerAirTransportCharge}}
    {{> db:print:logistics/sales/booking/related-party.hbs customerCharge=booking.customerAirTransportCharge}}
    {{> db:print:logistics/sales/booking/air/remark.hbs customerCharge=booking.customerAirTransportCharge}}
  {{/text}}
  {{#text 'eq' booking.inquiry.transportationMode 'SEA_FCL'}}
    {{> db:print:logistics/sales/booking/sea/shipment-info.hbs
      customerCharge=booking.customerSeaTransportCharge serviceTerm='FCL/FCL'}}
    {{> db:print:logistics/sales/booking/related-party.hbs customerCharge=booking.customerSeaTransportCharge}}
    {{> db:print:logistics/sales/booking/sea/remark.hbs customerCharge=booking.customerSeaTransportCharge}}
  {{/text}}
  {{#text 'eq' booking.inquiry.transportationMode 'SEA_LCL'}}
    {{> db:print:logistics/sales/booking/sea/shipment-info.hbs
      customerCharge=booking.customerSeaTransportCharge serviceTerm='LCL/LCL'}}
    {{> db:print:logistics/sales/booking/related-party.hbs customerCharge=booking.customerSeaTransportCharge}}
    {{> db:print:logistics/sales/booking/sea/remark.hbs customerCharge=booking.customerSeaTransportCharge}}
  {{/text}}

</body>

</html>
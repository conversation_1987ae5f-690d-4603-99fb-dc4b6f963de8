<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
  <meta http-equiv='Content-Type' content='text/xhtml; charset=UTF-8' />
  <title>Sea Shipping Instruction</title>
  <style>
    {{> file:print:css/A4/A4.css}}
  </style>

  <style>

    .title {
      font-family: Arial, Helvetica, sans-serif;
      font-weight: bold;
    }

    table {
      border-collapse: collapse;
      table-layout: fixed;
      width: 100%;
      margin: 0px;
    }

    th, td {
      border: 0.5px solid black;
      padding: 4px;
    }
  </style>
</head>

{{#*inline 'checkBox'}}
<div>
  <input type="checkbox" {{#if isChecked}}checked="true" {{/if}} />
  <span class="ml-1">{{label}}</span>
</div>
{{/inline}}

<body class="page">
  <div style="padding-top: 0; margin-top: 0; margin-bottom: 10px;">
    {{> companydb:print:logistics/common/banner.hbs}}
  </div>

  <h1 class="title" style="text-align: center">SHIPPING INSTRUCTION</h1>

  <h4>BOOKING NUMBER:
    <span style="color: red">
      {{#each houseBills}}
        {{#if (text 'eq' @index 0)}}
        {{else}}
          +
        {{/if}}
        {{bookingCode}}
      {{/each}}
    </span>
  </h4>

  <table style="border: 1px solid;">
    <tr>
      <td colspan="2">SHIPPER:</td>
      <td colspan="5" style="color: red; border-right: none; padding-top: 20px; padding-bottom: 20px">
        <pre style="white-space: pre-line; margin-top: -14px;">
          {{stripTags masterBill.customField.customPrintShipper}}
        </pre>
      </td>
      <td colspan="2" style="border-left: none; text-align: right;">
        <div>
          {{>checkBox isChecked=false label='NOT SHOW ON BL'}}
        </div>
        </td>
    </tr>
    <tr>
      <td colspan="2">CONSIGNEE:</td>
      <td colspan="5" style="color: red; border-right: none; padding-top: 20px; padding-bottom: 20px">
        <pre style="white-space: pre-line; margin-top: -12px;">
          {{stripTags masterBill.customField.customPrintConsignee}}
        </pre>
      </td>
      <td colspan="2"  style="border-left: none; text-align: right;">
        {{>checkBox isChecked=false label='NOT SHOW ON BL'}}
      </td>
    </tr>
    <tr>
      <td colspan="2">NOTIFY:</td>
      <td colspan="5" style="color: red; border-right: none; padding-top: 20px; padding-bottom: 20px">
        <pre style="white-space: pre-line; margin-top: -12px;">
          {{stripTags masterBill.customField.customPrintNotifyParty}}
        </pre>
      </td>
      <td colspan="2" style="border-left: none; text-align: right;">
        {{>checkBox isChecked=false label='NOT SHOW ON BL'}}
        {{>checkBox isChecked=false label='FREIGHT COLLECT'}}
      </td>
    </tr>
    <tr>
      <td colspan="2">PORT OF LOADING:</td>
      <td colspan="3">{{masterBillTransportPlan.fromLocationLabel}}</td>
      <td colspan="4" style="border: none; padding-left: 15px">
        {{>checkBox isChecked=false label='FREIGHT PREPAID'}}
      </td>
    </tr>
    <tr>
      <td colspan="2">PORT OF DISCHARGE:</td>
      <td colspan="3">{{masterBillTransportPlan.toLocationLabel}}</td>
      <td colspan="4" style="border: none;"></td>
    </tr>
    <tr>
      <td colspan="2">VESSEL VOY/ ETD:</td>
      <td colspan="3">{{#each masterBillTransportPlan.routes}}
      {{#if @last }}
       {{transportMethodLabel}} {{transportNo}} / {{#if arrivalTime}}{{dateFormat arrivalTime "MMM dd"}}{{/if}}
      {{/if}}
      {{/each}}</td>
      <td colspan="2" style="border: none; padding-left: 15px;">
        {{>checkBox isChecked=false label='ORIGINAL BL'}}
      </td>
      <td colspan="2" style="border: none; padding-left: 15px">
        {{>checkBox isChecked=false label='TELEX RELEASE BL'}}
      </td>
    </tr>
    <tr style="font-weight: bold; padding: 10px;">
      <td colspan="2">DETAIL:</td>
      <td colspan="1" style="text-align: center">Cont no</td>
      <td colspan="1" style="text-align: center">Seal No</td>
      <td colspan="1" style="text-align: center">Quantity</td>
      <td colspan="1" style="text-align: center">Type of package</td>
      <td colspan="1" style="text-align: center">Gross weight</td>
      <td colspan="1" style="text-align: center">CBM no.</td>
      <td colspan="1" style="text-align: center">HS.CODE</td>
    </tr>
    {{#each houseBills}}
      <tr>
        <td colspan="2">Booking <div>{{bookingCaseReference}}</div>
        </td>
        <td colspan="1" style="text-align: center; color: red;">
          {{#each goods.cargos}}
            {{#if (text 'eq' ../this.bookingProcessId this.bookingProcessId)}}
              {{#each goods.containers}}
                {{#text 'eq' ../this.containerId this.id}}
                  {{label}}
                {{/text}}
              {{/each}}
            {{/if}}
          {{/each}}
        </td>
        <td colspan="1" style="text-align: center; color: red;">
          {{#each goods.cargos}}
            {{#if (text 'eq' ../this.bookingProcessId this.bookingProcessId)}}
              {{#each goods.containers}}
                {{#text 'eq' ../this.containerId this.id}}
                  {{sealNo}}
                {{/text}}
              {{/each}}
            {{/if}}
          {{/each}}
        </td>
        <td colspan="1" style="text-align: center; color: red;">
          {{#each goods.cargos}}
            {{#if (text 'eq' ../this.bookingProcessId this.bookingProcessId)}}
             <div>{{quantity}}</div>
            {{/if}}
          {{/each}}
        </td>
        <td colspan="1" style="text-align: center; color: red;">
          {{#each goods.cargos}}
          {{#if (text 'eq' ../this.bookingProcessId this.bookingProcessId)}}
          <div>{{packagingType}}</div>
          {{/if}}
          {{/each}}
        </td>
        <td colspan="1" style="text-align: center; color: red;">
          {{#each goods.cargos}}
          {{#if (text 'eq' ../this.bookingProcessId this.bookingProcessId)}}
          <div>{{totalGrossWeight}}</div>
          {{/if}}
          {{/each}}
        </td>
        <td colspan="1" style="text-align: center; color: red;"></td>
        <td colspan="1" style="text-align: center; color: red;"></td>
      </tr>
    {{/each}}
    <tr>
      <th colspan="2" style="padding-top: 10px; padding-bottom: 10px;">TOTAL</th>
      <td colspan="1" style="text-align: center; color: red;"></td>
      <td colspan="1" style="text-align: center; color: red;"></td>
      <td colspan="1" style="text-align: center; color: red;">{{masterBill.packageQty}}</td>
      <td colspan="1" style="text-align: center; color: red;">{{masterBill.packagingType}}</td>
      <td colspan="1" style="text-align: center; color: red;">{{masterBill.grossWeightInKGS }}</td>
      <td colspan="1" style="text-align: center; color: red;"></td>
      <td colspan="1" style="text-align: center; color: red;"></td>
    </tr>
    <tr style="min-height: 120px;">
      <td colspan="2">Description of goods / <div>MÔ TẢ HÀNG HÓA</div></td>
      <td colspan="7" style="text-align: center">
        <div class="my-1">
          <pre style="white-space: pre-line; margin-top: -40px; margin-bottom: -40px;">
            {{#each houseBills}}
              {{#if this.descOfGoods}}
                {{stripTags this.descOfGoods}} <br/>
              {{/if}}
            {{/each}}
          </pre>
        </div>
      </td>
    </tr>
    <tr style="min-height: 150px;">
      <td colspan="2">SHIPPING MARK/GHI CHÚ</td>
      <td colspan="7" style="text-align: center">
        <div class="my-1">
          <pre style="white-space: pre-line; margin-top: -12px;">
            {{stripTags masterBill.customField.customPrintShippingMarks}}
          </pre>
        </div>
      </td>
    </tr>
  </table>
</body>

</html>
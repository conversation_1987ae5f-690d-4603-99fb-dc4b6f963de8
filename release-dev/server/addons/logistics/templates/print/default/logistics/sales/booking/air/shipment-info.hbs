{{#*inline 'renderRow'}}
<tr class="{{class}} " style="{{style}}">
  <th class="header" colspan="10">{{fieldLabel}}</th>
  <td class="row" colspan="1" style="text-align: right; padding-right: 0;">:</td>
  <td class="border-none" colspan="27" style="padding-top: 15px">{{value}}</td>
</tr>
{{/inline}}

{{#with booking}}
<table>
  {{>renderRow fieldLabel='1. B/L'
    value=(join 'MAWB:' customerCharge.planMBCode ' ')}}
  <tr>
    <th class="header" colspan="10">2. Schedule</th>
    <td class="row" colspan="1" style="text-align: right; padding-right: 0;">:</td>
    <td class="row" colspan="27">
      {{join
        customerCharge.planFlightNumber
        (dateFormat customerCharge.planTimeDeparture format="MMM-dd")
        (join customerCharge.fromLocationCode customerCharge.toLocationCode ' - ')
        '/ '
      }}
      ({{join
        (dateFormat customerCharge.planTimeDeparture format="hh:mm")
        (dateFormat customerCharge.planTimeArrival format="hh:mm")
        '-'
      }})
    </td>
  </tr>
  <tr>
    <th class="header" colspan="10">3. Description of good</th>
    <td class="row" colspan="1" style="text-align: right; padding-right: 0;">:</td>
    <td class="row" colspan="27">
      <div style="white-space: pre-line; margin-top: -40px; margin-bottom: -40px ">
        {{#each inquiry.commodities}}
          {{#if description}}
            <div>{{description}}</div>
          {{/if}}
        {{/each}}
      </div>
    </td>
  </tr>
  <tr>
    <th class="header" colspan="10">4. Gross Weight</th>
    <td class="row" colspan="1" style="text-align: right; padding-right: 0;">:</td>
    <td class="row" colspan="27" style="color: red">{{inquiry.grossWeight}} {{inquiry.grossWeightUnit}}</td>
  </tr>
  {{>renderRow fieldLabel='5. Shipper (name & add)' value=inquiry.clientLabel}}
  {{>renderRow fieldLabel='6. Consignee (name & add)' value=customerCharge.consigneeLabel}}
  <tr>
    <th class="header" colspan="10">7. Port of loading</th>
    <td class="row" colspan="1" style="text-align: right; padding-right: 0;">:</td>
    <td class="row" colspan="27" style="color: #FF0000">{{inquiry.fromLocationLabel}}</td>
  </tr>
  {{>renderRow fieldLabel='8. Port of delivery' value=inquiry.toLocationLabel}}
  {{>renderRow fieldLabel='9. Freight and condition' value=paymentTerm style="color: #FF0000;"}}
  <tr>
    <th class="header" colspan="10">10. Other items and conditions</th>
    <td class="row" colspan="1" style="text-align: right; padding-right: 0;">:</td>
    <td class="row" colspan="27" style="color: red">{{otherItemsAndConditions}}</td>
  </tr>
</table>
{{/with}}
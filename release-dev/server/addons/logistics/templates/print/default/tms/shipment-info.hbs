<div style="line-height: 22px; clear: both;">
  <table>
    <tr>
      <th colspan="2" class="text-center">JOB ID</th>
      <th colspan="3" class="text-center">
        SHIPMENT INFO
      </th>
      <th colspan="2" class="text-center">ETA/CUT OFF</th>
      <th colspan="2" class="text-center">
        TRUCK TYPE
      </th>
      <th colspan="4" class="text-center">NOTE</th>
    </tr>
    {{#each bills}}
      <tr style="font-size: 9px;">
        <td colspan="2" class="text-left">{{label}}</td>
        {{#if bill.tmsBillForwarderTransport.isFcl}}
          <td colspan="3" class="text-left">
            {{tmsBillForwarderTransport.containerNo}}/{{tmsBillForwarderTransport.sealNo}}<br/>
            {{tmsBillGoods.weight}} ({{tmsBillGoods.weightUnit}}) - {{tmsBillGoods.volumeAsText}} ({{tmsBillGoods.volumeUnit}})
          </td>
        {{else}}
          <td colspan="3" class="text-center">
            {{tmsBillGoods.quantity}} ({{tmsBillGoods.quantityUnit}})
          </td>
        {{/if}}
        <td colspan="2" class="text-center">{{tmsBillForwarderTransport.etaCutOffTime}}</td>
        <td colspan="2" class="text-center">
          {{tmsBillForwarderTransport.truckType}}
        </td>
        <td colspan="4" class="text-left">{{description}}</td>
      </tr>
    {{/each}}
    {{#if bill}}
    {{else}}
      <tr style="font-size: 9px; height: 200px;">
        <td colspan="2" class="text-left"></td>
        {{#if bill.tmsBillForwarderTransport.isFcl}}
        <td colspan="3" class="text-left">
        </td>
        {{else}}
        <td colspan="2" class="text-center">
        </td>
        {{/if}}
        <td colspan="2" class="text-center"></td>
        <td colspan="2" class="text-center">
        </td>
        <td colspan="4" class="text-left"></td>
      </tr>
    {{/if}}
  </table>
</div>
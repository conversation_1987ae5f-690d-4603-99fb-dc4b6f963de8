<p class="editor-paragraph" dir="ltr">
    <span style="white-space: pre-wrap;">Dear {{bills.0.customer.customerFullName}} Company,</span>
</p>
<p class="editor-paragraph" dir="ltr">
    <span style="white-space: pre-wrap;">I am writing to inform you of cont seal no. and trucks info.</span>
</p>
<table class="table-table">
    <tr>
        <th class="table-tableCell table-tableCellHeader"
            style="border: 1px solid black; width: 135px; vertical-align: top; text-align: center; background-color: rgb(245, 129, 35);">
            <p class="editor-paragraph" dir="ltr">
                <span style="white-space: pre-wrap; text-align: center">DATE</span>
            </p>
        </th>
        <th class="table-tableCell table-tableCellHeader"
            style="border: 1px solid black; width: 135px; vertical-align: top; text-align: center; background-color: rgb(245, 129, 35);">
            <p class="editor-paragraph" dir="ltr">
                <span style="white-space: pre-wrap; text-align: center">TYPE</span>
            </p>
        </th>
        <th class="table-tableCell table-tableCellHeader"
            style="border: 1px solid black; width: 90px; vertical-align: top; text-align: center; background-color: rgb(245, 129, 35);">
            <p class="editor-paragraph" dir="ltr" style="color:orange;">
                <span style="white-space: pre-wrap;">CONTAINER</span>
            </p>
        </th>
        <th class="table-tableCell table-tableCellHeader"
            style="border: 1px solid black; width: 90px; vertical-align: top; text-align: center; background-color: rgb(245, 129, 35); ">
            <p class="editor-paragraph" dir="ltr">
                <span style="white-space: pre-wrap;">SEAL</span>
            </p>
        </th>
        {{#if visibleTruckNo }}
            <th class="table-tableCell table-tableCellHeader" style="border: 1px solid black; width: 90px; vertical-align: top; text-align: center; background-color: rgb(245, 129, 35);">
                <p class="editor-paragraph" dir="ltr">
                    <span style="white-space: pre-wrap;">TRUCK NO</span>
                </p>
            </th>
        {{/if}}
    </tr>
    {{#each bills as |bill|}}
        <tr>
            <td class="table-tableCell" style="border: 1px solid black; width: 150px; vertical-align: top; text-align: center;">
                <p class="editor-paragraph">
                    <span>{{#if bill.tmsBillForwarderTransport.isImport}} {{dateFormat bill.planDeliverySuccessTime format="dd/MM/yyyy"}} {{else}} {{dateFormat bill.planPickupSuccessTime format="dd/MM/yyyy"}} {{/if}}</span>
                </p>
            </td>
            <td class="table-tableCell" style="border: 1px solid black; width: 150px; vertical-align: top; text-align: center;">
                <p class="editor-paragraph">
                    <span> {{bill.tmsBillForwarderTransport.truckType}}</span>
                </p>
            </td>
            <td class="table-tableCell" style="border: 1px solid black; width: 150px; vertical-align: top; text-align: center; ">
                <p class="editor-paragraph">
                    <span style="white-space: pre-wrap;">{{bill.tmsBillForwarderTransport.containerNo}}</span>
                </p>
            </td>
            <td class="table-tableCell" style="border: 1px solid black; width: 140px; vertical-align: top; text-align: center;">
                <p class="editor-paragraph">
                    <span style="white-space: pre-wrap;">{{bill.tmsBillForwarderTransport.sealNo}}</span>
                </p>
            </td>
            {{#if visibleTruckNo }}
                <td class="table-tableCell" style="border: 1px solid black; width: 235px; vertical-align: top; text-align: center;">
                    <p class="editor-paragraph">
                        <span style="white-space: pre-wrap;">{{bill.tmsBillForwarderTransport.truckNo}}</span>
                    </p>
                </td>
            {{/if}}
        </tr>
    {{/each}}
</table>
<p class="editor-paragraph" dir="ltr">
    <span style="white-space: pre-wrap;">Full return location: {{#if bills.0.tmsBillForwarderTransport.isExport}} {{bills.0.receiver.receiverLocation.shortLabel}} {{bills.0.receiver.receiverContact}} {{else if bills.0.tmsBillForwarderTransport.isImport}} {{bills.0.sender.senderLocation.shortLabel}} {{bills.0.sender.senderContact}}{{/if}} </span>
    <br/>
    <span style="white-space: pre-wrap;">Please check the photos in the attached file carefully . In case of a red declaration, full containers must be asked to lift off at the port of loading.</span>
</p>
<br />
<p class="editor-paragraph" dir="ltr">
    <i style="white-space: pre-wrap;">Thank you for choosing our services. If you would like any further information,
        feel free to contact us!</i>
</p>
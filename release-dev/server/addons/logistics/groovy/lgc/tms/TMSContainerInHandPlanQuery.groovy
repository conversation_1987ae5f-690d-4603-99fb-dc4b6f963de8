package lgc.tms

static def buildSqlQuery() {
  return """
    WITH reference_table AS (
      SELECT
        'TMS Bill'                      AS label,
        ft.seal_no                      AS seal_no,
        bill.plan_delivery_success_time AS date_time,
        bill.receiver_contact           AS delivery_contact,
        bill.receiver_address           AS delivery_address,
        receiver_location.address       AS delivery_location_address,
        receiver_location.state_label   AS delivery_state_label,
        receiver_location.state_id      AS delivery_state_id,
        receiver_location.code          AS delivery_location_code,
        bill.container_in_hand_plan_id
      FROM lgc_tms_bill AS bill
      LEFT JOIN settings_location AS receiver_location ON receiver_location.id = bill.receiver_location_id
      LEFT JOIN lgc_tms_bill_forwarder_transport AS ft ON ft.id = bill.tms_bill_forwarder_transport_id
    UNION ALL
      SELECT
        'Round Used'                  AS label,
        ru.seal_no                    AS seal_no,
        ru.date_time                  AS date_time,
        ru.delivery_contact           AS delivery_contact,
        ru.delivery_address           AS delivery_address,
        delivery_location.address     AS delivery_location_address,
        delivery_location.state_label AS delivery_state_label,
        delivery_location.state_id    AS delivery_state_id,
        delivery_location.code        AS delivery_location_code,
        ru.container_in_hand_plan_id
      FROM lgc_tms_round_used AS ru
      LEFT JOIN settings_location AS delivery_location ON delivery_location.id = ru.delivery_location_id
    UNION ALL
      SELECT
        'Hub'           AS label,
        hub.seal_no     AS seal_no,
        hub.valid_date  AS date_time,
        NULL            AS delivery_contact,
        NULL            AS delivery_address,
        NULL            AS delivery_location_address,
        NULL            AS delivery_state_label,
        NULL            AS delivery_state_id,
        NULL            AS delivery_location_code,
        hub.container_in_hand_plan_id
      FROM lgc_tms_hub AS hub
    )
    SELECT
        ref_table.label                     AS label,
        plan.id                             AS id,
        in_hand.container_no                AS container_no,
        cont.owner_id                       AS container_owner_id,
        cont.owner_label                    AS container_owner_label,
        cont_type.type                      AS container_type,
        plan.from_date                      AS from_date,
        plan.to_date                        AS to_date,
        ref_table.seal_no                   AS seal_no,
        ref_table.date_time                 AS date_time,
        ref_table.delivery_contact          AS delivery_contact,
        ref_table.delivery_address          AS delivery_address,
        ref_table.delivery_location_address AS delivery_location_address,
        ref_table.delivery_state_label      AS delivery_state_label,
        ref_table.delivery_state_id         AS delivery_state_id,
        ref_table.delivery_location_code    AS delivery_location_code
      FROM lgc_tms_container_in_hand_plan AS plan
      LEFT JOIN reference_table AS ref_table ON ref_table.container_in_hand_plan_id = plan.id
      LEFT JOIN lgc_tms_container_in_hand AS in_hand ON in_hand.id = plan.container_in_hand_id
      LEFT JOIN lgc_tms_container AS cont ON cont.id = in_hand.container_id
      LEFT JOIN lgc_tms_container_type AS cont_type ON cont_type.id = cont.container_type_id
      WHERE plan.company_id = :company_id AND plan.container_in_hand_id = :container_in_hand_id
  """;
}

buildSqlQuery()

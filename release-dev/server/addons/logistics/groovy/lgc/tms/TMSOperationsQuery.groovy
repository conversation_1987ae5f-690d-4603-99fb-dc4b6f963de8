package lgc.tms

import net.datatp.module.data.db.entity.ICompany
import net.datatp.module.data.db.query.RangeFilter
import net.datatp.module.data.db.query.SqlQueryParams
import net.datatp.util.text.DateUtil
import net.datatp.util.text.StringUtil

def BUILD_RANGER_FILTER(RangeFilter rangerFilter) {
  StringBuilder b = new StringBuilder();
  b.append("(");
  if(!StringUtil.isEmpty(rangerFilter.getFromValue())) {
    Date date = DateUtil.parseCompactDateTime(rangerFilter.getFromValue());
    b.append( rangerFilter.getName() + " >= " + "'" + date + "'");
  }
  if(!StringUtil.isEmpty(rangerFilter.getToValue())) {
    if(!StringUtil.isEmpty(rangerFilter.getFromValue())) b.append(" AND ");
    Date date = DateUtil.parseCompactDateTime(rangerFilter.getToValue());
    b.append( rangerFilter.getName() + " <= " + "'" + date + "'");
  }
  b.append(")");

  if(StringUtil.isEmpty(rangerFilter.getFromValue()) && StringUtil.isEmpty(rangerFilter.getToValue())) return null;
  return b.toString();
}

def BUILD_RANGER_FILTERS(List<RangeFilter> rangerFilters) {
  if(rangerFilters == null) return "";
  StringBuilder b = new StringBuilder();
  for(RangeFilter sel : rangerFilters) {
    String buildRangeFilter = BUILD_RANGER_FILTER(sel);
    if(buildRangeFilter != null) b.append(" AND " + buildRangeFilter);
  }
  return b.toString();
}

String filterByIds(SqlQueryParams params) {
  if(!params.hasParam("ids")) return "";
  Long [] ids = params.getParam("ids");
  if(ids.length == 0) return "";

  String condition = """
      AND id IN (${params.getParam("ids")})
      """
  condition = condition.replace("[", "").replace("]", "");
  return condition;
}

  SqlQueryParams params = sqlparams;
  def query = """
       WITH bill AS(
            SELECT 
                 bill.id                          AS id,
                 bill.sync_with_origin_tms_bill_authorization AS sync_with_origin_tms_bill_authorization,
                 bill.sync_vendor_bill_authorization_token    AS sync_vendor_bill_authorization_token,
                 bill.origin_total_vendor_attach_files        AS origin_total_vendor_attach_files,
                 bill.label                       AS label,
                 bill.time                        AS time,
                 bill.customer_id                 AS customer_id,
                 bill.customer_full_name          AS customer_full_name,
                 bill.sender_address              AS sender_address,
                 bill.receiver_address            AS receiver_address,
                 bill.plan_delivery_success_time  AS plan_delivery_success_time,
                 bill.plan_pickup_success_time    AS plan_pickup_success_time,
                 bill.responsible_full_name       AS responsible_full_name,
                 bill.responsible_account_id      AS responsible_account_id,
                 bill.quantity                    AS quantity,
                 bill.quantity_unit               AS quantity_unit,
                 bill.weight                      AS weight,
                 bill.weight_unit                 AS weight_unit,
                 bill.volume_as_text              AS volume,
                 bill.volume_unit                 AS volume_unit,
                 bill.origin_vendor_bill_id       AS origin_vendor_bill_id,
                 ft.truck_no                      AS truck_no,
                 ft.mode                          AS mode,
                 ft.warehouse_label               AS warehouse_label,
                 CASE
                  WHEN account_user_profile.nickname IS NOT NULL THEN account_user_profile.nickname
                  ELSE account_user_profile.full_name
                 END                              AS user_name
            FROM lgc_tms_bill bill
            LEFT JOIN lgc_tms_bill_forwarder_transport ft ON bill.tms_bill_forwarder_transport_id = ft.id 
            LEFT JOIN account_account                     ON bill.responsible_account_id          = account_account.id
            LEFT JOIN account_user_profile                ON account_user_profile.login_id        = account_account.login_id
       ), 
       tracking_group_by_tms_bill AS (
                SELECT 
                     tms_bill_id,
                     count(CASE WHEN lgc_fleet_vehicle_trip.vehicle_label <> '' AND lgc_fleet_vehicle_trip.vehicle_label IS NOT NULL THEN 1 END) AS tms_tracking_transporting,
                     string_agg(
                       COALESCE(fleet_label, '') || ' ' ||
                       COALESCE(lgc_fleet_vehicle_trip_goods_tracking.vehicle_type, '') || ' ' ||
                       COALESCE(vehicle_label, '') || ' ' ||
                       COALESCE(driver_full_name, '') || ' Mobile:' ||
                       COALESCE(lgc_fleet_vehicle_trip.mobile, 'N/A') || ' ID:' ||  COALESCE(identification_no, 'N/A'), ', '
                     )                            AS tms_tracking_truck_no,
                    string_agg(
                      COALESCE(file_trucking, ''), ', '
                    )                             AS tms_file_trucking,
                     string_agg(
                        COALESCE(lgc_fleet_vehicle_trip_goods_tracking.description, ''), ' '
                     )                            AS tms_tracking_description
                FROM lgc_fleet_vehicle_trip_goods_tracking
                LEFT JOIN lgc_fleet_vehicle_trip ON lgc_fleet_vehicle_trip_goods_tracking.vehicle_trip_id = lgc_fleet_vehicle_trip.id
                GROUP BY tms_bill_id
       ),
       attachment_group_by_tms_bill AS (
                      SELECT count(tms_bill_id) AS bill_attachment_count, tms_bill_id
                      FROM lgc_tms_bill_attachment
                      GROUP BY tms_bill_id
       ),
       attachment_group_by_ops AS (
                      SELECT count(ops_id) AS bill_attachment_count, ops_id
                      FROM lgc_tms_ops_attachment
                      GROUP BY ops_id
       ),
       vendor_bill_attachment AS (
              SELECT
                vendor_bill_id,
                COUNT(vendor_bill_id) AS vendor_attach_file_total
              FROM lgc_tms_vendor_attachment
              GROUP BY vendor_bill_id
       )
       
       SELECT 
          *
       FROM
       (  
          SELECT 
              ops.id AS id,
              ops.code AS code,
              CASE
                WHEN ops.tms_bill_id IS NULL THEN ops.label
                ELSE bill.label
              END AS label,
              CASE
                WHEN ops.tms_bill_id IS NULL THEN ops.customer_id
                ELSE bill.customer_id
              END AS customer_id,
              CASE
                WHEN ops.tms_bill_id IS NULL THEN ops.customer_full_name
                ELSE bill.customer_full_name
              END AS customer_full_name,
              CASE 
                WHEN ops.tms_bill_id IS null then ops.delivery_plan
                ELSE 
                  CASE
                      WHEN bill.mode = 'IMPORT_FCL' OR bill.mode = 'IMPORT_LCL' OR bill.mode = 'IMPORT_AIR'
                      THEN bill.plan_delivery_success_time
                      ELSE bill.plan_pickup_success_time
                  END
              END AS delivery_plan,        
              CASE
                WHEN ops.tms_bill_id IS NULL THEN ops.responsible_full_name
                ELSE bill.responsible_full_name
              END AS responsible_full_name ,
              CASE
                WHEN ops.tms_bill_id IS NULL THEN ops.responsible_account_id
                ELSE bill.responsible_account_id
              END AS responsible_account_id,
              CASE
                WHEN ops.tms_bill_id IS NULL THEN ops.mode
                ELSE bill.mode
              END AS mode,
              CASE
                WHEN ops.tms_bill_id IS NULL THEN ops.quantity
                ELSE bill.quantity
              END AS quantity,
              CASE
                WHEN ops.tms_bill_id IS NULL THEN ops.truck_no
                WHEN tracking_group_by_tms_bill.tms_tracking_truck_no IS NOT NULL THEN tracking_group_by_tms_bill.tms_tracking_truck_no
                ELSE bill.truck_no   
              END AS truck_no,
              CASE
                WHEN ops.tms_bill_id IS NULL THEN ops.warehouse_label
                ELSE bill.warehouse_label   
              END AS warehouse_label,
              CASE
                WHEN ops.tms_bill_id IS NOT NULL THEN bill.user_name
                ELSE
                    CASE
                        WHEN account_user_profile.nickname IS NOT NULL THEN account_user_profile.nickname
                        ELSE account_user_profile.full_name
                    END 
              END AS user_name, 
              CASE
                  WHEN ops.status = 'NEED_CONFIRM' THEN 1
                  WHEN ops.status = 'PROCESSING' THEN 2
                  ELSE 3
              END AS status_number,
              CASE
                  WHEN ops.mode = 'EXPORT_FCL' THEN 1
                  WHEN ops.mode = 'EXPORT_LCL' THEN 2
                  WHEN ops.mode = 'EXPORT_AIR' THEN 3
                  WHEN ops.mode = 'IMPORT_FCL' THEN 4
                  WHEN ops.mode = 'IMPORT_LCL' THEN 5
                  WHEN ops.mode = 'IMPORT_AIR' THEN 6
                  ELSE 7
              END AS mode_number,
              lgc_tms_vendor_bill.id            AS vendor_bill_id,
              bill.sync_with_origin_tms_bill_authorization AS sync_with_origin_tms_bill_authorization,
              bill.sync_vendor_bill_authorization_token    AS sync_vendor_bill_authorization_token,
              bill.origin_total_vendor_attach_files        AS origin_total_vendor_attach_files,
              bill.origin_vendor_bill_id        AS origin_vendor_bill_id,
              bill.user_name                    AS user_name,
              bill.user_name                    AS user_name,
              bill.time                         AS time,
              bill.sender_address               AS sender_address,
              bill.receiver_address             AS receiver_address,
              bill.quantity_unit                AS tms_bill_quantity_unit,
              bill.weight                       AS tms_bill_weight,
              bill.weight_unit                  AS tms_bill_weight_unit,
              bill.volume                       AS tms_bill_volume,
              bill.volume_unit                  AS tms_Bill_volume_unit,
              ops.note                          AS note,
              ops.ops_account_id                AS ops_account_id,
              ops.ops_account_full_name         AS ops_account_full_name,
              ops.ops_account_mobile            AS ops_account_mobile,
              ops.identification_no             AS identification_no,
              ops.status                        AS status,
              ops.tms_bill_id                   AS tms_bill_id,
              ops.version                       AS version,
              ops.company_id                    AS company_id,
              ops.created_by                    AS created_by,
              ops.created_time                  AS created_time,
              ops.modified_by                   AS modified_by,
              ops.modified_time                 AS modified_time,
              ops.storage_state                 AS storage_state,
              tracking_group_by_tms_bill.tms_tracking_transporting,
              tracking_group_by_tms_bill.tms_tracking_description,
              tracking_group_by_tms_bill.tms_file_trucking,
              CASE
                  WHEN ops.tms_bill_id IS NOT NULL THEN attachment_group_by_tms_bill.bill_attachment_count
                  ELSE attachment_group_by_ops.bill_attachment_count
              END bill_attachment_count,
              vendor_bill_attachment.vendor_attach_file_total AS vendor_attach_file_total
          FROM lgc_tms_operations ops 
          LEFT JOIN bill ON ops.tms_bill_id = bill.id
          LEFT JOIN lgc_tms_vendor_bill ON (ops.tms_bill_id = lgc_tms_vendor_bill.tms_bill_id OR bill.origin_vendor_bill_id = lgc_tms_vendor_bill.id)
          LEFT JOIN tracking_group_by_tms_bill ON tracking_group_by_tms_bill.tms_bill_id = ops.tms_bill_id
          LEFT JOIN account_account ON ops.responsible_account_id = account_account.id
          LEFT JOIN account_user_profile ON account_user_profile.login_id = account_account.login_id 
          LEFT JOIN attachment_group_by_tms_bill ON attachment_group_by_tms_bill.tms_bill_id = bill.id 
          LEFT JOIN vendor_bill_attachment ON vendor_bill_attachment.vendor_bill_id = lgc_tms_vendor_bill.id  
          LEFT JOIN attachment_group_by_ops ON attachment_group_by_ops.ops_id = ops.id 
       ) AS tms_operations
       WHERE (label LIKE :filter_val OR :filter_val IS NULL)
          AND storage_state = COALESCE(:storage_state, storage_state)
          AND (company_id = :company_id)
          AND ( :data_scope = 'Company' OR (:data_scope = 'Owner' AND (ops_account_id = :account_id OR responsible_account_id = :account_id )))
       ${BUILD_RANGER_FILTERS(params.getRangeFilters())}
       ${filterByIds(params)}
       ORDER BY delivery_plan DESC, status_number, warehouse_label, ops_account_full_name, customer_full_name ,label, mode_number 
       LIMIT :max_return
  """
  return query;
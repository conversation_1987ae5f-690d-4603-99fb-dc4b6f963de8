package lgc.forwarder.mngt.report

static def buildQuery() {
  return """
    SELECT
      0 AS bill_type_order,
      booking.booking_case_reference as master_case_reference,
      bp.close_date as close_date,
      NULL as mawb_no,
      h.code as hawb_no,
      NULL as master_bill_id,
      h.id as house_bill_id,
      h.booking_process_id as booking_process_id,
      h.shipment_type as shipment_type,
      h.mode as mode,
      h.issued_date as issued_date,
      h.purpose as purpose,
      NULL as carrier_partner_id,
      NULL as carrier_label,
      h.client_partner_id as client_partner_id,
      h.client_label as client_label,
      h.shipper_partner_id as shipper_partner_id,
      h.shipper_label as shipper_label,
      h.consignee_partner_id as consignee_partner_id,
      h.consignee_label as consignee_label,
      h.handling_agent_partner_id as handling_agent_partner_id,
      h.handling_agent_label as handling_agent_label,
      h.sales_employee_id as sale_employee_id,
      h.sales_label as sale_label,
      h.assignee_employee_id,
      h.assignee_label,
      NULL as container,
      NULL as tues,
      h.note as note,
      0 as mbl_total_gross_weight,
      0 as mbl_total_chargeable_weight,
      NULL as mbl_weight_unit,
      0 as mbl_total_volume,
      NULL as mbl_volume_unit,
      h.cargo_gross_weight as hbl_total_gross_weight,
      h.cargo_chargeable_weight as hbl_total_chargeable_weight,
      h.cargo_weight_unit as hbl_weight_unit,
      h.cargo_volume as hbl_total_volume,
      h.cargo_volume_unit as hbl_volume_unit,
      t.from_location_code,
      t.to_location_code,
      t.depart_time,
      t.arrival_time,
      SUM(
        CASE
          WHEN (i.payer_partner_id IN (:company_partner_ids) OR i.payee_partner_id IN (:company_partner_ids)) AND i.currency = 'USD'
            THEN CASE i.cash_flow WHEN 'Inbound' THEN i.total END
          ELSE 0
        END
      ) AS usd_total_revenue,
      SUM(
        CASE
          WHEN (i.payer_partner_id IN (:company_partner_ids) OR i.payee_partner_id IN (:company_partner_ids)) AND i.currency = 'USD'
            THEN CASE i.cash_flow WHEN 'Outbound' THEN i.total END
          ELSE 0
        END
      ) AS usd_total_costing,
      0 as mbl_total_revenue,
      0 as mbl_total_sale_tax,
      0 as mbl_total_costing,
      0 as mbl_total_purchase_tax,
      SUM(
        CASE
          WHEN i.payer_partner_id IN (:company_partner_ids) OR i.payee_partner_id IN (:company_partner_ids)
            THEN CASE i.cash_flow WHEN 'Inbound' THEN i.domestic_total END
          ELSE 0
        END
      ) AS hbl_total_revenue,
      SUM(
        CASE
          WHEN i.payer_partner_id IN (:company_partner_ids) OR i.payee_partner_id IN (:company_partner_ids)
            THEN CASE i.cash_flow WHEN 'Inbound' THEN i.domestic_total_tax END
          ELSE 0
        END
      ) AS hbl_total_sale_tax,
      SUM(
        CASE
          WHEN i.payer_partner_id IN (:company_partner_ids) OR i.payee_partner_id IN (:company_partner_ids)
            THEN CASE i.cash_flow WHEN 'Outbound' THEN i.domestic_total END
          ELSE 0
        END
      ) AS hbl_total_costing,
      SUM(
        CASE
          WHEN i.payer_partner_id IN (:company_partner_ids) OR i.payee_partner_id IN (:company_partner_ids)
            THEN CASE i.cash_flow WHEN 'Outbound' THEN i.domestic_total_tax END
          ELSE 0
        END
      ) AS hbl_total_purchase_tax,
      SUM(
        CASE
          WHEN i.payer_partner_id IN (:company_partner_ids)
            THEN CASE i.cash_flow WHEN 'Internal' THEN i.domestic_total END
          ELSE 0
        END
      ) AS hbl_internal_total_costing,
      SUM(
        CASE
          WHEN i.payer_partner_id IN (:company_partner_ids)
            THEN CASE i.cash_flow WHEN 'Internal' THEN i.domestic_total_tax END
          ELSE 0
        END
      ) AS hbl_internal_total_purchase_tax,
      SUM(
        CASE
          WHEN i.payer_partner_id IN (:company_partner_ids) OR i.payee_partner_id IN (:company_partner_ids) THEN
            CASE
              WHEN i.cash_flow = 'Inbound' OR (i.cash_flow = 'Internal' AND i.payee_partner_id IN (:company_partner_ids))
                THEN i.domestic_total
              WHEN i.cash_flow = 'Outbound' OR (i.cash_flow = 'Internal' AND i.payer_partner_id IN (:company_partner_ids))
                THEN -i.domestic_total
              ELSE 0
            END
          ELSE 0
        END
      ) AS hbl_total_net_profit,
      SUM(
        CASE
          WHEN i.payer_partner_id NOT IN (:company_partner_ids) AND i.payee_partner_id NOT IN (:company_partner_ids)
            THEN i.domestic_final_charge ELSE 0
        END
      ) AS hbl_on_be_half,
      com.label as company_label,
      com.code as company_code
    FROM lgc_mgmt_truck_house_bill h
      LEFT JOIN company_company com ON com.id = h.company_id
      INNER JOIN lgc_mgmt_order_transport_plan t ON t.order_process_id = h.order_process_id
      LEFT JOIN accounting_invoice_link l ON l.link_name = 'lgc_mgmt_truck_house_bill' AND l.entity_id = h.id
      INNER JOIN accounting_invoice i ON l.invoice_id = i.id
      INNER JOIN lgc_mgmt_booking_process bp ON bp.id = h.booking_process_id
      INNER JOIN lgc_sales_booking booking ON booking.id = bp.booking_id
      WHERE h.company_id IN (:company_ids)
        AND h.issued_date >= COALESCE(:issued_date_from, h.issued_date)
        AND h.issued_date <= COALESCE(:issued_date_to, h.issued_date)
        AND bp.close_date >= COALESCE(:closed_date_from, bp.close_date)
        AND bp.close_date <= COALESCE(:closed_date_to, bp.close_date)
        AND (bp.mode = 'TRUCK_REGULAR' OR bp.mode = 'TRUCK_CONTAINER')
    GROUP BY h.id, bp.close_date, booking.booking_case_reference, t.from_location_code,
      t.to_location_code,t.depart_time, t.arrival_time, com.label, com.code
    """
}

buildQuery()
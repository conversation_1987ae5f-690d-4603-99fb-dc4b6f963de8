package lgc.forwarder.mngt.general

/**
 * Build an SQL query to retrieve data for the house bill list screen.
 *
 * @param {@code table_name} House Bill table name.
 * @param {@code customSelectField} add transportation mode column
 * @param {@code domain} The conditions, filters of the query.
 *
 * @return A string containing the SQL query.
 */

def buildSqlQuery() {
    return """
       SELECT
          '${table_name}' as table_name,
          ${customSelectField}
          hb.id as bill_id,
          hb.code as hawb_no,
          hb.issued_date as issued_date,
          hb.purpose as purpose,
          hb.client_partner_id as client_partner_id,
          hb.client_label as client_label,
          hb.shipment_type as shipment_type,
          hb.sales_employee_id as sales_employee_id,
          hb.sales_label as sales_label,
          hb.payment_term as payment_term,
          hb."source"  as "source",
          hb.package_quantity as package_quantity,
          hb.packaging_type as packaging_type,
          hb.cargo_gross_weight as total_gross_weight,
          hb.cargo_chargeable_weight as total_chargeable_weight,
          hb.cargo_volume as total_volume,
          hb.desc_of_goods as desc_of_goods,
          hb.note as note,
          hb.warehouse_location_config_id as warehouse_location_config_id,
          hb.warehouse_location_config_label as warehouse_location_config_label,
          hb.order_process_id as order_process_id,
          hb.created_by as created_by,
          hb.created_time as created_time,
          hb.modified_by as modified_by,
          hb.modified_time as modified_time,
          t.from_location_code,
          t.to_location_code,
          t.depart_time,
          t.arrival_time,
          booking.booking_case_reference as case_reference,
          booking.id as booking_id,
          bp.id as booking_process_id,
          bp.close_date as closed_date,
          bp.edit_mode as edit_mode,
          bp.state as state,

          -- Report profit by invoice
          SUM(
              CASE
                  WHEN i.payer_partner_id IN (:company_partner_id) OR i.payee_partner_id IN (:company_partner_id)
                      THEN CASE i.cash_flow WHEN 'Inbound' THEN i.domestic_total END
                  ELSE 0
              END
          ) AS total_revenue,
          SUM(
              CASE
                  WHEN i.payer_partner_id IN (:company_partner_id) OR i.payee_partner_id IN (:company_partner_id)
                      THEN CASE i.cash_flow WHEN 'Outbound' THEN i.domestic_total END
                  ELSE 0
              END
          ) AS total_costing,
          SUM(
              CASE
                  WHEN i.payer_partner_id IN (:company_partner_id) OR i.payee_partner_id IN (:company_partner_id) THEN
                      CASE
                          WHEN i.cash_flow = 'Inbound' OR (i.cash_flow = 'Internal' AND i.payee_partner_id IN (:company_partner_id))
                              THEN i.domestic_total
                          WHEN i.cash_flow = 'Outbound' OR (i.cash_flow = 'Internal' AND i.payer_partner_id IN (:company_partner_id))
                              THEN -i.domestic_total
                          ELSE 0
                      END
                  ELSE 0
              END
          ) AS gross_profit,
          SUM(
              CASE
                 WHEN i.payer_partner_id NOT IN (:company_partner_id) AND i.payee_partner_id NOT IN (:company_partner_id)
                    THEN i.domestic_final_charge ELSE 0
              END
          ) AS on_behalf
      FROM ${table_name} hb
          LEFT JOIN lgc_mgmt_order_transport_plan t ON t.order_process_id = hb.order_process_id
          LEFT JOIN accounting_invoice_link l ON l.link_name = '${table_name}' AND l.entity_id = hb.id
          LEFT JOIN accounting_invoice i ON l.invoice_id = i.id
          INNER JOIN lgc_mgmt_booking_process bp ON bp.id = hb.booking_process_id
          INNER JOIN lgc_sales_booking booking ON booking.id = bp.booking_id
      WHERE hb.company_id = :company_id
          ${domain}
      GROUP BY hb.id, t.from_location_code,t.to_location_code, t.depart_time, t.arrival_time,
          booking.booking_case_reference, bp.close_date, bp.edit_mode, bp.state, bp.id, booking.id
    """
}
buildSqlQuery()
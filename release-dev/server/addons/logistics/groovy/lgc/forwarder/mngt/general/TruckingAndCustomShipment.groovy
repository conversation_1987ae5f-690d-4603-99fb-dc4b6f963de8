package lgc.forwarder.mngt.general
/**
 * Build an SQL query to retrieve data for the shipment list screen.
 * For trucking and logistics, fetch from the booking process table.
 * For sea, air, and rail shipments, gather information from the master bill table.
 *
 * @param {@code domain} The conditions, filters of the query.
 *
 * @return A string containing the SQL query.
 */

def buildQuery() {
    return """
        SELECT
            'lgc_mgmt_booking_process' as table_name,
            bp.id as bill_id,
            b.booking_case_reference as case_reference,
            bp."mode"  as "mode",
            bp."type"  as "type",
            '' as mawb_no,
            COALESCE(hbs.hawb_no, '') AS hawb_no,
            first_hb.issued_date as issued_date,
            bp.close_date  as closed_date,
            'DOMESTIC' as purpose,
            first_hb.shipment_type as shipment_type,
            0 as carrier_partner_id,
            '' as carrier_label,
            po.assignee_employee_id as assignee_employee_id,
            po.assignee_label as assignee_label,
            0 as package_quantity,
            'PACKAGES' as packaging_type,
            0 as total_gross_weight,
            0 as total_chargeable_weight,
            0 as total_volume,
            bp.edit_mode as edit_mode,
            bp.state as state,
            bp.input_source as input_source,
            bp.description as note,
            bp.created_by as created_by,
            bp.created_time as created_time,
            bp.modified_by as modified_by,
            bp.modified_time as modified_time
          FROM lgc_mgmt_booking_process bp
              INNER JOIN lgc_sales_booking b
                  ON b.id = bp.booking_id
              INNER JOIN lgc_mgmt_purchase_order po
                  ON po.id = bp.purchase_order_id
              FULL JOIN (
                  SELECT
                      hb.company_id,
                      hb.booking_process_id,
                      STRING_AGG(hb.code, ', ') AS hawb_no
                  FROM (
                      SELECT booking_process_id, company_id, code FROM lgc_mgmt_truck_house_bill
                      UNION ALL
                      SELECT booking_process_id, company_id, code FROM lgc_mgmt_cc_house_bill
                  ) hb
                  GROUP BY hb.booking_process_id, hb.company_id
              ) AS hbs
                  ON hbs.booking_process_id = bp.id
                  AND hbs.company_id = bp.company_id
              FULL JOIN (
                  SELECT
                      company_id,
                      booking_process_id,
                      issued_date,
                      shipment_type
                  FROM (
                      SELECT
                          hb.company_id,
                          hb.booking_process_id,
                          hb.issued_date,
                          hb.shipment_type,
                          ROW_NUMBER() OVER(PARTITION BY hb.company_id, hb.booking_process_id ORDER BY hb.booking_process_id) AS rn
                      FROM (
                          SELECT booking_process_id, company_id, code, issued_date, shipment_type FROM lgc_mgmt_truck_house_bill
                          UNION ALL
                          SELECT booking_process_id, company_id, code, issued_date, shipment_type FROM lgc_mgmt_cc_house_bill
                      ) as hb
                  ) AS numbered
                  WHERE rn = 1 AND company_id = :company_id
              ) as first_hb ON first_hb.booking_process_id = bp.id AND first_hb.company_id = bp.company_id
          WHERE bp.company_id = :company_id
              AND (bp.mode IN ('TRUCK_CONTAINER', 'TRUCK_REGULAR') OR bp.type IS NOT NULL)
              ${domain}
    """
}
buildQuery()
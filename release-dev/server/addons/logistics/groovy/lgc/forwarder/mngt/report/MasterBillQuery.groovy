
package lgc.forwarder.mngt.report


def buildMasterBillSqlQuery() {
  return """
    SELECT
      1 AS bill_type_order,
      m.code as master_case_reference,
      m.close_date as close_date,
      m.master_bill_no as mawb_no,
      NULL as hawb_no,
      m.id as master_bill_id,
      NULL as house_bill_id,
      NULL as booking_process_id,
      m.shipment_type as shipment_type,
      m.transportation_mode as mode,
      m.issued_date as issued_date,
      m.purpose as purpose,
      m.carrier_partner_id as carrier_partner_id,
      m.carrier_label as carrier_label,
      NULL as client_partner_id,
      NULL as client_label,
      m.master_shipper_partner_id as shipper_partner_id,
      m.master_shipper_label as shipper_label,
      m.master_consignee_partner_id as consignee_partner_id,
      m.master_consignee_label as consignee_label,
      NULL as handling_agent_partner_id,
      NULL as handling_agent_label,
      NULL as sale_employee_id,
      NULL as sale_label,
      m.assignee_employee_id as assignee_employee_id,
      m.assignee_label as assignee_label,
      NULL as container,
      NULL as tues,
      m.note as note,
      m.gross_weight_in_kgs as mbl_total_gross_weight,
      m.chargeable_weight_in_kgs as mbl_total_chargeable_weight,
      'KGS' as mbl_weight_unit,
      m.volume_in_cbm as mbl_total_volume,
      'CBM' as mbl_volume_unit,
      0 as hbl_total_gross_weight,
      0 as hbl_total_chargeable_weight,
      NULL as hbl_weight_unit,
      0 as hbl_total_volume,
      NULL as hbl_volume_unit,
      t.from_location_code,
      t.to_location_code,
      t.depart_time,
      t.arrival_time,
      SUM(
        CASE
          WHEN
            (i.payer_partner_id IN (:company_partner_ids) OR i.payee_partner_id IN (:company_partner_ids))
            AND i. currency = 'USD'
            THEN CASE i.cash_flow WHEN 'Inbound' THEN i.total END
          ELSE 0
        END
      ) AS usd_total_revenue,
      SUM(
        CASE
          WHEN
            (i.payer_partner_id IN (:company_partner_ids) OR i.payee_partner_id IN (:company_partner_ids))
            AND i.currency = 'USD'
              THEN CASE i.cash_flow WHEN 'Outbound' THEN i.total END
          ELSE 0
        END
      ) AS usd_total_costing,
      SUM(
        CASE
          WHEN i.payer_partner_id IN (:company_partner_ids) OR i.payee_partner_id IN (:company_partner_ids)
            THEN CASE i.cash_flow WHEN 'Inbound' THEN i.domestic_total END
          ELSE 0
        END
      ) AS mbl_total_revenue,
      SUM(
        CASE
          WHEN i.payer_partner_id IN (:company_partner_ids) OR i.payee_partner_id IN (:company_partner_ids)
            THEN CASE i.cash_flow WHEN 'Inbound' THEN i.domestic_total_tax END
          ELSE 0
        END
      ) AS mbl_total_sale_tax,
      SUM(
        CASE
          WHEN i.payer_partner_id IN (:company_partner_ids) OR i.payee_partner_id IN (:company_partner_ids)
            THEN CASE i.cash_flow WHEN 'Outbound' THEN i.domestic_total END
          ELSE 0
        END
      ) AS mbl_total_costing,
      SUM(
        CASE
          WHEN i.payer_partner_id IN (:company_partner_ids) OR i.payee_partner_id IN (:company_partner_ids)
            THEN CASE i.cash_flow WHEN 'Outbound' THEN i.domestic_total_tax END
          ELSE 0
        END
      ) AS mbl_total_purchase_tax,
      0 as hbl_total_revenue,
      0 as hbl_total_sale_tax,
      0 as hbl_total_costing,
      0 as hbl_total_purchase_tax,
      0 as hbl_internal_total_costing,
      0 as hbl_internal_total_purchase_tax,
      0 as hbl_total_net_profit,
      0 as hbl_on_be_half,
      com.label as company_label,
      com.code as company_code
    FROM ${table_name} m
      LEFT JOIN company_company com ON com.id = m.company_id
      LEFT JOIN accounting_invoice_link l ON l.link_name = '${table_name}' AND l.entity_id = m.id
      LEFT JOIN accounting_invoice i ON l.invoice_id = i.id
      INNER JOIN lgc_mgmt_master_bill_transport_plan t ON t.id = m.${plan_foreign_key_column}
    WHERE m.id IN (
      SELECT temp.bill_id FROM master_bill_temp temp WHERE temp.table_name = '${table_name}'
    )
    GROUP BY m.id, t.from_location_code,t.to_location_code,t.depart_time, t.arrival_time, com.label, com.code
  """
}
buildQuery()
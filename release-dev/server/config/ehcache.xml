<?xml version="1.0" encoding="UTF-8"?>
<config
  xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'
  xmlns:jsr107='http://www.ehcache.org/v3/jsr107'
  xmlns='http://www.ehcache.org/v3'
  xsi:schemaLocation=" http://www.ehcache.org/v3 http://www.ehcache.org/schema/ehcache-core-3.1.xsd http://www.ehcache.org/v3/jsr107 http://www.ehcache.org/schema/ehcache-107-ext-3.1.xsd">

  <service>
    <jsr107:defaults default-template="default" enable-management="false" enable-statistics="true">
      <jsr107:cache name="generic" template="default"/>
      <jsr107:cache name="entity" template="entity"/>
    </jsr107:defaults>
  </service>

  <persistence directory="${app.instance.dir}/tmp/ehcache"/>

  <cache-template name="default">
    <resources>
      <heap unit="MB">50</heap>
      <offheap unit="MB">100</offheap>
      <disk unit="MB" persistent="true">1024</disk>
    </resources>
  </cache-template>

  <cache alias="generic" uses-template="default"/>
  <cache alias="entity" uses-template="default"/>
</config>

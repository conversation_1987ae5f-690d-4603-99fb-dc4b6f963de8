<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <property name="LOG_FILE" value="${app.instance.log.dir}/server.log}"/>

  <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
  <include resource="org/springframework/boot/logging/logback/console-appender.xml" />
  <include resource="org/springframework/boot/logging/logback/file-appender.xml" />

  <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
    <resetJUL>true</resetJUL>
  </contextListener>

  <root level="INFO">
    <appender-ref ref="CONSOLE" />
    <appender-ref ref="FILE" />
  </root>
  <logger name="org.hibernate" level="DEBUG"/>
  <logger name="org.springframework" level="DEBUG"/>
  <logger name="net.datatp" level="INFO"/>
  <logger name="cloud.datatp" level="INFO"/>
</configuration>

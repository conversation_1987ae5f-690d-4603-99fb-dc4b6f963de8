app:
  env: dev

logging:
  config: file:../server-env/config/datatp-logback-console.xml

server:
  servlet:
    session:
      timeout: 5m

---
spring:
  profiles:
    group:
      dev: database, db-schema-update, data, log-info-file
---
spring:
  profiles:
    group:
      dev-console: database, db-schema-validate, data
---
spring:
  profiles:
    group:
      dev-new: database, db-schema-update, data

---
spring:
  profiles:
    group:
      dev-update: database, db-schema-update, data

---
spring:
  config:
    activate:
      on-profile: database
  datasource:
    server:
      host: localhost
      port: 5432
    jdbc:
      type: com.zaxxer.hikari.HikariDataSource
      pool-name: jdbc
      connectionTimeout: 30000 
      idleTimeout: 600000 
      maxLifetime: 600000
      minimumIdle: 3
      maximumPoolSize: 15
      auto-commit: false
      driverClassName: org.postgresql.Driver
      jdbcUrl: jdbc:postgresql://${spring.datasource.server.host}:${spring.datasource.server.port}/datatp_basic_db
      username: datatp
      password: datatp
    rw:
      type: com.zaxxer.hikari.HikariDataSource
      pool-name: rw
      connectionTimeout: 45000 
      idleTimeout: 600000 
      maxLifetime: 600000
      minimumIdle: 15
      maximumPoolSize: 30
      leakDetectionThreshold: 30000
      auto-commit: false
      driverClassName: org.postgresql.Driver
      jdbcUrl: jdbc:postgresql://${spring.datasource.server.host}:${spring.datasource.server.port}/datatp_basic_db
      username: datatp
      password: datatp
    hibernate:
      show_sql: false
      dialect: org.hibernate.dialect.PostgreSQLDialect
      hbm2ddl:
        auto: update
      schema_update:
        unique_constraint_strategy: RECREATE_QUIETLY
  batch:
    datasource:
      db-type: postgresql

---
spring:
  config:
    activate:
      on-profile: db-schema-validate
  datasource:
    hibernate:
      hbm2ddl:
        auto: validate

---
spring:
  config:
    activate:
      on-profile: db-schema-update
  datasource:
    hibernate:
      hbm2ddl:
        auto: update


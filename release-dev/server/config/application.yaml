# Build Configuration  ############################

build: 
  version: <<build.version>>

# Server Configuration ############################

server:
  port: 7080
  http2:
    enabled: true
  compression:
    enabled: true
    min-response-size: 64
    # Comma-separated list of MIME types that should be compressed.
    mime-types: text/html,text/xml,text/plain,text/css,application/json,application/xml,application/javascript
  servlet:
    encoding:
      charset: UTF-8
      force: true
    contextPath: /
    session:
      timeout: 90m
      cookie:
        #same-site: None;Secure
        same-site: lax
  jetty:
    max-connections: 200
    threads:
      acceptors: -1
# Spring Configuration ############################

spring:
  main:
    banner-mode: off
    allow-bean-definition-overriding: false
    allow-circular-references: true
    web-application-type: servlet
  output:
    ansi:
      enabled: never
  
  servlet:
    multipart:
      enabled: true
      file-size-threshold: 2KB
      max-file-size: 200MB
      max-request-size: 215MB
  datasource:
    jdbc:
      hikari:
        connectionTimeout: 30000
        idleTimeout: 600000
        maxLifetime: 1800000
      type: com.zaxxer.hikari.HikariDataSource
      jdbcUrl: jdbc:h2:${app.data.dir}/data/db/h2/ofonedb;DATABASE_TO_LOWER=TRUE;CASE_INSENSITIVE_IDENTIFIERS=TRUE;
      driverClassName: org.h2.Driver
      username: sa
      password:
      auto-commit: true
    rw:
      type: com.zaxxer.hikari.HikariDataSource
      hikari:
        connectionTimeout: 30000 
        idleTimeout: 600000 
        maxLifetime: 600000
      auto-commit: false
      jdbcUrl: jdbc:h2:${app.data.dir}/data/db/h2/ofonedb;DATABASE_TO_LOWER=TRUE;CASE_INSENSITIVE_IDENTIFIERS=TRUE;
      driverClassName: org.h2.Driver
      username: sa
      password: 
    bfsone-report:
      connection-timeout: 30000
      idleTimeout: 600000
      maxLifetime: 600000
      minimumIdle: 2
      maximumPoolSize: 5
      auto-commit: false
      driverClassName: org.postgresql.Driver
      jdbcUrl: jdbc:postgresql://${spring.datasource.server.host}:${spring.datasource.server.port}/bee_legacy_db
      username: sa
      password:
    hibernate:
      show_sql: false
      dialect: org.hibernate.dialect.H2Dialect
      hbm2ddl:
        auto: update
  jpa:
    open-in-view: false
    database-platform: org.hibernate.dialect.H2Dialect

  batch:
    jdbc:
      initialize-schema: never
    job:
      enabled: false
  h2:
    console:
      enabled: true
      path: /h2-console

# Hibernate And DB  Configuration #######################
hibernate:
  jdbc:
    batch_size: 30
    batch_versioned_data: true
  order_inserts: true
  order_updates: true

# Log Configuration ###########################

logging:
  config: classpath:logback-console.xml

# App Configuration ###########################
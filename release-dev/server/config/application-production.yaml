spring:
  profiles:
    group:
      production: database, db-schema-update, log-info-file
---
spring:
  profiles:
    group:
      production-console: database, db-schema-validate
---
spring:
  profiles:
    group:
      production-new: database, db-schema-update

---
spring:
  profiles:
    group:
      production-update: database, db-schema-update

---
spring:
  config:
    activate:
      on-profile: database
  datasource:
    server:
      host: localhost
      port: 5432
    jdbc:
      connection-timeout: 30000
      idleTimeout: 600000
      maxLifetime: 600000
      minimumIdle: 2
      maximumPoolSize: 5
      auto-commit: false
      driverClassName: org.postgresql.Driver
      jdbcUrl: jdbc:postgresql://${spring.datasource.server.host}:${spring.datasource.server.port}/ofonedb
      username: ofone
      password: ofone
    rw:
      type: com.zaxxer.hikari.HikariDataSource
      connectionTimeout: 30000
      idleTimeout: 600000
      maxLifetime: 600000
      minimumIdle: 5
      maximumPoolSize: 15
      auto-commit: false
      driverClassName: org.postgresql.Driver
      jdbcUrl: jdbc:postgresql://${spring.datasource.server.host}:${spring.datasource.server.port}/ofonedb
      username: ofone
      password: ofone
    bfsone-report:
      connection-timeout: 30000
      idleTimeout: 600000
      maxLifetime: 600000
      minimumIdle: 2
      maximumPoolSize: 5
      auto-commit: false
      driverClassName: org.postgresql.Driver
      jdbcUrl: jdbc:postgresql://${spring.datasource.server.host}:${spring.datasource.server.port}/bee_legacy_db
      username: sa
      password:

    hibernate:
      show_sql: false
      dialect: org.hibernate.dialect.PostgreSQLDialect
      hbm2ddl:
        auto: validate
  batch:
    datasource:
      db-type: postgresql
  h2:
    console:
      enabled: false

---
spring:
  config:
    activate:
      on-profile: db-schema-validate
  datasource:
    hibernate:
      hbm2ddl:
        auto: validate

---
spring:
  config:
    activate:
      on-profile: db-schema-update
  datasource:
    hibernate:
      hbm2ddl:
        auto: update

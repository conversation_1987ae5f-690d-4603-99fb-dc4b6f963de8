spring:
  config:
    activate:
      on-profile: data

app:
  data:
    init-sample: true
    init-demo: false

---
spring:
  config:
    activate:
      on-profile: db-schema-update


hibernate:
  hbm2ddl:
    auto: update

---
spring:
  config:
    activate:
      on-profile: db-schema-validate


hibernate:
  hbm2ddl:
    auto: validate

---
spring:
  config:
    activate:
      on-profile: log-info-file

logging:
  config: classpath:logback-file.xml

---
spring:
  config:
    activate:
      on-profile: log-debug

logging:
  config: classpath:logback-console-debug.xml

---
spring:
  config:
    activate:
      on-profile: log-debug-all

logging:
  config: classpath:logback-console-debug-all.xml

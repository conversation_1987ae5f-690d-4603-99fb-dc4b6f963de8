2025-06-18T10:50:31.755+07:00  INFO 5667 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 5667 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-06-18T10:50:31.756+07:00  INFO 5667 --- [main] net.datatp.server.ServerApp              : The following 3 profiles are active: "core", "logistics", "dev"
2025-06-18T10:50:32.479+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.581+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 98 ms. Found 22 JPA repository interfaces.
2025-06-18T10:50:32.593+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.596+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-06-18T10:50:32.596+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.600+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-06-18T10:50:32.600+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.604+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-06-18T10:50:32.615+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.620+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 1 JPA repository interface.
2025-06-18T10:50:32.630+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.635+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-06-18T10:50:32.638+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.641+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-06-18T10:50:32.642+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.642+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-06-18T10:50:32.642+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.648+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-06-18T10:50:32.653+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.659+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-06-18T10:50:32.664+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.666+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-06-18T10:50:32.667+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.670+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-06-18T10:50:32.672+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.679+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-06-18T10:50:32.680+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.683+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-06-18T10:50:32.683+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.683+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-06-18T10:50:32.683+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.684+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-06-18T10:50:32.684+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.688+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-06-18T10:50:32.688+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.689+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-06-18T10:50:32.689+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.689+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-06-18T10:50:32.690+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.699+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-06-18T10:50:32.708+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.714+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-06-18T10:50:32.714+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.717+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-06-18T10:50:32.717+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.721+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-06-18T10:50:32.722+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.727+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-06-18T10:50:32.727+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.731+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-06-18T10:50:32.731+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.734+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-06-18T10:50:32.737+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.742+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 6 JPA repository interfaces.
2025-06-18T10:50:32.742+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.744+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-06-18T10:50:32.746+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.755+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-06-18T10:50:32.761+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.762+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-06-18T10:50:32.762+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.777+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 23 JPA repository interfaces.
2025-06-18T10:50:32.787+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.798+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 19 JPA repository interfaces.
2025-06-18T10:50:32.798+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.806+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-06-18T10:50:32.810+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.846+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 67 JPA repository interfaces.
2025-06-18T10:50:32.846+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.849+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-06-18T10:50:32.849+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-18T10:50:32.852+07:00  INFO 5667 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-06-18T10:50:33.039+07:00  INFO 5667 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-06-18T10:50:33.043+07:00  INFO 5667 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-06-18T10:50:33.314+07:00  WARN 5667 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-18T10:50:33.518+07:00  INFO 5667 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-06-18T10:50:33.520+07:00  INFO 5667 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-06-18T10:50:33.533+07:00  INFO 5667 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-06-18T10:50:33.533+07:00  INFO 5667 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1669 ms
2025-06-18T10:50:33.582+07:00  INFO 5667 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-06-18T10:50:33.784+07:00  INFO 5667 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection conn0: url=jdbc:h2:/Users/<USER>/nez/code/datatp/working/release-dev/server/../server-data/data/db/h2/ofonedb user=SA
2025-06-18T10:50:33.785+07:00  INFO 5667 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-06-18T10:50:33.790+07:00  INFO 5667 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-06-18T10:50:33.791+07:00  INFO 5667 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection conn1: url=jdbc:h2:/Users/<USER>/nez/code/datatp/working/release-dev/server/../server-data/data/db/h2/ofonedb user=SA
2025-06-18T10:50:33.791+07:00  INFO 5667 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-06-18T10:50:33.792+07:00  WARN 5667 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-3 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-06-18T10:50:33.792+07:00  INFO 5667 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-3 - Starting...
2025-06-18T10:50:33.797+07:00  WARN 5667 --- [main] org.postgresql.util.PGPropertyUtil       : JDBC URL invalid port number: ${spring.datasource.server.port}
2025-06-18T10:50:33.797+07:00 ERROR 5667 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-4 - dataSource or dataSourceClassName or jdbcUrl is required.
2025-06-18T10:50:33.797+07:00  INFO 5667 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at 'jdbc:h2:/Users/<USER>/nez/code/datatp/working/release-dev/server/../server-data/data/db/h2/ofonedb', 'jdbc:h2:/Users/<USER>/nez/code/datatp/working/release-dev/server/../server-data/data/db/h2/ofonedb'
2025-06-18T10:50:33.868+07:00  INFO 5667 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-06-18T10:50:33.871+07:00  INFO 5667 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@434896b0{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.14540602600227553844/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@56aacc7b{STARTED}}
2025-06-18T10:50:33.872+07:00  INFO 5667 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@434896b0{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.14540602600227553844/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@56aacc7b{STARTED}}
2025-06-18T10:50:33.873+07:00  INFO 5667 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@1f9af742{STARTING}[12.0.15,sto=0] @2524ms
2025-06-18T10:50:34.078+07:00  INFO 5667 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages: 
 [ "net.datatp.module.project", "cloud.datatp.fforwarder.mgmt", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "cloud.datatp.fforwarder.price", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "cloud.datatp.tms", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "cloud.datatp.fforwarder.sales", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.document", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-06-18T10:50:34.084+07:00  INFO 5667 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.H2Dialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-06-18T10:50:34.108+07:00  INFO 5667 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-18T10:50:34.137+07:00  INFO 5667 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-18T10:50:34.151+07:00  INFO 5667 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-18T10:50:34.357+07:00  INFO 5667 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-18T10:50:34.379+07:00  WARN 5667 --- [main] org.hibernate.orm.deprecation            : HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-18T10:50:37.553+07:00  INFO 5667 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-18T10:50:37.561+07:00  INFO 5667 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@3af87e28] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-06-18T10:50:37.802+07:00  INFO 5667 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-18T10:50:37.839+07:00  INFO 5667 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-06-18T10:50:37.844+07:00  INFO 5667 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-06-18T10:50:37.844+07:00  INFO 5667 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-06-18T10:50:37.850+07:00  WARN 5667 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-06-18T10:50:37.986+07:00  INFO 5667 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-18T10:50:38.429+07:00  INFO 5667 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-06-18T10:50:38.432+07:00  INFO 5667 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-06-18T10:50:38.465+07:00  INFO 5667 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-06-18T10:50:38.501+07:00  INFO 5667 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-06-18T10:50:38.522+07:00  INFO 5667 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-06-18T10:50:38.574+07:00  WARN 5667 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Attempting to allocate 100.0MB of offheap when there is only 95.2MB of free physical memory - some paging will therefore occur.
2025-06-18T10:50:38.575+07:00  INFO 5667 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-06-18T10:50:38.614+07:00  INFO 5667 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-06-18T10:50:38.617+07:00  WARN 5667 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Attempting to allocate 100.0MB of offheap when there is only 88.5MB of free physical memory - some paging will therefore occur.
2025-06-18T10:50:38.617+07:00  INFO 5667 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-06-18T10:50:38.630+07:00  INFO 5667 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-06-18T10:50:38.642+07:00  INFO 5667 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-06-18T10:50:38.643+07:00  INFO 5667 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-06-18T10:50:41.299+07:00  INFO 5667 --- [main] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 18/06/2025@10:45:00+0700 to 18/06/2025@11:00:00+0700
2025-06-18T10:50:41.299+07:00  INFO 5667 --- [main] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 18/06/2025@10:45:00+0700 to 18/06/2025@11:00:00+0700
2025-06-18T10:50:42.080+07:00  INFO 5667 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-06-18T10:50:42.080+07:00  INFO 5667 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-06-18T10:50:42.080+07:00  INFO 5667 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-06-18T10:50:43.762+07:00  WARN 5667 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 2e8fec6f-3c85-4aad-a450-cc5e8b2ca77b

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-18T10:50:43.766+07:00  INFO 5667 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-18T10:50:44.089+07:00  INFO 5667 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-06-18T10:50:44.089+07:00  INFO 5667 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-06-18T10:50:44.089+07:00  INFO 5667 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-06-18T10:50:44.089+07:00  INFO 5667 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-06-18T10:50:44.089+07:00  INFO 5667 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-06-18T10:50:44.089+07:00  INFO 5667 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-06-18T10:50:44.089+07:00  INFO 5667 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-06-18T10:50:44.089+07:00  INFO 5667 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-06-18T10:50:44.089+07:00  INFO 5667 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-06-18T10:50:44.089+07:00  INFO 5667 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-06-18T10:50:44.089+07:00  INFO 5667 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-06-18T10:50:44.090+07:00  INFO 5667 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-06-18T10:50:44.090+07:00  INFO 5667 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-06-18T10:50:44.090+07:00  INFO 5667 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-06-18T10:50:44.090+07:00  INFO 5667 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-06-18T10:50:44.100+07:00  INFO 5667 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-18T10:50:44.100+07:00  INFO 5667 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-18T10:50:44.101+07:00  INFO 5667 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-06-18T10:50:44.115+07:00  INFO 5667 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@450d8839{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-06-18T10:50:44.116+07:00  INFO 5667 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-06-18T10:50:44.117+07:00  INFO 5667 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-06-18T10:50:44.120+07:00  INFO 5667 --- [main] net.datatp.server.DataInitService        : Start initializing the default company
2025-06-18T10:50:44.123+07:00  INFO 5667 --- [main] net.datatp.module.company.CompanyLogic   : Create company My Company
2025-06-18T10:50:44.123+07:00  INFO 5667 --- [main] net.datatp.module.company.CompanyLogic   : Save company My Company
2025-06-18T10:50:44.592+07:00  INFO 5667 --- [main] n.d.module.company.CompanyDbInitializer  : Create company default
2025-06-18T10:50:44.594+07:00  INFO 5667 --- [main] n.d.module.company.CompanyDbInitializer  : Create app user/my-space
2025-06-18T10:50:44.595+07:00  INFO 5667 --- [main] n.d.module.company.CompanyDbInitializer  : Create app partner/partner-space
2025-06-18T10:50:44.595+07:00  INFO 5667 --- [main] n.d.module.company.CompanyDbInitializer  : Create app admin/admin
2025-06-18T10:50:44.596+07:00  INFO 5667 --- [main] n.d.module.company.CompanyDbInitializer  : Create app company/company
2025-06-18T10:50:44.596+07:00  INFO 5667 --- [main] n.d.module.company.CompanyDbInitializer  : Create app company/user-asset
2025-06-18T10:50:44.597+07:00  INFO 5667 --- [main] n.d.module.company.CompanyDbInitializer  : Create app company/company-asset
2025-06-18T10:50:44.598+07:00  INFO 5667 --- [main] n.d.module.company.CompanyDbInitializer  : Create app document/user-document-ie
2025-06-18T10:50:44.598+07:00  INFO 5667 --- [main] n.d.module.company.CompanyDbInitializer  : Create app document/partner-document-ie
2025-06-18T10:50:44.599+07:00  INFO 5667 --- [main] n.d.module.company.CompanyDbInitializer  : Create app document/company-document-ie
2025-06-18T10:50:44.599+07:00  INFO 5667 --- [main] n.d.module.company.CompanyDbInitializer  : Create app sample/reactjs-lib
2025-06-18T10:50:44.601+07:00  INFO 5667 --- [main] n.d.module.company.CompanyDbInitializer  : The account admin is already created
2025-06-18T10:50:44.611+07:00  INFO 5667 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-06-18T10:50:44.611+07:00  INFO 5667 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-06-18T10:50:44.617+07:00  INFO 5667 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.11 seconds (process running for 13.268)
2025-06-18T10:51:05.156+07:00  INFO 5667 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-06-18T10:51:47.238+07:00  INFO 5667 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-06-18T10:51:47.252+07:00  INFO 5667 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-06-18T10:52:06.290+07:00  INFO 5667 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-06-18T10:53:04.407+07:00  INFO 5667 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-06-18T10:53:51.491+07:00  INFO 5667 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-06-18T10:53:51.494+07:00  INFO 5667 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-06-18T10:54:06.521+07:00  INFO 5667 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-06-18T10:55:03.621+07:00  INFO 5667 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-06-18T10:55:03.625+07:00  INFO 5667 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-06-18T10:55:50.710+07:00  INFO 5667 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-06-18T10:55:50.712+07:00  INFO 5667 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-06-18T10:56:06.747+07:00  INFO 5667 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-06-18T10:57:02.823+07:00  INFO 5667 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-06-18T10:57:50.921+07:00  INFO 5667 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-06-18T10:57:50.922+07:00  INFO 5667 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-06-18T10:58:05.979+07:00  INFO 5667 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-06-18T10:59:02.134+07:00  INFO 5667 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-06-18T10:59:51.274+07:00  INFO 5667 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-06-18T10:59:51.276+07:00  INFO 5667 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-06-18T11:00:05.326+07:00  INFO 5667 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-06-18T11:00:05.326+07:00  INFO 5667 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-06-18T11:00:05.327+07:00  INFO 5667 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-06-18T11:00:05.328+07:00  INFO 5667 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-06-18T11:00:05.329+07:00  INFO 5667 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 18/06/2025@11:00:05+0700
2025-06-18T11:00:05.348+07:00  INFO 5667 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 18/06/2025@11:00:00+0700 to 18/06/2025@11:15:00+0700
2025-06-18T11:00:05.348+07:00  INFO 5667 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 18/06/2025@11:00:00+0700 to 18/06/2025@11:15:00+0700
2025-06-18T11:01:06.514+07:00  INFO 5667 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-06-18T11:01:50.665+07:00  INFO 5667 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-06-18T11:01:50.667+07:00  INFO 5667 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-06-18T11:02:01.317+07:00  INFO 5667 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@450d8839{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-06-18T11:02:01.319+07:00  INFO 5667 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-06-18T11:02:01.319+07:00  INFO 5667 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-06-18T11:02:01.319+07:00  INFO 5667 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-06-18T11:02:01.319+07:00  INFO 5667 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-06-18T11:02:04.974+07:00  INFO 5667 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-06-18T11:02:04.974+07:00  INFO 5667 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-06-18T11:02:04.974+07:00  INFO 5667 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-06-18T11:02:04.974+07:00  INFO 5667 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-06-18T11:02:04.974+07:00  INFO 5667 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-06-18T11:02:04.974+07:00  INFO 5667 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-06-18T11:02:04.974+07:00  INFO 5667 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-06-18T11:02:04.974+07:00  INFO 5667 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-06-18T11:02:04.974+07:00  INFO 5667 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-06-18T11:02:04.974+07:00  INFO 5667 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-06-18T11:02:04.974+07:00  INFO 5667 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-06-18T11:02:04.974+07:00  INFO 5667 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-06-18T11:02:04.983+07:00  INFO 5667 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-06-18T11:02:05.086+07:00  INFO 5667 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-06-18T11:02:05.090+07:00  INFO 5667 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-06-18T11:02:05.114+07:00  INFO 5667 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-18T11:02:05.116+07:00  INFO 5667 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-06-18T11:02:05.129+07:00  INFO 5667 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-06-18T11:02:05.129+07:00  INFO 5667 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-06-18T11:02:05.129+07:00  INFO 5667 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-06-18T11:02:05.130+07:00  INFO 5667 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@1f9af742{STOPPING}[12.0.15,sto=0]
2025-06-18T11:02:05.132+07:00  INFO 5667 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-18T11:02:05.132+07:00  INFO 5667 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@434896b0{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.14540602600227553844/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@56aacc7b{STOPPED}}
